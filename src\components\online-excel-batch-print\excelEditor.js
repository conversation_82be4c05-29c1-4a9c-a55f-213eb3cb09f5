import '../../../public/jquery.mousewheel.min.js';
import LuckySheet from '@cxist/luckysheet/dist/luckysheet.esm.js';
import '@cxist/luckysheet/dist/css/luckysheet.css';
import '@cxist/luckysheet/dist/plugins/css/pluginsCss.css';
import '@cxist/luckysheet/dist/assets/iconfont/iconfont.css';

import LuckyExcel from 'luckyexcel';
import { read } from 'xlsx/xlsx.esm.mjs';
import { reportTypes } from '@/data/industryTerm';
import { getIdByName, getNameByid } from '@/utils/common';

class ExcelEditor {
  static columeHeaderWordIndex = {
    A: 0,
    B: 1,
    C: 2,
    D: 3,
    E: 4,
    F: 5,
    G: 6,
    H: 7,
    I: 8,
    J: 9,
    K: 10,
    L: 11,
    M: 12,
    N: 13,
    O: 14,
    P: 15,
    Q: 16,
    R: 17,
    S: 18,
    T: 19,
    U: 20,
    V: 21,
    W: 22,
    X: 23,
    Y: 24,
    Z: 25
  };

  constructor(options = {}) {
    this.luckyExcelJson = null;
    this.xlsxWorkbook = null;
    this.LuckyExcelOptions = {
      container: 'luckysheet', // luckysheet is the container id
      showinfobar: false,
      lang: 'zh',
      showtoolbarConfig: {
        postil: false, // 批注
        print: false, // 打印
        chart: false, // 图表
        pivotTable: false, // 数据透视表
        screenshot: false, // 截图
        function: false, // 公式
        frozenMode: false, // 冻结方式
        sortAndFilter: false, // 排序和筛选
        conditionalFormat: false, // 条件格式
        dataVerification: false, // 数据验证
        protection: false // 工作表保护
      },
      // plugins: [LuckySheetPlugin],
      ...options
    };
    this.screenshots = [];
  }

  createExcel() {
    LuckySheet.create(this.LuckyExcelOptions);
  }

  loadExcel(url) {
    return new Promise(async resolve => {
      const file = await fetch(url).then(response => response.blob());
      const fileBuffer = await file.arrayBuffer();
      const workbook = read(fileBuffer, { type: 'buffer' });
      this.xlsxWorkbook = workbook;
      LuckyExcel.transformExcelToLucky(file, (luckyExcelJson, luckysheetfile) => {
        // LuckyExcel.transformExcelToLuckyByUrl(url, 'abc', (luckyExcelJson, luckysheetfile) => {
        if (luckyExcelJson.sheets === null || luckyExcelJson.sheets.length === 0) {
          console.error('Failed to read the content of the excel file', luckyExcelJson);
          return;
        }

        this.luckyExcelJson = luckyExcelJson;

        LuckySheet.destroy();
        LuckySheet.create({
          ...this.LuckyExcelOptions,
          data: luckyExcelJson.sheets
        });
        LuckySheet.hideGridLines();
        LuckySheet.setRangeShow('A9999', { show: false });
        resolve();
      });
    });
  }

  destroyExcel() {
    LuckySheet.destroy();
  }

  getValuesByDefinedName() {
    const workbook = this.xlsxWorkbook;
    const luckyExcelSheets = LuckySheet.getAllSheets();
    const values = workbook.Workbook.Sheets.reduce((accumulator, sheet) => {
      if (sheet.Hidden === 0) {
        accumulator[sheet.name] = [];
      }
      return accumulator;
    }, {});
    // const notFoundCells = []
    workbook.Workbook.Names.forEach(item => {
      const definedName = item.Name;
      const keys = item.Ref.replaceAll("'", '').split(/!?\$/);
      if (keys && keys.length === 3) {
        const [sheetName, column, row] = keys;
        if (values[sheetName]) {
          const columnIndex = ExcelEditor.columeHeaderWordIndex[column];
          const rowIndex = Number(row) - 1;
          const sheetJson = luckyExcelSheets.find(sheet => sheet.name === sheetName);
          const locatedCell = sheetJson.data?.[rowIndex]?.[columnIndex];
          if (locatedCell?.mc?.c === columnIndex && locatedCell?.mc?.r === rowIndex) {
            values[sheetName].push({
              definedName,
              column,
              columnIndex,
              row,
              rowIndex,
              cell: locatedCell,
              rawValue:
                locatedCell.v !== undefined
                  ? locatedCell.v
                  : locatedCell.ct?.s
                  ? locatedCell.ct.s.map(textItem => textItem.v).join('')
                  : '',
              formattedText: locatedCell.m || ''
            });
          } else if (!locatedCell.mc && columnIndex > -1 && rowIndex > -1) {
            values[sheetName].push({
              definedName,
              column,
              columnIndex,
              row,
              rowIndex,
              cell: locatedCell,
              rawValue: locatedCell.v || '',
              formattedText: locatedCell.m || ''
            });
          }
        }
      } else {
        return new Error('failed to parse workbook ref', keys, item.Ref);
      }
    });
    return values;
  }

  setValuesByDefinedName(data) {
    const workbook = this.xlsxWorkbook;
    const values = workbook.Workbook.Sheets.reduce((accumulator, sheet) => {
      if (sheet.Hidden === 0) {
        accumulator[sheet.name] = [];
      }
      return accumulator;
    }, {});
    workbook.Workbook.Names.forEach(item => {
      const definedName = item.Name;
      console.log(definedName);
      const keys = item.Ref.replaceAll("'", '').split(/!?\$/);
      if (keys && keys.length === 3) {
        const [sheetName, column, row] = keys;
        if (values[sheetName]) {
          const columnIndex = ExcelEditor.columeHeaderWordIndex[column];
          const rowIndex = Number(row) - 1;
          if (definedName.startsWith('img')) {
            console.log('插入图片', definedName, definedName, data[definedName]);
            this.insertImage(rowIndex, columnIndex, this.getDataValue(definedName, data));
          } else {
            console.log('插入数据', definedName, data[definedName]);
            LuckySheet.setCellValue(rowIndex, columnIndex, this.getDataValue(definedName, data));
          }
        }
      } else {
        return new Error('failed to parse workbook ref', keys, item.Ref);
      }
    });
  }

  getDataValue(field, data) {
    if (data[field] !== undefined && data[field] !== '') {
      return data[field];
    }
    return '';
  }

  insertImage(rowIndex, columnIndex, src) {
    if (src.startsWith('http')) {
      console.log('插入图片http');
      LuckySheet.insertImage(src, {
        rowIndex: rowIndex,
        colIndex: columnIndex
      });
      return;
    }

    const img = new Image();
    img.src = src;
    img.width = 80;
    img.height = 80;

    const $this = this;

    img.onload = function () {
      console.log('插入图片base64');
      const { pixelWidth, pixelHeight } = $this.getCellSize(rowIndex, columnIndex);
      console.log('图片所在单元格宽高', pixelWidth, pixelHeight);

      LuckySheet.getConfig().columnlen[columnIndex];
      LuckySheet.getConfig().rowlen[rowIndex];

      // 获取当前活动工作表的数据对象
      const config = LuckySheet.getluckysheetfile()[0];
      const zoomRatio = config.zoomRatio; // 获取当前缩放比例
      console.log('当前工作表缩放比例:', zoomRatio);

      const canvas = document.createElement('canvas');
      canvas.width = pixelWidth * zoomRatio;
      canvas.height = pixelHeight * zoomRatio;
      const context = canvas.getContext('2d');
      context.drawImage(img, 0, 0, canvas.width, canvas.height);
      const dataUrl = canvas.toDataURL('image/png');

      LuckySheet.clearCell(rowIndex, columnIndex);

      LuckySheet.insertImage(dataUrl, {
        rowIndex: rowIndex,
        colIndex: columnIndex,
        width: 10,
        height: 10
      });
    };
  }

  getCellSize(row, col) {
    console.log('获取单元格尺寸', row, col);
    const sheet = LuckySheet.getSheet();
    const mergeKey = `${row}_${col}`;
    const mergeData = sheet.config.merge?.[mergeKey];

    if (mergeData) {
      // 合并单元格尺寸
      const { r, c, rs, cs } = mergeData;
      return {
        isMerged: true,
        rows: rs,
        cols: cs,
        pixelWidth: this.sumColumnWidths(c, c + cs - 1),
        pixelHeight: this.sumRowHeights(r, r + rs - 1)
      };
    } else {
      // 普通单元格尺寸
      return {
        isMerged: false,
        rows: 1,
        cols: 1,
        pixelWidth: LuckySheet.getColumnWidth(col),
        pixelHeight: LuckySheet.getRowHeight(row)
      };
    }
  }

  // 辅助函数：计算多列总宽度
  sumColumnWidths(startCol, endCol) {
    console.log('sumColumnWidths', startCol, endCol);
    let total = 0;
    for (let c = startCol; c <= endCol; c++) {
      total += LuckySheet.getColumnWidth([c])[c];
    }
    return total;
  }

  // 辅助函数：计算多行总高度
  sumRowHeights(startRow, endRow) {
    console.log('sumRowHeights', startRow, endRow);
    let total = 0;
    for (let r = startRow; r <= endRow; r++) {
      total += LuckySheet.getRowHeight([r])[r];
    }
    return total;
  }

  getScreenshots(requiredColumns = 3, requiredLeadingCols = 3) {
    console.log('获取截图');
    this.screenshots = [];

    const currentSheet = LuckySheet.getSheet();
    if (currentSheet && currentSheet.images) {
      console.log('currentSheet.images', JSON.parse(JSON.stringify(currentSheet.images)));
      for (const imageKey in currentSheet.images) {
        const imageItem = currentSheet.images[imageKey];
        console.log('currentSheet.images.imageItem', imageKey, imageItem);
        this.screenshots.push({ ...imageItem.default, src: imageItem.src });
      }
    }

    console.log('currentSheet.data.length', currentSheet.data.length);
    const { maxRow, maxColumn } = this.calculateDataRange(currentSheet.data);
    console.log('setRangeShow', maxRow, maxColumn);
    LuckySheet.setRangeShow({ row: [0, maxRow], column: [0, maxColumn] });
    const screenshot = LuckySheet.getScreenshot();
    this.screenshots.unshift({ top: 0, left: 0, src: screenshot });
    return this.screenshots;
  }

  /**
   * 计算表格数据的有效范围（最大行和最大列）
   * @param {Array<Array>} sheetData - 二维数组表格数据
   * @param {number} emptyColThreshold - 连续空列判定阈值（默认3列）
   * @param {number} requiredLeadingCols - 行有效性检查列数（默认前3列）
   * @returns {Object} { maxRow, maxColumn } 有效数据的最大行和列
   */
  calculateDataRange(sheetData, emptyColThreshold = 3, requiredLeadingCols = 3) {
    let maxRow = 0;
    let maxColumn = 0;

    if (!Array.isArray(sheetData)) {
      return { maxRow, maxColumn };
    }

    for (let rowIndex = 0; rowIndex < sheetData.length; rowIndex++) {
      const row = sheetData[rowIndex];
      if (!Array.isArray(row)) continue;

      // 计算当前行的有效列数
      let rowMaxCol = 0;
      let emptyColCount = 0;
      for (let colIndex = 0; colIndex < row.length; colIndex++) {
        if (row[colIndex] == null || row[colIndex] === '') {
          emptyColCount++;
          if (emptyColCount >= emptyColThreshold) {
            rowMaxCol = Math.max(0, colIndex - emptyColThreshold);
            break;
          }
        } else {
          emptyColCount = 0;
          rowMaxCol = colIndex;
        }
      }
      maxColumn = Math.max(maxColumn, rowMaxCol);

      // 检查行有效性：前 requiredLeadingCols 列是否至少有一列有值
      const isRowValid = row.slice(0, requiredLeadingCols).some(cell => cell != null && cell !== '');

      // 动态更新 maxRow（不因空行中断）
      if (isRowValid) {
        maxRow = rowIndex;
      }
    }

    return {
      maxRow: Math.min(maxRow, sheetData.length - 1),
      maxColumn: Math.min(maxColumn, sheetData[0]?.length - 1 ?? 0)
    };
  }

  checkRowData() {}

  processDefinedNameForDetailData(excelValues, defaultDetailData) {
    if (!excelValues) {
      return;
    }
    const newData = { ...defaultDetailData };
    let suppDateYear = '';
    let suppDateMonth = '';
    let suppDateDay = '';
    for (let index = 0; index < excelValues.length; index++) {
      const cellItem = excelValues[index];
      if (cellItem.rawValue === '') continue;
      switch (cellItem.definedName) {
        case 'reportType': {
          const reportTypeItem = reportTypes.find(item => item.name === cellItem.rawValue);
          if (reportTypeItem) {
            newData.reportType = reportTypeItem.code;
          } else {
            newData.reportType = '';
          }
          break;
        }
        case 'inspector': {
          const person = getIdByName(cellItem.rawValue);
          if (person) {
            newData.inspector = person;
          } else {
            newData.inspector = '';
          }
          break;
        }
        case 'suppDateYear': {
          const year = Number(cellItem.rawValue);
          if (year && year >= 1900 && year <= 9999) {
            suppDateYear = year;
          }
          break;
        }
        case 'suppDateMonth': {
          const month = Number(cellItem.rawValue);
          if (month && month >= 1 && month <= 12) {
            suppDateMonth = month;
          }
          break;
        }
        case 'suppDateDay': {
          const day = Number(cellItem.rawValue);
          if (day && day >= 1 && day <= 31) {
            suppDateDay = day;
          }
          break;
        }
        case 'startMeter': {
          newData.startMeter = cellItem.rawValue;
          break;
        }
        case 'endMeter': {
          newData.endMeter = cellItem.rawValue;
          break;
        }
        case 'segment': {
          newData.segment = cellItem.rawValue;
          break;
        }
        case 'judgmentName': {
          newData.judgmentName = cellItem.rawValue;
          break;
        }
        case 'startDate': {
          newData.startDate = cellItem.rawValue;
          break;
        }
        case 'grossWeight': {
          newData.grossWeight = cellItem.rawValue;
          break;
        }
      }
    }
    if (suppDateYear && suppDateMonth && suppDateDay) {
      newData.suppDate =
        suppDateYear + (suppDateMonth ? '-' + suppDateMonth : '') + (suppDateDay ? '-' + suppDateDay : '');
    }
    newData.printStatus = 1;
    return newData;
  }

  processDetailDataForDefinedName(data) {
    const newData = { ...data };
    if (newData.reportType) {
      const reportTypeItem = reportTypes.find(item => item.code === Number(data.reportType));
      if (reportTypeItem) {
        newData.reportType = reportTypeItem.name;
      } else {
        newData.reportType = '';
      }
    }
    if (newData.inspector) {
      newData.inspector = getNameByid(newData.inspector) || '';
    }
    if (newData.suppDate) {
      const [year, month, day] = newData.suppDate.split('-');
      if (Number(year)) {
        newData.suppDateYear = year;
      }
      if (Number(month)) {
        newData.suppDateMonth = month;
      }
      if (Number(day)) {
        newData.suppDateDay = day;
      }
      newData.suppDate;
    }
    return newData;
  }
}

export default ExcelEditor;
