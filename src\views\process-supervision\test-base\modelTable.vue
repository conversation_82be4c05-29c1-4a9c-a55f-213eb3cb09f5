<template>
  <div v-if="isShow" class="modelTable">
    <!-- 型号的表格 -->
    <el-table
      ref="tableRef"
      v-loading="tableLoading"
      :data="tableData"
      fit
      border
      height="auto"
      size="medium"
      highlight-current-row
      class="dark-table format-height-table base-table format-height-table2"
      @header-dragend="drageHeader"
    >
      <el-table-column label="序号" width="70px" align="center">
        <template #default="{ $index }">
          {{ $index + 1 }}
        </template>
      </el-table-column>
      <el-table-column label="型号" prop="productModel" min-width="120px" sortable>
        <template #default="{ row }">
          <span>{{ row.productModel }}</span>
        </template>
      </el-table-column>
      <el-table-column label="型号名称" prop="name" min-width="220px">
        <template #default="{ row }">
          {{ row.name }}
        </template>
      </el-table-column>
      <el-table-column label="检测依据" prop="testBasis" min-width="220px">
        <template #default="{ row }">
          {{ row.testBasis || '--' }}
        </template>
      </el-table-column>
    </el-table>
    <!-- <pagination :page="listQuery.page" :limit="listQuery.limit" :total="total" @pagination="getTableList" /> -->
  </div>
</template>

<script>
import { reactive, toRefs, ref, watch } from 'vue';
// import { saveProduct, copyProduct, updateProduct } from '@/api/testBase'
import { drageHeader } from '@/utils/formatTable';
export default {
  name: 'ModelTable',
  props: {
    isShow: {
      type: Boolean,
      default: false
    },
    tableData: {
      type: Array,
      default: function () {
        return [];
      }
    },
    dialogTitle: {
      type: String,
      default: ''
    },
    detailData: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['closeDialog'],
  setup(props, context) {
    // const { proxy } = getCurrentInstance()
    watch(props, newValue => {
      state.isShow = props.isShow;
      if (state.isShow) {
        state.tableData = props.tableData;
      }
    });
    const state = reactive({
      tableData: [],
      tableRef: ref(),
      tableLoading: false,
      isShow: false
    });
    return {
      ...toRefs(state),
      drageHeader
    };
  },
  computed: {},
  created() {}
};
</script>

<style lang="scss" scoped>
.modelTable {
  margin-top: 10px;
}
:deep(.el-table.format-height-table2) {
  .el-table__body-wrapper {
    max-height: calc(100vh - 260px);
    overflow-y: auto;
    overflow-x: hidden;
  }
}
</style>
