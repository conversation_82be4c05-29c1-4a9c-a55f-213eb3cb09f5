<template>
  <!-- 检验申请列表 -->
  <ListLayout>
    <template #search-bar>
      <el-form ref="editFrom" :inline="true" :model="formInline" class="page-searchbar" @submit.prevent>
        <el-form-item prop="param">
          <div style="width: 42vw">
            <CombinationQuery
              :key="queryKey"
              :field-list="tableColumns.concat(applicationCombinedFieldList)"
              field-tip="申请单号/检验对象/对象位置/对象名称"
              @get-query-info="getQueryInfo"
              @reset-search="reset"
            />
          </div>
        </el-form-item>
        <el-form-item style="margin-left: 0px">
          <el-button size="large" type="text" class="searchBtn" @click="search" @keyup.prevent @keydown.enter.prevent
            >高级搜索<i class="el-icon--right" :class="[showS ? 'el-icon-arrow-up' : 'el-icon-arrow-down']"
          /></el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #button-group>
      <el-button
        v-if="getPermissionBtn('ERPInspectionBtn')"
        size="large"
        icon="el-icon-download"
        :loading="erpSuccessLoading"
        @click="getERPData"
        @keyup.prevent
        @keydown.enter.prevent
        >获取ERP数据</el-button
      >
      <el-button
        v-if="getPermissionBtn('copyInspectionBtn')"
        size="large"
        type="primary"
        icon="el-icon-document"
        @click="handleCopyInspection()"
        @keyup.prevent
        @keydown.enter.prevent
        >复制检验单</el-button
      >
      <el-button
        v-if="getPermissionBtn('addInspectionBtn')"
        size="large"
        type="primary"
        icon="el-icon-plus"
        @click="addIA"
        @keyup.prevent
        @keydown.enter.prevent
        >新增检验单</el-button
      >
    </template>
    <template #search-panel>
      <el-collapse v-model="activeName" class="search-collapse">
        <el-collapse-item name="1">
          <el-form ref="form" :model="searchForm" label-width="110px" label-position="right">
            <el-form-item label="登记日期：">
              <el-date-picker
                v-model="searchForm.bzDateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                size="small"
                :shortcuts="shortcuts"
                @change="changeBZTime"
              />
            </el-form-item>
            <el-form-item label="登记人：">
              <el-select
                v-model="searchForm.registerUserId"
                class="owner-select"
                placeholder="选择登记人"
                size="small"
                clearable
                filterable
                :filter-method="filterUserList"
                @focus="filterUserList(null)"
                @change="changeUser"
              >
                <el-option v-for="item in userOptions" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>
            <el-form-item label="来源：">
              <el-select v-model="formInline.thirdType" placeholder="请选择" clearable size="small">
                <el-option v-for="item in thirdTypeOptions" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-form>
        </el-collapse-item>
      </el-collapse>
    </template>
    <template #radio-content>
      <el-row>
        <el-col :span="16" class="flex gap-2">
          <el-radio-group v-model="radioData" size="small" @change="changeRadio">
            <el-radio-button label="全部" />
            <el-radio-button label="待提交" />
            <el-radio-button label="已提交" />
            <el-radio-button label="已作废" />
          </el-radio-group>
          <el-select
            v-model="searchForm.type"
            class="owner-select"
            filterable
            placeholder="请选择检验类型"
            size="small"
            clearable
            @change="changeType"
          >
            <el-option v-for="(val, key) in dictionaryAll['JYLX'].enable" :key="key" :label="val" :value="key" />
          </el-select>
        </el-col>
        <el-col :span="8" style="text-align: right">
          <TableColumnView binding-menu="InspectionApplication" @columns="onUpdateColumns" />
        </el-col>
      </el-row>
    </template>
    <el-table
      ref="tableRef"
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      fit
      border
      height="auto"
      size="medium"
      class="dark-table base-table format-height-table"
      :row-style="
        () => {
          return 'cursor: pointer';
        }
      "
      highlight-current-row
      @current-change="changeSelectInspection"
      @header-dragend="drageHeader"
      @sort-change="sortChange"
    >
      <el-table-column type="index" label="选择" width="70" align="center">
        <template #default="{ row }">
          <el-radio v-model="row.radio" :label="row.id" @change="changeSelectInspection(row)">{{ '' }}</el-radio>
        </template>
      </el-table-column>
      <template v-for="item in tableColumns" :key="item.fieldKey">
        <el-table-column
          :prop="item.fieldKey"
          :label="item.fieldName"
          :sortable="Number(item.isSortable) === 1"
          :width="item.isMinWidth ? '' : item.columnWidth"
          :min-width="item.isMinWidth ? item.columnWidth : ''"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <template v-if="item.fieldType === fieldTypesEnum.Link">
              <span v-if="row[item.fieldKey]" v-copy="row.no" class="nowrap blue-color" @click.stop="iaDetail(row)">{{
                row.no || '--'
              }}</span>
            </template>
            <template v-else-if="item.fieldType === fieldTypesEnum.Person">
              <UserTag :name="getNameByid(row[item.fieldKey]) || row[item.fieldKey] || '--'" />
            </template>
            <template v-else-if="item.fieldType === fieldTypesEnum.Status">
              <span v-if="item.fieldKey === 'type'">{{ dictionaryAll['JYLX'].all[row[item.fieldKey]] || '--' }}</span>
              <el-tag
                v-else-if="item.fieldKey === 'status'"
                size="small"
                effect="dark"
                :type="handleTag(item.fieldKey, row[item.fieldKey])[0]"
              >
                {{ handleTag(item.fieldKey, row[item.fieldKey])[1] }}
              </el-tag>
            </template>
            <template v-else-if="item.fieldType === fieldTypesEnum.Date">
              <span>{{ formatDate(row[item.fieldKey]) || '--' }}</span>
            </template>
            <template v-else-if="item.fieldType === fieldTypesEnum.Custom">
              <span v-if="item.fieldKey === 'thirdType'">{{
                handleEnum(item.fieldKey, row[item.fieldKey]) || '--'
              }}</span>
              <div v-else-if="item.fieldKey === 'inputWarehouseNo,productionOrderNo'" class="nowrap">
                <span>{{ row.type === 1 ? row.inputWarehouseNo || '--' : row.productionOrderNo || '--' }}</span>
              </div>
              <template v-else-if="item.fieldKey === 'productionProcedure,productionStation,wareHouseName'">
                <span>{{
                  row.type === 1
                    ? row.wareHouseName || '--'
                    : (row.productionProcedure || '--') + '-' + (row.productionStation || '--')
                }}</span>
              </template>
              <template v-else-if="item.fieldKey === 'customerName,supplierName'">
                <span>{{ row.type === 1 ? row.supplierName || '--' : row.customerName || '--' }}</span>
              </template>
            </template>
            <span v-else>{{ row[item.fieldKey] || '--' }}</span>
          </template>
        </el-table-column>
      </template>

      <el-table-column
        label="操作"
        :width="colWidth.operationMultiple"
        prop="caozuo"
        fixed="right"
        class-name="fixed-right"
      >
        <template #default="scope">
          <span class="blue-color" @click.stop="iaDetail(scope.row)">查看</span>
          <span
            v-if="scope.row.status === 0 && scope.row.isInvalidated === 0 && getPermissionBtn('sumitInspectionBtn')"
            class="blue-color"
            @click.stop="submitIA(scope.row)"
            >提交</span
          >
          <span
            v-if="scope.row.isInvalidated === 0 && getPermissionBtn('voiInspectionBtn')"
            class="blue-color"
            @click.stop="cancleIA(scope.row)"
            >作废</span
          >
          <span
            v-if="scope.row.isInvalidated === 1 && getPermissionBtn('voiInspectionBtn')"
            class="blue-color"
            @click.stop="handleRestore(scope.row)"
            >还原</span
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :page="listQuery.page"
      :limit="listQuery.limit"
      :total="total"
      @pagination="getList"
    />
    <template #other>
      <!-- 新增检验单 -->
      <add-info
        :show="showAddInfoDialog"
        :dictionary="dictionaryAll"
        :page-view="pageViewGroup"
        @close="closeAddInfo"
      />
      <!-- 获取ERP数据弹出框 -->
      <el-dialog
        v-model="erpDialog"
        title="获取ERP数据"
        custom-class="small-dialog"
        width="480px"
        :close-on-click-modal="false"
        @close="erpDialog = false"
      >
        <div class="dialog-main">
          <img src="@/assets/img/erp.png" alt="erp-img" />
          <el-form ref="submitERPRef" :model="erpFrom" :rules="erpRules" label-position="right">
            <el-form-item label="日期：" prop="start">
              <el-date-picker
                v-model="erpDateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                size="large"
                @change="changeERPTime(erpDateRange)"
              />
            </el-form-item>
          </el-form>
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="erpDialog = false">取 消</el-button>
            <el-button
              type="primary"
              :loading="erpSuccessLoading"
              @click="submitERPSuccess"
              @keyup.prevent
              @keydown.enter.prevent
              >确 定</el-button
            >
          </span>
        </template>
      </el-dialog>
      <!-- 复制检验单 -->
      <el-dialog
        v-model="dialogCopy"
        title="复制检验单"
        width="480px"
        :close-on-click-modal="false"
        @close="erpDialog = false"
      >
        <div v-loading="dialogCopyLoading">
          <el-form v-if="dialogCopy" ref="copyFormRef" :model="formDataCopy" :rules="rulesCopy" label-position="right">
            <el-form-item label="检验类型：" prop="type">
              <el-select
                v-model="formDataCopy.type"
                filterable
                placeholder="请选择检验类型"
                size="small"
                clearable
                style="width: 100%"
              >
                <el-option v-for="(val, key) in dictionaryAll['JYLX'].enable" :key="key" :label="val" :value="key" />
              </el-select>
            </el-form-item>
          </el-form>
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="dialogCopy = false">取 消</el-button>
            <el-button
              type="primary"
              :loading="dialogCopyLoading"
              @click="copyInspectionInfo"
              @keyup.prevent
              @keydown.enter.prevent
              >确 定</el-button
            >
          </span>
        </template>
      </el-dialog>
    </template>
  </ListLayout>
</template>

<script>
import { reactive, ref, toRefs, getCurrentInstance, computed, onMounted } from 'vue';
import router from '@/router/index.js';
// import { useRoute } from 'vue-router'
import Pagination from '@/components/Pagination';
import UserTag from '@/components/UserTag';
import {
  getInspectionList,
  cancelInspection,
  restoreInspection,
  submitInspection,
  copyInspection
} from '@/api/inspection-application';
import { getERP, getSyncToLims } from '@/api/login';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getNameByid, getPermissionBtn } from '@/utils/common';
import { formatDate } from '@/utils/formatTime';
import { useStore } from 'vuex';
import { drageHeader } from '@/utils/formatTable';
import { getLoginInfo } from '@/utils/auth';
import _ from 'lodash';
// import { reportAudit } from '@/api/permission'
import { addByTemp } from '@/api/messageAgent';
import { mapGetters } from 'vuex';
import AddInfo from './add-info.vue';
import ListLayout from '@/components/ListLayout';
import { colWidth } from '@/data/tableStyle';
import { getColWidth } from '@/utils/func/customTable';
import {
  applicationFieldList,
  handleEnum,
  handleTag,
  applicationCombinedFieldList
} from './func/inspectionApplicationInfo';
import CombinationQuery from '@/components/CombinationQuery';
import TableColumnView from '@/components/TableColumnView';
import { fieldTypesEnum, columnFixedTypesEnum } from '@/components/TableColumnView/enum';
import { useRoute } from 'vue-router';
import { getViewByBindingMenu } from '@/api/tableView';
import { formatViewData } from '@/utils/formatJson';
import { getDictionary } from '@/api/user';

export default {
  name: 'InspectionApplication',
  components: { Pagination, AddInfo, UserTag, ListLayout, CombinationQuery, TableColumnView },
  setup() {
    const { proxy } = getCurrentInstance();
    const store = useStore().state;
    const route = useRoute();
    // console.log(route.query)
    const editFrom = ref(null);
    const otherForm = reactive({
      currentAccountId: getLoginInfo().accountId,
      auditNameList: [],
      dialogCopy: false,
      dialogCopyLoading: false,
      activeName: '0',
      showS: false,
      type: 'info',
      formInline: {
        param: '',
        endTime: '',
        startTime: '',
        registerUserId: '',
        type: '',
        thirdType: '',
        tableQueryParamList: []
      },
      formDataCopy: {},
      copyFormRef: ref(),
      searchForm: {
        registerUserId: '',
        bzDateRange: ''
      },
      thirdTypeOptions: [
        { id: 0, name: 'ERP' },
        { id: 1, name: 'MES' },
        { id: 2, name: 'LIMS/自建' }
      ],
      types: store.user.materialList,
      userOptions: store.common.nameList,
      copyUserOptions: store.common.nameList,
      dictionaryAll: {
        JYLX: {
          enable: {},
          all: {}
        }
      },
      tableColumns: [],
      list: [],
      content: '',
      radioData: '全部',
      listQuery: {
        page: 1,
        limit: 20,
        orderBy: '',
        isAsc: ''
      },
      tableKey: 0,
      listLoading: false,
      total: 0,
      shortcuts: [
        {
          text: '近三天',
          value: (() => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 3);
            return [start, end];
          })()
        },
        {
          text: '最近一周',
          value: (() => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            return [start, end];
          })()
        },
        {
          text: '最近一个月',
          value: (() => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            return [start, end];
          })()
        }
      ],
      showAddReport: false,
      tableRef: ref(),
      erpDialog: false,
      erpFrom: {
        start: '',
        end: ''
      },
      erpDateRange: '',
      erpSuccessLoading: false,
      erpRules: {
        sampleSubmitTimeBegin: [{ required: true, message: '请选择日期范围' }]
      },
      rulesCopy: {
        type: [{ required: true, message: '请选择检验单' }]
      },
      showAddInfoDialog: false,
      selectedFieldList: [],
      queryKey: 0,
      currentInspection: null,
      pageViewGroup: {
        '1-basicInfo': {},
        '2-basicInfo': {},
        '3-basicInfo': {},
        '1-basicInfo-dialog': {},
        '2-basicInfo-dialog': {},
        '3-basicInfo-dialog': {}
      }
    });
    const getDetailView = async () => {
      const res = await getViewByBindingMenu('AddInspection');
      if (res) {
        otherForm.pageViewGroup = formatViewData(res.data.data[0].sysEmployeeListConfigList);
      }
    };
    getDetailView();
    // 查询
    function onSubmit() {
      proxy.getList();
    }
    // 重置
    function reset(val) {
      editFrom.value.resetFields();
      otherForm.formInline = {
        param: '',
        endTime: '',
        startTime: '',
        registerUserId: '',
        type: '',
        thirdType: '',
        tableQueryParamList: []
      };
      otherForm.radioData = '全部';
      otherForm.searchForm = {
        registerUserId: '',
        bzDateRange: ''
      };
      otherForm.listQuery = {
        page: 1,
        limit: 20,
        orderBy: '',
        isAsc: null
      };
      proxy.getList();
    }
    // 打开 高级搜索
    const search = () => {
      otherForm.showS = !otherForm.showS;
      if (otherForm.activeName === '0') {
        otherForm.activeName = '1';
      } else {
        otherForm.activeName = '0';
      }
    };
    // 排序
    const sortChange = data => {
      const { prop, order } = data;
      // console.log(prop)
      // console.log(order)
      otherForm.listQuery.orderBy = prop;
      if (order === 'ascending') {
        otherForm.listQuery.isAsc = true;
      } else if (order === 'descending') {
        otherForm.listQuery.isAsc = false;
      } else {
        otherForm.listQuery.isAsc = null;
      }
    };
    // 添加消息待办
    const addMsg = () => {
      // 添加消息待办
      const params = {
        eventCode: 'M012',
        receiverType: '1',
        senderName: getNameByid(otherForm.currentAccountId),
        receiverIds: '',
        receiverNames: '',
        c_ids: '',
        c_b_samplesIdArray: '',
        c_b_sampleNoArray: '',
        c_b_reportNoArray: ''
      };
      addByTemp(params).then(res => {
        if (res !== false) {
          // console.log(res.data)
        }
      });
    };
    // 获取ERP数据
    const getERPData = () => {
      // console.log('get ERP Data')
      otherForm.erpDialog = true;
    };
    // 选择获取ERP数据的时间段
    const changeERPTime = date => {
      otherForm.erpFrom.start = date ? formatDate(date[0]) : '';
      otherForm.erpFrom.end = date ? formatDate(date[1]) : '';
    };
    // ERP弹出框-点击确定
    const submitERPSuccess = () => {
      if (otherForm.erpFrom.start && otherForm.erpFrom.end) {
        otherForm.erpSuccessLoading = true;
        getERP(otherForm.erpFrom).then(res => {
          if (res !== false) {
            // console.log(res)
          }
          getSyncToLims().then(res1 => {
            // console.log(res1)
            if (res1 !== false) {
              // const data = res1.data
              ElMessage.success('数据同步lims成功！');
            }
          });
          otherForm.erpDialog = false;
          otherForm.erpSuccessLoading = false;
        });
      } else {
        ElMessage.error('请选择日期');
      }
    };
    // 新增
    const addIA = () => {
      // console.log(otherForm.tableRef)
      // router.push({ name: 'AddInspection', query: { id: 0, flag: 0 }})
      otherForm.showAddInfoDialog = true;
    };
    // 关闭-新增
    const closeAddIA = value => {};
    // 关闭-新增检验单弹出框
    const closeAddInfo = value => {
      otherForm.showAddInfoDialog = value;
    };

    // 点击编辑
    const editIA = row => {
      // console.log(row)
      router.push({ name: 'AddInspection', query: { id: row.id, flag: 2 } });
    };
    // 查看
    const iaDetail = row => {
      // console.log(row)
      setCurrentStatus();
      if (row.status === 0) {
        // 待提交状态可以编辑
        router.push({ name: 'AddInspection', query: { id: row.id, flag: 2 } });
      } else {
        // 非待提交，只能看详情
        router.push({ name: 'AddInspection', query: { id: row.id, flag: 1 } });
      }
    };

    // 点击提交
    const submitIA = row => {
      // console.log(row)
      if (row.list.length > 0) {
        ElMessageBox({
          title: '提示',
          message: '确认提交吗？提交后不可编辑',
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          showCancelButton: true,
          closeOnClickModal: true,
          type: 'warning'
        })
          .then(() => {
            submitInspection(row.id).then(res => {
              if (res !== false) {
                ElMessage.success('编号：' + row.no + '，提交成功');
                proxy.getList();
              }
            });
          })
          .catch(() => {});
      } else {
        ElMessage.warning('提交前请先添加样品');
      }
    };

    // 作废
    const cancleIA = row => {
      // console.log(row)
      ElMessageBox({
        title: '提示',
        message: '确定作废当前检验申请单吗？',
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        showCancelButton: true,
        closeOnClickModal: true,
        type: 'warning'
      })
        .then(() => {
          cancelInspection(row.id).then(res => {
            if (res !== false) {
              ElMessage.success('编号：' + row.no + '，已作废');
              proxy.getList();
            }
          });
        })
        .catch(() => {});
    };

    const handleRestore = row => {
      ElMessageBox({
        title: '提示',
        message: '确定还原当前检验申请单吗？',
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        showCancelButton: true,
        closeOnClickModal: true,
        type: 'warning'
      })
        .then(() => {
          restoreInspection(row.id).then(res => {
            if (res) {
              ElMessage.success('编号：' + row.no + '，已还原');
              proxy.getList();
            }
          });
        })
        .catch(() => {});
    };
    // 切换tab
    const changeRadio = value => {
      // console.log(value)
      const param = {
        全部: '',
        待提交: '0',
        已提交: '1',
        已作废: '2'
      };
      const isInvalidatedParam = {
        全部: '',
        待提交: '0',
        已提交: '0',
        已作废: '1'
      };
      // otherForm.formInline.examineStage = param[value]
      otherForm.formInline.isInvalidated = isInvalidatedParam[value];
      if (otherForm.formInline.isInvalidated === '1') {
        otherForm.formInline.status = '';
      } else {
        otherForm.formInline.status = param[value];
      }
      proxy.getList();
    };

    // 高级搜索-登记日期-change
    const changeBZTime = date => {
      otherForm.formInline.startTime = date ? formatDate(date[0]) : '';
      otherForm.formInline.endTime = date ? formatDate(date[1]) : '';
    };
    // 高级搜索-登记人-change
    const changeUser = id => {
      otherForm.formInline.registerUserId = id;
    };
    // 高级搜索-检验类型-change
    const changeType = type => {
      otherForm.formInline.type = type + '';
      proxy.getList();
    };
    // 过滤登记人
    const filterUserList = val => {
      if (val) {
        const list = [];
        otherForm.copyUserOptions.forEach(user => {
          const item = _.filter(user.strName, function (us) {
            return us.indexOf(val) !== -1;
          });
          if (user.name.indexOf(val) !== -1 || item.length > 0) {
            list.push(user);
          }
        });
        otherForm.userOptions = list;
      } else {
        otherForm.userOptions = otherForm.copyUserOptions;
      }
    };
    // 获取资源权限
    // reportAudit('recordAudit').then(res => {
    //   otherForm.auditNameList = res.data
    // })

    // #region 组合查询

    const getQueryInfo = info => {
      otherForm.formInline.param = info.param;
      otherForm.formInline.tableQueryParamList = info.tableQueryParamList;
      otherForm.selectedFieldList = info.selectedFieldList;
      proxy.getList();
    };

    // #endregion

    // #region 恢复页面状态

    function setCurrentStatus() {
      const currentState = {
        searchForm: otherForm.searchForm,
        radioData: otherForm.radioData,
        formInline: otherForm.formInline,
        listQuery: otherForm.listQuery,
        selectedFieldList: otherForm.selectedFieldList
      };
      sessionStorage.setItem(route.name, JSON.stringify(currentState));
    }

    function consumeCurrentStatus() {
      const currentSorage = sessionStorage.getItem(route.name);
      if (currentSorage) {
        const currentState = JSON.parse(currentSorage);
        otherForm.searchForm = currentState.searchForm;
        otherForm.radioData = currentState.radioData;
        otherForm.formInline = currentState.formInline;
        otherForm.listQuery = currentState.listQuery;
        otherForm.queryKey = otherForm.queryKey + 1;
      }
    }

    consumeCurrentStatus();

    // #endregion

    // #region 复制检验单

    const handleCopyInspection = () => {
      if (otherForm.currentInspection && otherForm.currentInspection.id) {
        otherForm.dialogCopy = true;
        otherForm.formDataCopy.type = '4';
      } else {
        ElMessage.warning('请先选择要复制的申请单!');
      }
    };

    // 点击复制申请单
    const copyInspectionInfo = () => {
      otherForm.copyFormRef
        .validate()
        .then(valid => {
          if (valid) {
            ElMessageBox({
              title: '提示',
              message: `确认复制检验单${otherForm.currentInspection?.no}吗？`,
              confirmButtonText: '确认',
              cancelButtonText: '取消',
              showCancelButton: true,
              closeOnClickModal: true,
              type: 'warning'
            })
              .then(() => {
                const formData = {
                  inspectionId: otherForm.currentInspection.id,
                  type: otherForm.formDataCopy.type
                };
                otherForm.dialogCopyLoading = true;
                copyInspection(formData).then(res => {
                  otherForm.dialogCopyLoading = false;
                  if (res) {
                    ElMessage.success(`创建申请单：${res.data.data?.no} 成功！`);
                    otherForm.dialogCopy = false;
                    otherForm.currentInspection = {};
                    proxy.getList();
                  }
                });
              })
              .catch(() => {});
          } else {
            return false;
          }
        })
        .catch(() => {});
    };

    const changeSelectInspection = row => {
      if (row && row.id) {
        row.radio = row.id;
        otherForm.currentInspection = row;
        otherForm.list.forEach(item => {
          if (item.id !== row.id) {
            item.radio = false;
          }
        });
      }
    };

    const haveCopyPermission = computed({
      get: () => {
        return getPermissionBtn('copyInspectionBtn');
      }
    });

    const onUpdateColumns = columns => {
      otherForm.tableKey = otherForm.tableKey + 1;
      otherForm.tableColumns = columns;
    };

    const getDictionAll = async () => {
      Object.keys(otherForm.dictionaryAll).forEach(async item => {
        const response = await getDictionary(item);
        if (response) {
          otherForm.dictionaryAll[item] = { enable: {}, all: {} };
          response.data.data.dictionaryoption.forEach(optionItem => {
            if (optionItem.status == 1) {
              otherForm.dictionaryAll[item].enable[optionItem.code] = optionItem.name;
            }
            otherForm.dictionaryAll[item].all[optionItem.code] = optionItem.name;
          });
        }
      });
    };
    onMounted(() => {
      getDictionAll();
    });

    return {
      closeAddIA,
      handleCopyInspection,
      handleRestore,
      getERPData,
      addIA,
      addMsg,
      changeType,
      changeERPTime,
      submitERPSuccess,
      editIA,
      drageHeader,
      formatDate,
      changeUser,
      changeBZTime,
      getNameByid,
      changeRadio,
      filterUserList,
      closeAddInfo,
      sortChange,
      editFrom,
      ...toRefs(otherForm),
      search,
      onSubmit,
      reset,
      cancleIA,
      submitIA,
      getPermissionBtn,
      iaDetail,
      colWidth,
      getColWidth,
      applicationFieldList,
      applicationCombinedFieldList,
      handleEnum,
      handleTag,
      getQueryInfo,
      copyInspectionInfo,
      changeSelectInspection,
      haveCopyPermission,
      onUpdateColumns,
      fieldTypesEnum,
      columnFixedTypesEnum
    };
  },
  computed: {
    ...mapGetters(['tenantGroup'])
  },
  created() {
    this.getList();
    // 刷新列表
    this.bus.$on('reloadInspectionList', msg => {
      this.getList();
    });
  },
  methods: {
    // 获取检测报告列表
    getList(data) {
      const _this = this;
      _this.listLoading = true;
      if (data && data !== undefined) {
        _this.listQuery.page = data.page ? data.page : 1;
        _this.listQuery.limit = data.limit;
      }
      const param = Object.assign(_this.formInline, _this.listQuery);
      param.page = param.page + '';
      param.limit = param.limit + '';
      // console.log(param)
      // 检验单列表接口
      getInspectionList(param).then(res => {
        // console.log(res.data)
        if (res !== false) {
          const { data } = res.data;
          _this.list = data.list;
          _this.list.forEach(item => {
            item.radio = false;
          });
          _this.total = data.totalCount;
        }
        setTimeout(() => {
          _this.listLoading = false;
        }, 100);
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.el-form {
  .el-form-item {
    margin-bottom: 0;
  }
}

.small-dialog {
  .dialog-main {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    img {
      width: 78%;
      margin-bottom: 20px;
    }
  }
}
.blue-color {
  color: $tes-primary;
  cursor: pointer;
}
</style>
