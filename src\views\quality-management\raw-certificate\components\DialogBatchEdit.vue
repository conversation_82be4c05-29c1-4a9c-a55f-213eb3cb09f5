<template>
  <!-- 打印 -->
  <el-dialog v-model="dialogShow" title="修改核标识" width="500px" :close-on-click-modal="false">
    <el-select v-model="nuclearMarker" multiple placeholder="请选择核标识" size="small" style="width: 100%">
      <el-option label="⚠H" value="H" />
      <el-option label="©" value="空" />
    </el-select>
    <template #footer>
      <el-button @click="cancelDialog()">取消</el-button>
      <el-button type="primary" @click="handleSubmit()">确定</el-button>
    </template>
  </el-dialog>
</template>
<script>
import { ElMessage } from 'element-plus';
import { saveCertificatePrint } from '@/api/certificate-export';
import { reactive, watch, toRefs } from 'vue';
export default {
  name: 'DialogBatchPrint',
  components: {},
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    selectRow: {
      type: Array,
      default: () => []
    }
  },
  emits: ['closeDialog'],
  setup(props, context) {
    const state = reactive({
      selectData: {},
      printNumber: null,
      dialogLoading: false,
      nuclearMarker: [],
      dialogShow: false
    });

    const cancelDialog = value => {
      state.dialogShow = false;
      context.emit('closeDialog', value);
    };

    const handleSubmit = async () => {
      state.dialogLoading = true;
      const params = {
        ...state.selectData,
        nuclearMarker: state.nuclearMarker.toString()
      };
      const { data } = await saveCertificatePrint({ entityList: [params] }).finally((state.dialogLoading = false));
      if (data) {
        ElMessage.success('编辑成功!');
        cancelDialog(true);
      }
    };

    watch(
      () => props.dialogVisible,
      newValue => {
        state.dialogShow = props.dialogVisible;
        if (newValue) {
          state.selectData = props.selectRow[0];
          state.nuclearMarker = state.selectData.nuclearMarker ? state.selectData?.nuclearMarker?.split(',') : [];
        }
      }
    );

    return {
      ...toRefs(state),
      cancelDialog,
      handleSubmit
    };
  }
};
</script>
<style lang="scss" scoped></style>
