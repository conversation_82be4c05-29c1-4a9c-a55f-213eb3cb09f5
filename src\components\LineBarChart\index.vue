<template>
  <v-chart class="chart" :option="chartOption" :autoresize="true" :style="{ width: chartWidth, height: chartHeight }" />
</template>

<script>
import { defaultOption } from './demo';
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { Bar<PERSON>hart, LineChart } from 'echarts/charts';
import { GridComponent, TitleComponent, TooltipComponent, LegendComponent, ToolboxComponent } from 'echarts/components';
import { UniversalTransition } from 'echarts/features';
import VChart, { THEME_KEY } from 'vue-echarts';
import { computed } from 'vue';

use([
  CanvasRenderer,
  GridComponent,
  BarChart,
  LineChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  UniversalTransition,
  ToolboxComponent
]);

export default {
  name: 'LineBarChart',
  components: {
    VChart
  },
  provide: {
    [THEME_KEY]: 'light'
  },
  props: {
    option: {
      type: Object,
      default: function () {
        return defaultOption;
      }
    },
    width: {
      type: String,
      default: '600px'
    },
    height: {
      type: String,
      default: '400px'
    }
  },
  setup(props, context) {
    const chartOption = computed({
      get: () => props.option
    });
    const chartWidth = computed({
      get: () => props.width
    });
    const chartHeight = computed({
      get: () => props.height
    });
    return {
      chartOption,
      chartWidth,
      chartHeight
    };
  }
};
</script>

<style scoped>
.chart {
  height: 200px;
  width: 300px;
}
</style>
