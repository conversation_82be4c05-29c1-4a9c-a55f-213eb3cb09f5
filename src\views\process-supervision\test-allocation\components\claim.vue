<template>
  <!-- 认领选项弹窗 -->
  <el-dialog
    :ref="dialogForm"
    v-model="dialogFormVisible"
    :before-close="reset"
    :close-on-click-modal="false"
    title="任务认领"
    width="480px"
  >
    <el-form ref="refForm" :model="form" :rules="rules" label-position="right">
      <el-form-item label="试验负责人：" :label-width="formLabelWidth" prop="ownerId">
        <el-select v-model="form.ownerId" placeholder="请选择" clearable filterable>
          <el-option
            v-for="(item, index) in userList"
            :key="index"
            :label="item.name"
            :value="item.id"
            :disabled="item.status === -2"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="试验日期：" :label-width="formLabelWidth" prop="finishedDate">
        <el-date-picker
          v-model="timeRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="handleTimeRange()"
        />
      </el-form-item>
      <el-form-item label="试验说明：" :label-width="formLabelWidth">
        <el-input v-model="form.assignedRemark" type="textarea" />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="reset()">取 消</el-button>
        <el-button type="primary" @click="handleSubmit()">确 定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { reactive, toRefs, ref, watch } from 'vue';
import { formatDate } from '@/utils/formatTime';
import { getLoginInfo } from '@/utils/auth';
// import { ElMessage } from 'element-plus'
// import { samplesOrder } from '@/api/order'
// import { getName } from '@/api/login'
// import { reportAudit } from '@/api/permission'
import store from '@/store';

export default {
  name: 'ModelOrder',
  props: {
    isShow: {
      type: Boolean,
      default: function () {
        return false;
      }
    },
    samplesInfo: {
      type: Object,
      default: function () {
        return {};
      }
    },
    mangeList: {
      type: Array,
      default: function () {
        return [];
      }
    },
    multipleSelection: {
      type: Array,
      default: function () {
        return [];
      }
    }
  },
  emits: ['setInfo', 'close'],
  setup(props, context) {
    const dialogForm = ref();
    const refForm = ref();
    const state = reactive({
      formLabelWidth: '100px',
      userList: store.state.common.nameList,
      timeRange: [],
      allocationNameList: [],
      dialogFormVisible: props.isShow,
      multipleSelection: props.multipleSelection,
      samplesInfo: props.samplesInfo,
      form: {
        assignedRemark: '',
        finishedDate: '',
        startDate: '',
        id: '',
        ownerId: getLoginInfo().accountId,
        status: 1
      },
      rules: {
        finishedDate: [
          {
            required: true,
            trigger: 'change',
            message: '请输入试验日期'
          }
        ],
        ownerId: [
          {
            required: true,
            trigger: 'blur',
            message: '请输入试验负责人'
          }
        ]
      }
    });

    watch(
      () => props.isShow,
      newValue => {
        console.log(props);
        state.dialogFormVisible = newValue;
        if (newValue) {
          state.multipleSelection = props.multipleSelection;
          state.samplesInfo = props.samplesInfo;
          state.form = JSON.parse(JSON.stringify(props.samplesInfo));
          state.form.ownerId = state.form.ownerId ? state.form.ownerId : getLoginInfo().accountId;
          state.form.startDate = state.form.startDate ? state.form.startDate : formatDate(new Date());
          state.form.finishedDate = state.form.finishedDate ? state.form.finishedDate : formatDate(new Date());
          state.timeRange[0] = state.form.startDate;
          state.timeRange[1] = state.form.finishedDate;
        }
      },
      { deep: true }
    );
    // 选择试验时间
    const handleTimeRange = () => {
      if (state.timeRange && state.timeRange.length === 2) {
        state.form.startDate = state.timeRange[0];
        state.form.finishedDate = state.timeRange[1];
        state.form.startDate = formatDate(state.form.startDate);
        state.form.finishedDate = formatDate(state.form.finishedDate);
      } else {
        state.timeRange = [];
        state.form.startDate = '';
        state.form.finishedDate = '';
      }
    };
    // 获取资源权限
    // reportAudit('allocation').then(res => {
    //   state.allocationNameList = res.data
    // })
    // 提交表单
    const handleSubmit = () => {
      const postdata = [];
      const idsStatus0 = [];
      const sampleNoList0 = [];
      const sampleIds0 = [];
      const idsStatus1 = [];
      const sampleNoList1 = [];
      const sampleIds1 = [];
      const idsStatus2 = [];
      const sampleNoList2 = [];
      const sampleIds2 = [];
      if (state.multipleSelection.length > 0) {
        state.multipleSelection.forEach(item => {
          const postdateitem = JSON.parse(JSON.stringify(state.form));
          postdateitem.id = item.id;
          postdateitem.status = 1;
          postdata.push(postdateitem);
          if (item.status === 0) {
            idsStatus0.push(item.id);
            sampleNoList0.push(item.secSampleNum);
            sampleIds0.push(item.sampleId);
          } else if (item.status === 1) {
            idsStatus1.push(item.id);
            sampleNoList1.push(item.secSampleNum);
            sampleIds1.push(item.sampleId);
          } else if (item.status === 2) {
            idsStatus2.push(item.id);
            sampleNoList2.push(item.secSampleNum);
            sampleIds2.push(item.sampleId);
          }
        });
      } else {
        state.form.status = 1;
        postdata[0] = JSON.parse(JSON.stringify(state.form));
      }

      refForm.value.validate(valid => {
        if (valid) {
          const formdata = JSON.parse(JSON.stringify(postdata));
          const p = {
            nos0: sampleNoList0.join(','),
            idsStatus0: idsStatus0.join(','),
            idsSample0: sampleIds0.join(','),
            nos1: sampleNoList1.join(','),
            idsStatus1: idsStatus1.join(','),
            idsSample1: sampleIds1.join(','),
            nos2: sampleNoList2.join(','),
            idsStatus2: idsStatus2.join(','),
            idsSample2: sampleIds2.join(','),
            formdata: formdata
          };
          context.emit('setInfo', p);
          // samplesOrder({ samplesVoList: formdata }).then(res => {
          //   if (res.data.code === 200) {
          //     ElMessage.success({
          //       message: '创建成功',
          //       type: 'success'
          //     })
          //     reset()
          //   }
          // })
        }
      });
    };
    const reset = () => {
      state.multipleSelection = [];
      refForm.value.clearValidate();
      state.form = {
        assignedRemark: '',
        finishedDate: '',
        startDate: '',
        id: '',
        ownerId: getLoginInfo().accountId,
        status: 1
      };
      state.timeRange = [];
      // refForm.value.resetFields()
      state.dialogFormVisible = false;
      context.emit('close', false);
    };
    return {
      ...toRefs(state),
      dialogForm,
      handleTimeRange,
      handleSubmit,
      reset,
      refForm,
      props
    };
  }
};
</script>

<style scoped lang="scss">
.el-select {
  width: 100%;
}
:deep(.el-range-editor.el-input__inner) {
  width: 100% !important;
}
</style>
