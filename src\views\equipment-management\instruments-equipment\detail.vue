<template>
  <!-- 设备台账详情 -->
  <el-drawer
    v-if="showDrawer"
    v-model="showDrawer"
    :title="title"
    direction="rtl"
    :before-close="handleClose"
    size="88%"
    destroy-on-close
    custom-class="instruments-equipment-detail"
  >
    <DrawerLayout
      v-loading="detailLoading"
      class="h-full"
      :has-left-panel="false"
      :has-button-group="getPermissionBtn('editEquipment')"
    >
      <template #drawer-title>
        <div class="drawer-title">
          <span>{{ detailData.name || '名称' }}</span>
          <el-tag size="small" :type="tagType[detailData.status]">{{
            dictionaryJSON[24].all[detailData.status]
          }}</el-tag>
          <QRCodeTrigger :value="qrCodeData" style="margin-left: 10px" />
        </div>
      </template>
      <template #button-group>
        <el-button size="small" @click="editDetial" @keyup.prevent @keydown.enter.prevent>编辑仪器设备</el-button>
      </template>
      <el-form class="isCheck" label-width="120px">
        <el-row>
          <el-col :span="8">
            <el-form-item label="所属分类：" prop="deviceCategoryId">
              <el-cascader
                v-model="detailData.deviceCategoryId"
                class="ssfl"
                size="small"
                :options="treeData"
                disabled
                :props="categoryProps"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="创建人：" prop="createByUserId">
              <UserTag :name="getNameByid(detailData.createByUserId) || '--'" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="建档日期：" prop="createDateTime">
              <div>{{ formatDate(detailData.createDateTime) || '--' }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="设备编号：" prop="deviceNumber">
              <div class="formValue">{{ detailData.deviceNumber || '--' }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="设备管理编号：" prop="deviceManagementNumber">
              <div class="formValue">{{ detailData.deviceManagementNumber || '--' }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="型号规格：" prop="model">
              <div class="formValue">{{ detailData.model || '--' }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="测量范围：" prop="measurementRange">
              <div class="formValue">{{ detailData.measurementRange || '--' }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="不确定度/...误差：" prop="measurementDeviation">
              <el-tooltip placement="top-start" effect="light" content="不确定度/准确度/最大允许误差">
                <div class="formValue">{{ detailData.measurementDeviation || '--' }}</div>
              </el-tooltip>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="生产厂家：" prop="equipmentManufactureName">
              <div class="formValue">{{ detailData.equipmentManufactureName || '--' }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="生产日期：" prop="equipmentManufactureDate">
              <div class="formValue">{{ detailData.equipmentManufactureDate || '--' }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="存放地点：" prop="deviceLocation">
              <div class="formValue">
                {{ dictionaryJSON['cfdd'].all[detailData.deviceLocation] || detailData.deviceLocation || '--' }}
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="到货日期：" prop="arrivalDate">
              <div>{{ formatDate(detailData.arrivalDate) || '--' }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="验收日期：" prop="acceptanceDate">
              <div>{{ formatDate(detailData.acceptanceDate) || '--' }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="保管人：" prop="custodianId">
              <UserTag :name="getNameByid(detailData.custodianId) || '--'" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否支持数采：" prop="supportDataAcquisition">
              <el-result v-if="detailData.supportDataAcquisition" icon="success">
                <template #extra> 是 </template>
              </el-result>
              <el-result v-else icon="error">
                <template #extra>
                  <div class="error">否</div>
                </template>
              </el-result>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="仪器设备计量：" prop="isEquipmentMetering">
              <el-result v-if="detailData.isEquipmentMetering" icon="success">
                <template #extra> 需要 </template>
              </el-result>
              <el-result v-else icon="error">
                <template #extra>
                  <div class="error">不需要</div>
                </template>
              </el-result>
            </el-form-item>
          </el-col>
          <el-col v-if="detailData.isEquipmentMetering" :span="8">
            <el-form-item label="计量周期：" prop="measurementCycle">
              <div>
                <span v-if="detailData.measurementCycle || detailData.measurementCycle === 0">{{
                  detailData.measurementCycle
                }}</span>
                <span v-else>/</span>
                {{ detailData.measurementCycleUnitType ? '月' : '年' }}
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="84">
            <el-form-item label="描述：" prop="remark">
              <div class="formValue">{{ detailData.remark || '--' }}</div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-tabs v-model="activeName" class="marginTop" @tab-click="tabsClick">
        <el-tab-pane label="设备码点" name="1">
          <DetailCodePoint :list="codepointList" :detail-data="detailData" />
        </el-tab-pane>
        <el-tab-pane label="计量信息" name="2">
          <DetailMeasuring :list="measurementList" :detail-data="detailData" />
        </el-tab-pane>
        <el-tab-pane label="维修信息" name="3">
          <DetailFixedRecord
            :list="deviceFixedRecordList"
            :detail-data="detailData"
            :dictionary-all="dictionaryJSON"
            :device-id="deviceId"
            @handleResh="handleResh"
          />
        </el-tab-pane>
        <el-tab-pane label="可检项目" name="4">
          <DetailEquipmentItems :id="deviceId" type="equipment" :list="deviceItemList" @handleResh="handleReshItem" />
        </el-tab-pane>
        <el-tab-pane label="设备附件" name="5">
          <DetailFile :device-id="deviceId" />
        </el-tab-pane>
      </el-tabs>
    </DrawerLayout>
    <!-- 二维码弹出框 -->
    <QRCodePopup title="设备信息" />
    <DrawerUnit
      :drawer="unitVisiable"
      title="编辑仪器设备"
      :dictionary-all="dictionaryJSON"
      :tree-data="treeData"
      :detail-data="detailData"
      @close="closeDrawer"
    />
  </el-drawer>
</template>
<script>
import { ref, watch, reactive, getCurrentInstance, toRefs, computed } from 'vue';
import { getLoginInfo } from '@/utils/auth';
import { getDetail, itemListApi } from '@/api/equipment';
import DrawerLayout from '@/components/DrawerLayout';
import { getPermissionBtn } from '@/utils/common';
import { getNameByid } from '@/utils/common';
import DetailMeasuring from './DetailMeasuring';
import DetailCodePoint from './DetailCodePoint';
import DetailFixedRecord from './DetailFixedRecord';
import DetailFile from './DetailFile';
import DetailEquipmentItems from '@/components/ItemComponents/DetailEquipmentItems';
import DrawerUnit from './DrawerUnit';
import { formatDate } from '@/utils/formatTime';
import UserTag from '@/components/UserTag';
import { QRCodePopup, QRCodeTrigger } from '@/components/QRCodePopup';
export default {
  name: 'DetailEquipment',
  components: {
    DetailMeasuring,
    DetailEquipmentItems,
    DetailCodePoint,
    DrawerUnit,
    DrawerLayout,
    UserTag,
    DetailFile,
    DetailFixedRecord,
    QRCodePopup,
    QRCodeTrigger
  },
  props: {
    drawer: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      required: true
    },
    dictionaryAll: {
      type: Object,
      default: function () {
        return {};
      }
    },
    deviceId: {
      type: String,
      default: ''
    },
    treeData: {
      type: Array,
      default: function () {
        return [];
      }
    }
  },
  emits: ['close'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const state = reactive({
      activeName: '1',
      deviceId: '', // 设备id
      detailData: {},
      treeData: [], // 分类树
      unitVisiable: false,
      tagType: {
        Running: 'success',
        Standby: 'warning',
        Maintenance: 'default',
        Fault: 'danger',
        Scrapped: 'info'
      },
      typeOfData: {
        数值型: 1,
        枚举型: 2,
        字符串: 3
      },
      codepointList: [], // 设备码点列表
      detailLoading: false, // 详情页的loading
      measurementList: [], // 计量信息列表
      deviceFixedRecordList: [], // 维修信息列表
      deviceItemList: [], // 设备关联的项目
      isRefresh: true,
      accountId: getLoginInfo().accountId, // 当前登录人的id
      listLoading: false,
      dictionaryJSON: {
        cfdd: {
          enable: {},
          all: {}
        },
        24: {
          enable: {},
          all: {}
        }
      },
      categoryProps: {
        expandTrigger: 'hover',
        checkStrictly: true,
        children: 'children',
        label: 'name',
        value: 'id'
      },
      tableSize: 'medium',
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      title: ''
    });
    const qrCodeData = computed(() => {
      return {
        type: 'device',
        id: state.detailData.id,
        orderId: state.detailData.orderId
      };
    });
    const filterNode = (value, data) => {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    };
    const showDrawer = ref(props.drawer);
    const handleClose = () => {
      if (state.isModified) {
        proxy
          .$confirm('确认离开当前页面吗？离开后数据不可恢复', {
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            type: 'warning',
            showClose: false,
            closeOnClickModal: false,
            closeOnPressEscape: false
          })
          .then(() => {
            showDrawer.value = false;
            state.isModified = false;
            context.emit('close', false);
          })
          .catch(() => {});
      } else {
        showDrawer.value = false;
        context.emit('close', false);
      }
    };
    watch(props, newValue => {
      showDrawer.value = newValue.drawer;
      if (showDrawer.value) {
        state.deviceId = props.deviceId;
        state.treeData = props.treeData;
        state.activeName = '1';
        state.title = props.title;
        state.dictionaryJSON = props.dictionaryAll;
        initDetail(state.deviceId);
        getDeviceItem(state.deviceId);
      }
    });
    // 根据维修信息，刷新设备状态
    const handleResh = () => {
      initDetail(state.deviceId);
    };
    const handleReshItem = () => {
      getDeviceItem(state.deviceId);
    };
    // 获取设备关联的项目
    const getDeviceItem = deviceId => {
      itemListApi(deviceId).then(res => {
        state.tableLoading = false;
        if (res) {
          state.deviceItemList = res.data.data;
        }
      });
    };
    // 查询详情
    const initDetail = deviceId => {
      state.detailLoading = true;
      getDetail(deviceId).then(res => {
        state.detailLoading = false;
        if (res) {
          state.detailData = res.data.data.device;
          state.codepointList = res.data.data.devicecodepointList;
          state.measurementList = res.data.data.devicemeasurementList;
          state.deviceFixedRecordList = res.data.data.deviceFixedRecordList;
        }
      });
    };
    const closeDrawer = value => {
      if (value.isRefresh) {
        initDetail(state.deviceId);
      }
      state.unitVisiable = false;
    };
    const tabsClick = () => {};
    const editDetial = () => {
      state.unitVisiable = true;
    };
    return {
      ...toRefs(state),
      qrCodeData,
      initDetail,
      handleResh,
      handleReshItem,
      closeDrawer,
      getPermissionBtn,
      tabsClick,
      editDetial,
      filterNode,
      getNameByid,
      formatDate,
      handleClose,
      showDrawer
    };
  }
};
</script>

<style lang="scss" scoped>
@import '@/styles/tree.scss';
.instruments-equipment-detail {
  .formValue {
    word-wrap: break-word;
  }
  .drawer-title {
    display: flex;
    align-items: center;
  }
  .ssfl {
    width: 300px;
    :deep(.el-input.is-disabled .el-input__inner) {
      background: transparent;
    }
    :deep(.el-input__suffix) {
      display: none;
    }
  }
  :slotted(.drawer-wrapper .drawer-title) {
    margin-bottom: 0 !important;
  }
}

.marginTop {
  margin-top: 35px;
}
:deep(.el-input.is-disabled .el-input__inner) {
  border: 0;
  color: #606266;
  padding: 0;
  cursor: text;
}
:deep(.el-result) {
  padding: 0;
  flex-direction: row;
  justify-content: flex-start;
}
:deep(.el-result__icon svg) {
  width: 14px;
  height: 14px;
  position: relative;
  top: 2px;
}
:deep(.el-result__extra) {
  margin-top: 0;
  color: $green;
  .error {
    color: $red;
  }
}
.isCheck {
  margin-top: 12px;
  padding: 10px 20px;
  background: #f5f7fa;
  border-radius: 3px;
}
</style>
<style lang="scss">
.instruments-equipment-detail {
  .el-drawer__close-btn {
    padding-right: 10px;
  }
  .ssfl {
    width: 50%;
    .el-input__suffix {
      display: none;
    }
  }
}
.instruments-equipment-detail .el-drawer__header {
  padding: 20px 40px 0px !important;
}
.instruments-equipment-detail .el-drawer__body {
  padding: 20px 40px !important;
  // background: #f0f2f5;
}
</style>
