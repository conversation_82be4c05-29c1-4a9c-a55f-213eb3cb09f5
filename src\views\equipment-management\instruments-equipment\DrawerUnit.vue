<template>
  <el-drawer
    v-model="showDrawer"
    :title="titles"
    direction="rtl"
    :before-close="handleClose"
    :size="750"
    destroy-on-close
    :close-on-click-modal="false"
  >
    <DrawerLayout v-loading="drawerLoading" :has-button-group="false" :has-page-header="false">
      <el-form
        ref="formRef"
        :inline="true"
        :model="formData"
        label-width="110px"
        label-position="top"
        class="form-height-auto"
      >
        <el-row :gutter="40">
          <el-col :span="12">
            <el-form-item
              label="所属分类："
              prop="deviceCategoryId"
              :rules="{ required: true, message: '请选择所属分类', trigger: 'change' }"
            >
              <el-cascader
                v-model="formData.deviceCategoryId"
                :options="dialogTreeData"
                :props="categoryProps"
                style="width: 100%"
                @change="changeCategory"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="设备编号："
              prop="deviceNumber"
              :rules="{ required: true, message: '请输入设备编号', trigger: 'change' }"
            >
              <el-input
                v-model.trim="formData.deviceNumber"
                maxlength="30"
                placeholder="请输入设备编号"
                @change="handleModify"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="仪器设备名称："
              prop="name"
              :rules="{ required: true, message: '请输入仪器设备名称', trigger: 'change' }"
            >
              <el-input
                v-model.trim="formData.name"
                maxlength="100"
                placeholder="请输入仪器设备名称"
                @change="handleModify"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="状态："
              prop="status"
              :rules="{ required: true, message: '请选择仪器设备状态', trigger: 'change' }"
            >
              <el-select
                v-model="formData.status"
                placeholder="请选择仪器设备状态"
                clearable
                filterable
                style="width: 100%"
                @change="handleModify"
              >
                <el-option v-for="(val, key) in dictionaryAll['24'].enable" :key="key" :label="val" :value="key" />
              </el-select> </el-form-item
          ></el-col>
          <el-col :span="12">
            <el-form-item
              label="型号规格："
              prop="model"
              :rules="{ required: true, message: '请输入型号规格', trigger: 'change' }"
            >
              <el-input
                v-model.trim="formData.model"
                maxlength="100"
                placeholder="请输入型号规格"
                @change="handleModify"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="设备管理编号："
              prop="deviceManagementNumber"
              :rules="{ required: false, message: '请输入设备管理编号', trigger: 'change' }"
            >
              <el-input
                v-model.trim="formData.deviceManagementNumber"
                maxlength="100"
                placeholder="请输入设备管理编号"
                @change="handleModify"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="测量范围：" prop="measurementRange">
              <el-input
                v-model="formData.measurementRange"
                maxlength="100"
                placeholder="请输入测量范围"
                @change="handleModify"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="不确定度/准确度/最大允许误差：" prop="measurementDeviation">
              <el-input
                v-model="formData.measurementDeviation"
                maxlength="50"
                placeholder="请输入不确定度/准确度/最大允许误差"
                @change="handleModify"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="生产厂家：" prop="equipmentManufactureName">
              <el-input
                v-model="formData.equipmentManufactureName"
                maxlength="100"
                placeholder="请输入生产厂家"
                @change="handleModify"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="生产日期：" prop="equipmentManufactureDate">
              <el-date-picker
                v-model="formData.equipmentManufactureDate"
                type="date"
                placeholder="请选择生产日期"
                style="width: 100%"
                @change="
                  val => {
                    return handleChangeDate(val, 'equipmentManufactureDate');
                  }
                "
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="存放地点：" prop="deviceLocation">
              <el-select
                v-model="formData.deviceLocation"
                placeholder="请选择存放地点"
                clearable
                filterable
                style="width: 100%"
                @change="handleModify"
              >
                <el-option v-for="(val, key) in dictionaryAll['cfdd'].enable" :key="key" :label="val" :value="key" />
              </el-select>
              <!-- <el-input
                v-model="formData.deviceLocation"
                maxlength="100"
                placeholder="请输入存放地点"
                @change="handleModify"
              /> -->
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="到货日期：" prop="arrivalDate">
              <el-date-picker
                v-model="formData.arrivalDate"
                type="date"
                placeholder="请选择到货日期"
                style="width: 100%"
                @change="
                  val => {
                    return handleChangeDate(val, 'arrivalDate');
                  }
                "
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="验收日期：" prop="acceptanceDate">
              <el-date-picker
                v-model="formData.acceptanceDate"
                type="date"
                placeholder="请选择验收日期"
                style="width: 100%"
                @change="
                  val => {
                    return handleChangeDate(val, 'acceptanceDate');
                  }
                "
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="保管人：" prop="custodianId">
              <el-select
                v-model="formData.custodianId"
                placeholder="请选择保管人"
                clearable
                filterable
                style="width: 100%"
                @change="handleModify"
              >
                <el-option v-for="item in userList" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否支持数采：" prop="supportDataAcquisition">
              <el-radio-group v-model="formData.supportDataAcquisition" @change="handleModify">
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="仪器设备计量：" prop="isEquipmentMetering">
              <el-radio-group v-model="formData.isEquipmentMetering" @change="handleModify">
                <el-radio :label="true">需要</el-radio>
                <el-radio :label="false">不需要</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              v-if="formData.isEquipmentMetering"
              prop="measurementCycle"
              label="计量周期："
              :rules="{ required: true, validator: validateNumber, trigger: 'change' }"
            >
              <el-input v-model="formData.measurementCycle" maxlength="2" placeholder="请输入计量周期">
                <template #append>
                  <el-select
                    v-model="formData.measurementCycleUnitType"
                    placeholder="请选择周期单位"
                    style="width: 120px"
                    @change="handleModify"
                  >
                    <el-option label="年" :value="0" />
                    <el-option label="月" :value="1" />
                  </el-select>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="描述：" prop="remark">
              <el-input
                v-model="formData.remark"
                type="textarea"
                :rows="2"
                placeholder="请输入设备描述"
                @change="handleModify"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div class="drawer-fotter">
        <el-button type="primary" :loading="drawerLoading" @click="onSubmit">确认</el-button>
        <el-button :loading="drawerLoading" @click="handleClose">取消</el-button>
      </div>
    </DrawerLayout>
  </el-drawer>
</template>

<script>
import { ref, watch, reactive, getCurrentInstance, toRefs } from 'vue';
import { saveUnit, updateUnit } from '@/api/equipment';
import DrawerLayout from '@/components/DrawerLayout';
import store from '@/store';
import { formatDate } from '@/utils/formatTime';

export default {
  name: 'DrawerUnit',
  components: { DrawerLayout },
  props: {
    drawer: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      required: true
    },
    treeData: {
      type: Array,
      default: function () {
        return [];
      }
    },
    dictionaryAll: {
      type: Object,
      default: function () {
        return {};
      }
    },
    detailData: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['close'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    // 抽屉事件
    const showDrawer = ref(props.drawer);
    const state = reactive({
      titles: '',
      isModified: false,
      drawerLoading: false,
      userList: store.state.common.nameList,
      dictionaryJSON: {
        cfdd: {
          enable: {},
          all: {}
        },
        24: {
          enable: {},
          all: {}
        }
      },
      options: {
        1: '年',
        2: '月'
      },
      dialogTreeData: [],
      // equipUnit: [],
      detailData: {}, // 详情数据
      formRef: ref(),
      formData: {},
      categoryProps: {
        expandTrigger: 'hover',
        checkStrictly: true,
        children: 'children',
        label: 'name',
        value: 'id'
      }
    });
    // 关闭抽屉
    const handleClose = () => {
      if (state.isModified) {
        proxy
          .$confirm('确认离开当前页面吗？离开后数据不可恢复', {
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            type: 'warning',
            showClose: false,
            closeOnClickModal: false,
            closeOnPressEscape: false
          })
          .then(() => {
            showDrawer.value = false;
            state.isModified = false;
            context.emit('close', { isRefresh: false, isShow: false });
          })
          .catch(() => {});
      } else {
        showDrawer.value = false;
        context.emit('close', { isRefresh: false, isShow: false });
      }
    };
    // 格式化日期
    const handleChangeDate = (val, fieldName) => {
      state.formData[fieldName] = formatDate(val);
    };
    watch(props, newValue => {
      showDrawer.value = newValue.drawer;
      if (showDrawer.value) {
        state.titles = props.title;
        state.detailData = props.detailData;
        state.dictionaryJSON = props.dictionaryAll;
        state.dialogTreeData = props.treeData;
        state.isModified = false;
        state.equipUnit = props.equipUnit;
        if (props.title === '新增仪器设备') {
          initDetail(props);
        } else {
          getDetail(props);
        }
      }
    });
    const handleModify = value => {
      state.isModified = true;
    };
    const initDetail = newData => {
      state.formData = {
        isEquipmentMetering: false,
        supportDataAcquisition: false,
        measurementCycleUnitType: 1,
        deviceCategoryId: state.detailData.checkTreeId === 'all' ? '' : state.detailData.checkTreeId
      };
    };
    const getDetail = newData => {
      state.formData = JSON.parse(JSON.stringify(newData.detailData));
    };
    // 确认新增
    const onSubmit = () => {
      proxy.$refs['formRef'].validate(valid => {
        if (valid) {
          if (props.title === '新增仪器设备') {
            state.drawerLoading = true;
            saveUnit(state.formData).then(res => {
              state.drawerLoading = false;
              if (res.data.code === 200) {
                if (res.data.data.isSuccess) {
                  state.isModified = false;
                  context.emit('close', { isRefresh: true, isShow: false });
                  proxy.$message.success('新增成功');
                } else {
                  proxy.$message.closeAll();
                  proxy.$message.error(res.data.data.message);
                }
              }
            });
          } else {
            state.drawerLoading = true;
            updateUnit(state.formData).then(res => {
              state.drawerLoading = false;
              if (res.data.code === 200) {
                if (res.data.data.isSuccess) {
                  state.isModified = false;
                  context.emit('close', { isRefresh: true, isShow: false });
                  proxy.$message.success('编辑成功');
                } else {
                  proxy.$message.closeAll();
                  proxy.$message.error(res.data.data.message);
                }
              }
            });
          }
        } else {
          return false;
        }
      });
    };
    const validateNumber = (rule, value, callback) => {
      const numberReg = /^\d+$/;
      if (value === '' || value === undefined || value === null) {
        callback(new Error('请输入计量周期'));
      } else {
        if (numberReg.test(value)) {
          callback();
        } else {
          callback(new Error('请输入非负整数'));
        }
      }
    };

    // 所属分类change
    const changeCategory = value => {
      const len = value.length - 1;
      state.formData.deviceCategoryId = value[len];
      state.isModified = true;
    };
    return {
      ...toRefs(state),
      getDetail,
      validateNumber,
      changeCategory,
      handleModify,
      handleChangeDate,
      onSubmit,
      handleClose,
      showDrawer,
      initDetail
    };
  }
};
</script>

<style lang="scss" scoped>
:deep(.el-input--medium) {
  line-height: 1;
}
</style>
