<template>
  <div class="ReportManage">
    <div class="header-btn-group">
      <el-button
        v-if="getPermissionBtn('uploadReportMCBtn')"
        size="small"
        icon="el-icon-upload2"
        type="primary"
        @click="handleAddEdit()"
        @keyup.prevent
        @keydown.enter.prevent
        >报告模板上传</el-button
      >
    </div>
    <el-table
      ref="tableRef"
      :key="tableKey"
      v-loading="listLoading"
      :data="tableList"
      height="auto"
      size="medium"
      fit
      border
      class="dark-table base-table format-height-table"
      @header-dragend="drageHeader"
    >
      <el-table-column label="序号" prop="index" :width="colWidth.serialNo" align="center">
        <template #default="scope">
          <div class="">{{ Number(scope.$index) + 1 }}</div>
        </template>
      </el-table-column>
      <el-table-column label="名称" prop="name" show-overflow-tooltip :min-width="colWidth.name" align="left">
        <template #default="{ row }">
          <span class="nowrap">{{ row.name }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="报告语言"
        prop="languageType"
        show-overflow-tooltip
        align="left"
        :min-width="colWidth.status"
      >
        <template #default="{ row }">
          <div class="nowrap">{{ languageTypeJson[row.languageType] || '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column
        label="描述"
        prop="description"
        show-overflow-tooltip
        align="left"
        :min-width="colWidth.description"
      >
        <template #default="{ row }">
          <div class="nowrap">{{ row.description || '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column
        label="附件"
        prop="fileName"
        show-overflow-tooltip
        :min-width="colWidth.description"
        align="left"
      >
        <template #default="{ row }">
          <span class="nowrap blue" @click="downUpload(row.fileId)">{{ row.fileName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="应用范围" prop="type" show-overflow-tooltip align="left" :width="colWidth.typeGroup">
        <template #default="{ row }">
          <div class="nowrap">{{ filterTypes(row.type) || '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column label="更新日期" prop="lastUpdateDateTime" :width="colWidth.date" sortable>
        <template #default="{ row }">
          <span>{{ formatDate(row.lastUpdateDateTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="更新人" prop="lastUpdateByUserId" align="left" :width="colWidth.person">
        <template #default="{ row }">
          <UserTag :name="getNameByid(row.lastUpdateByUserId) || row.lastUpdateByUserId || '--'" />
        </template>
      </el-table-column>
      <el-table-column label="状态" prop="status" :width="colWidth.status">
        <template #default="{ row }">
          <el-tag size="small" effect="dark" :type="statusDicClass[row.status]"> {{ statusDic[row.status] }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" prop="caozuo" :width="colWidth.operationMultiple" fixed="right">
        <template #default="{ row }">
          <span v-if="row.status == 0" class="blue-color" @click="handleAddEdit(row)">编辑</span>
          <span v-if="row.status != 1" class="blue-color" @click="handleOpera(row, 1)">启用</span>
          <span v-if="row.status == 1" class="blue-color" @click="handleOpera(row, 2)">停用</span>
          <span v-if="row.status != 1" class="blue-color" @click="handleOpera(row, 3)">删除</span>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog v-model="dialogUpload" :title="dialogTitle" :close-on-click-modal="false" custom-class="small-dialog">
      <el-form v-if="dialogUpload" ref="ruleForm" :model="formData" label-position="right" label-width="105px">
        <el-form-item
          label="是否打印原始数据："
          prop="isPrint"
          label-width="155px"
          :rules="{ required: true, message: '请选择是否打印原始数据', trigger: 'change' }"
        >
          <el-radio-group v-model="formData.isPrint">
            <el-radio :label="1" size="small">是</el-radio>
            <el-radio :label="0" size="small">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          label="名称："
          prop="name"
          :rules="{ required: true, message: '请输入模板名称', trigger: 'change' }"
        >
          <el-input v-model="formData.name" placeholder="请输入模板名称" />
        </el-form-item>
        <el-form-item
          label="报告语言："
          prop="languageType"
          :rules="{ required: true, message: '请选择报告语言', trigger: 'change' }"
        >
          <el-select v-model="formData.languageType" placeholder="请选择报告语言" style="width: 100%">
            <el-option v-for="(val, key) in languageTypeJson" :key="key" :label="val" :value="Number(key)" />
          </el-select>
        </el-form-item>
        <el-form-item label="应用范围：" prop="type">
          <el-select
            v-model="formData.types"
            placeholder="请选择应用范围"
            multiple
            style="width: 100%"
            @change="changeType"
          >
            <el-option v-for="item in typeOptions" :key="item.code" :label="item.name" :value="item.code" />
          </el-select>
        </el-form-item>
        <el-form-item label="描述：" prop="description">
          <el-input v-model="formData.description" type="textarea" :rows="3" placeholder="请输入模板描述" />
        </el-form-item>
        <el-form-item
          v-if="dialogTitle == '报告模板上传'"
          label="上传模板："
          prop="fileId"
          :rules="{ required: true, message: '请上传模板', trigger: 'change' }"
        >
          <el-upload
            ref="uploadFile"
            class="upload-demo"
            :headers="headerconfig"
            :accept="'.doc, .docx'"
            :action="uploadAction"
            :on-success="handleUploadSuccess"
            :before-upload="beforeUpload"
            :on-remove="handleRemove"
            :limit="1"
            :on-exceed="handleExceed"
            :file-list="fileList"
          >
            <el-button size="small" icon="el-icon-upload2" type="primary" @keyup.prevent @keydown.enter.prevent
              >选择文件</el-button
            >
            <template #tip>
              <div class="el-upload__tip">单文件大小不超过20M，仅支持.doc，.docx文件扩展名</div>
            </template>
          </el-upload>
        </el-form-item>
        <!-- <el-form-item label="模板附件：">
          <el-upload
            ref="imgUploadRef"
            action="#"
            class="img-upload"
            list-type="picture-card"
            :auto-upload="false"
            :file-list="imgFileList"
            :on-remove="handleImgRemove"
          >
            <i class="el-icon-plus" />
            <template #file="{ file }">
              <div>
                <img class="el-upload-list__item-thumbnail" :src="file.url" alt="">
                <span class="el-upload-list__item-actions">
                  <span
                    v-if="!imgRemoveDisabled"
                    class="el-upload-list__item-delete"
                    @click="handleImgRemove(file)"
                  >
                    <i class="el-icon-delete" />
                  </span>
                </span>
              </div>
            </template>
          </el-upload>
          <el-dialog v-model="dialogImgVisible">
            <img width="100%" :src="dialogImageUrl" alt="">
          </el-dialog>
        </el-form-item> -->
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogUpload = false">取 消</el-button>
          <el-button type="primary" @click="onSubmit">确 认</el-button>
        </span>
      </template>
    </el-dialog>
    <pagination
      v-show="total > 0"
      :page="listQuery.page"
      :limit="listQuery.limit"
      :total="total"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { reactive, ref, toRefs, getCurrentInstance, watch } from 'vue';
import store from '@/store';
import UserTag from '@/components/UserTag';
import Pagination from '@/components/Pagination';
import { getToken } from '@/utils/auth';
import { formatDate } from '@/utils/formatTime';
import { getNamesByid, getNameByid, getPermissionBtn } from '@/utils/common';
import { initReportList, changeStatus, deleteReport, addRepore, editRepore, downLoad } from '@/api/material';
import { categoryReportTemplateUploadUrl } from '@/api/uploadAction';
import { drageHeader } from '@/utils/formatTable';
import { colWidth } from '@/data/tableStyle';
import { getDictionary } from '@/api/user';
import _ from 'lodash';

export default {
  name: 'ReportManage',
  components: { Pagination, UserTag },
  props: {
    categoryId: {
      type: String,
      default: ''
    },
    currentData: {
      type: Object,
      default: function () {
        return {};
      }
    },
    categoryCode: {
      type: String,
      default: ''
    }
  },
  setup(props) {
    const { proxy } = getCurrentInstance();
    const state = reactive({
      tableRef: ref(),
      currentData: {},
      languageTypeJson: {
        1: '中文报告',
        2: '英文报告',
        3: '中英文报告'
      },
      categoryId: '', // 物资分类id
      categoryCode: '', // 物资分类code
      dialogUpload: false,
      ruleForm: ref(),
      listLoading: false,
      headerconfig: {
        Authorization: getToken()
      },
      uploadAction: categoryReportTemplateUploadUrl(),
      dialogTitle: '',
      status: {
        3: { class: 'icon-tes-info', label: '待审核', value: '3' },
        5: { class: 'icon-tes-success', label: '已通过', value: '5' }
      },
      listQuery: {
        limit: 20,
        page: 1
      },
      fileList: [],
      formData: {},
      statusRadio: '3',
      tableList: [],
      nameList: store.state.common.nameList,
      uploadFile: ref(),
      samplesInfo: {},
      dialogFormVisible: false,
      total: 0,
      btnType: {
        0: {
          label: '启用',
          btnClass: 'blue-color'
        },
        1: {
          label: '停用',
          btnClass: 'orange-color'
        },
        2: {
          label: '删除',
          btnClass: 'red-color'
        }
      },
      statusDicClass: {
        0: 'warning',
        1: 'success',
        2: 'info'
      },
      statusDic: {
        0: '待启用',
        1: '已启用',
        2: '已停用'
      },
      typeOptions: [],
      dialogImageUrl: '',
      dialogImgVisible: false,
      imgRemoveDisabled: false,
      imgFileList: [],
      imgUploadRef: ref()
    });
    watch(props, async newValue => {
      state.categoryId = props.categoryId;
      state.categoryCode = props.categoryCode;
      state.currentData = props.currentData;
      state.typeOptions = await getTypeDictionary();
      state.languageTypeJson = await getLanguageType();
      getList();
    });
    const tableKey = ref(0);
    const getList = query => {
      const params = { categoryId: state.categoryId };
      if (query && query.page) {
        params.page = query.page.toString();
        params.limit = query.limit.toString();
        state.listQuery.page = query.page;
        state.listQuery.limit = query.limit;
      } else {
        state.listQuery.page = 1;
        params.page = '1';
        params.limit = state.listQuery.limit.toString();
      }
      state.listLoading = true;
      initReportList(params).then(res => {
        state.listLoading = false;
        if (res.data.code === 200) {
          state.tableList = res.data.data.list;
          state.total = res.data.data.totalCount;
        }
      });
    };
    const handleSizeChange = val => {
      state.listQuery.limit = val;
      getList();
    };
    // 新增、编辑报告
    const handleAddEdit = async row => {
      if (state.categoryId) {
        state.dialogUpload = true;
        if (row) {
          state.dialogTitle = '报告模板编辑';
          state.formData = JSON.parse(JSON.stringify(row));
          state.formData.types = state.formData.type ? state.formData.type.split(',') : [];
        } else {
          state.formData = {
            isPrint: 0,
            languageType: 1
          };
          state.formData.types = [];
          state.dialogTitle = '报告模板上传';
        }
      } else {
        proxy.$message.warning('请现在左侧添加类目');
      }
    };
    // 停用，删除，启用，type: 3删除/2停用/1启用
    const handleOpera = (row, type) => {
      if (type === 1) {
        submitOpera(row, 1);
      } else {
        const confirmTitle = {
          3: {
            title: '确认删除',
            text: '是否确认删除？',
            btnTitle: '确认删除'
          },
          2: {
            title: '确认停用',
            text: '当前报告已生效，是否确认停用？',
            btnTitle: '确认停用'
          }
        };
        proxy
          .$confirm(confirmTitle[type].text, confirmTitle[type].title, {
            confirmButtonText: confirmTitle[type].btnTitle,
            cancelButtonText: '取消',
            showCancelButton: true,
            closeOnClickModal: false,
            type: 'warning'
          })
          .then(() => {
            if (type === 3) {
              deleteList(row);
            } else {
              submitOpera(row, type);
            }
          })
          .catch(() => {});
      }
    };
    const deleteList = row => {
      deleteReport([row.id]).then(res => {
        if (res.data.code === 200) {
          proxy.$message.success(res.data.message);
          getList();
        } else {
          proxy.$message.error(res.data.message);
        }
      });
    };
    // 启用、停用、删除相关报告接口
    const submitOpera = (row, type) => {
      const params = {
        id: row.id,
        status: type
      };
      changeStatus(params).then(res => {
        if (res.data.code === 200) {
          proxy.$message.success(res.data.message);
          getList();
        } else {
          proxy.$message.error(res.data.message);
        }
      });
    };
    const onSubmit = () => {
      state.ruleForm
        .validate()
        .then(valid => {
          if (valid) {
            state.listLoading = true;
            if (state.dialogTitle === '报告模板上传') {
              addRepore({ categoryCode: state.categoryCode, categoryId: state.categoryId, ...state.formData }).then(
                res => {
                  state.listLoading = false;
                  if (res.data.code === 200) {
                    proxy.$message.success(res.data.message);
                    state.dialogUpload = false;
                    getList();
                  }
                }
              );
            } else {
              editRepore(state.formData).then(res => {
                state.listLoading = false;
                if (res.data.code === 200) {
                  proxy.$message.success(res.data.message);
                  state.dialogUpload = false;
                  getList();
                }
              });
            }
          }
        })
        .catch(() => {});
    };
    const handleUploadSuccess = (res, file, fileList) => {
      if (res.code === 200) {
        state.formData.fileId = res.data.fileId;
        state.formData.fileName = res.data.fileName;
        state.formData.fileRemoteName = res.data.fileRemoteName;
      }
    };
    // 下载附件
    const downUpload = fileId => {
      downLoad({ fileId: fileId }).then(res => {
        if (res) {
          window.open(res.data.data.url);
        }
      });
    };
    const handleRemove = () => {
      state.formData.fileId = '';
    };
    const handleExceed = () => {
      proxy.$message.warning('一次只能上传一个附件');
    };
    const beforeUpload = file => {
      const fileSize = file.size / 1024 / 1024 < 20;
      var fileName = '';
      if (file.name) {
        fileName = file.name.substring(file.name.lastIndexOf('.') + 1);
      }
      if (!fileSize) {
        proxy.$message.error('上传附件大小不能超过20M');
        return false;
      } else if (fileName !== 'doc' && fileName !== 'docx') {
        proxy.$message.error('仅支持.doc，.docx文件扩展名');
        return false;
      } else {
        return true;
      }
    };
    // 图片预览
    const handleImgPreview = file => {
      state.dialogImageUrl = file.url;
      state.dialogImgVisible = true;
    };
    // 图片删除
    const handleImgRemove = file => {
      console.log(file);
      console.log(state.imgUploadRef);
      var fileList = state.imgUploadRef.uploadFiles;
      var imgs = _.filter(fileList, img => {
        return file.name !== img.name;
      });
      state.imgUploadRef.uploadFiles = imgs;
      // state.imgUploadRef.onRemove(file)
    };
    // 字典---应用范围列表
    const getTypeDictionary = () => {
      return new Promise((resolve, reject) => {
        getDictionary('JYLX')
          .then(response => {
            if (response !== false) {
              const { data } = response;
              resolve(data.data.dictionaryoption);
            } else {
              resolve([]);
            }
          })
          .catch(error => {
            reject(error);
          });
      });
    };
    // 字典---报告语言
    const getLanguageType = () => {
      return new Promise((resolve, reject) => {
        getDictionary('BGYY')
          .then(response => {
            if (response !== false) {
              const { data } = response;
              const statusJson = {};
              data.data.dictionaryoption.forEach(val => {
                if (val.status === 1) {
                  statusJson[val.code] = val.name;
                }
              });
              resolve(statusJson);
            } else {
              resolve({});
            }
          })
          .catch(error => {
            reject(error);
          });
      });
    };
    // 应用范围选择
    const changeType = types => {
      state.formData.type = types.join(',');
    };
    // 列表过滤应用范围
    const filterTypes = type => {
      var names = [];
      if (type) {
        const types = type.split(',');
        if (types.length > 0 && state.typeOptions.length > 0) {
          types.forEach(item => {
            names.push(
              state.typeOptions.filter(t => {
                return t.code === item;
              })[0]?.name
            );
          });
        }
        return names.join('，');
      }
    };

    return {
      ...toRefs(state),
      handleAddEdit,
      beforeUpload,
      downUpload,
      handleUploadSuccess,
      submitOpera,
      handleOpera,
      deleteList,
      handleRemove,
      handleExceed,
      drageHeader,
      getNameByid,
      handleSizeChange,
      getNamesByid,
      onSubmit,
      getPermissionBtn,
      formatDate,
      getList,
      tableKey,
      getTypeDictionary,
      getLanguageType,
      handleImgPreview,
      handleImgRemove,
      changeType,
      filterTypes,
      colWidth
    };
  },
  computed: {},
  created() {}
};
</script>
<style lang="scss" scoped>
.header-btn-group {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 20px;
}
.el-upload__tip {
  display: inline-block;
  margin-left: 10px;
}

:deep(.el-table.format-height-table) {
  .el-table__body-wrapper {
    max-height: calc(100vh - 355px);
  }
}
.img-upload {
  :deep(.el-upload--picture-card) {
    width: 100px;
    height: 100px;
    line-height: 99px;
  }
  :deep(.el-upload-list--picture-card) {
    .el-upload-list__item {
      width: 100px;
      height: 100px;
    }
  }
}
</style>
