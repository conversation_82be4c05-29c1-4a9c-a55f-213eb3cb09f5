<template>
  <!-- 成品检验合格证 -->
  <div class="flex flex-col h-full">
    <el-form ref="editFrom" :inline="true" :model="formInline" class="page-searchbar" @submit.prevent>
      <el-form-item prop="param">
        <div style="width: 42vw">
          <CombinationQuery
            :field-list="searchFieldList"
            :field-tip="fieldTips"
            @get-single-text="getSingleText"
            @get-param-list="getParamList"
            @reset-search="reset"
          />
        </div>
      </el-form-item>
    </el-form>
    <div class="flex-1 overflow-auto">
      <div class="flex gap-5 flex-col px-6 py-5 h-full">
        <div class="page-model">
          <div class="flex flex-row justify-between pb-3">
            <el-radio-group v-model="formInline.status" size="small" @change="getList()">
              <el-radio-button label="">全部</el-radio-button>
              <el-radio-button v-for="(val, key) in radioData" :key="key" :label="key">{{ val }}</el-radio-button>
            </el-radio-group>
          </div>
          <el-table
            ref="singleTableRef"
            :key="tableKey"
            v-loading="listLoading"
            highlight-current-row
            :data="tableList"
            size="medium"
            fit
            border
            height="auto"
            class="dark-table base-table"
            :row-style="
              () => {
                return 'cursor: pointer';
              }
            "
            @header-dragend="drageHeader"
            @current-change="handleCurrentChange"
          >
            <template v-for="(item, index) in tableColumn" :key="index">
              <el-table-column
                :prop="item.fieldKey"
                :label="item.fieldName"
                :sortable="Number(item.isSortable) === 1"
                :width="item.isMinWidth ? '' : item.columnWidth"
                :min-width="item.isMinWidth ? item.columnWidth : ''"
                show-overflow-tooltip
              >
                <template #default="{ row }">
                  <template v-if="item.fieldType === fieldTypesEnum.Person">
                    <UserTag :name="getNameByid(row[item.fieldKey]) || row[item.fieldKey] || '--'" />
                  </template>
                  <template v-else-if="item.fieldType === fieldTypesEnum.Status">
                    <el-tag size="small" effect="dark" :type="status[item.fieldKey][row[item.fieldKey]].type">{{
                      status[item.fieldKey][row[item.fieldKey]].label
                    }}</el-tag>
                  </template>
                  <template v-else-if="item.fieldType === fieldTypesEnum.Date">
                    <span>{{ row[item.fieldKey] == '' ? '--' : formatDate(row[item.fieldKey]) }}</span>
                  </template>
                  <template v-else-if="item.fieldType === fieldTypesEnum.Custom">
                    <span v-if="item.fieldKey == 'materialCode'">
                      {{ materialCategoryAll[row[item.fieldKey]]?.name || row[item.fieldKey] || '--' }}
                    </span>
                    <template v-else-if="item.fieldKey == 'nuclearMarker'">
                      <span v-if="row.nuclearMarker">
                        {{ row.nuclearMarker.indexOf('H') > -1 ? '⚠H' : '' }}
                        {{ row.nuclearMarker.indexOf('空') > -1 ? '©' : '' }}
                      </span>
                      <span v-else>--</span>
                    </template>
                  </template>
                  <span v-else>{{ row[item.fieldKey] || '--' }}</span>
                </template>
              </el-table-column>
            </template>
          </el-table>
          <pagination
            v-show="total > 0"
            :page="listQuery.page"
            :limit="listQuery.limit"
            :total="total"
            @pagination="getList"
          />
        </div>
        <PrintTable :select="selectRow" :certificate-templates="originCertificateTemplates" />
      </div>
    </div>
  </div>
</template>

<script>
import { reactive, ref, toRefs, onMounted } from 'vue';
import router from '@/router/index.js';
import Pagination from '@/components/Pagination';
// import ListLayout from '@/components/ListLayout';
import { formatDate } from '@/utils/formatTime';
import UserTag from '@/components/UserTag';
import { getNamesByid, getNameByid, getPermissionBtn } from '@/utils/common';
import { drageHeader } from '@/utils/formatTable';
import { colWidth } from '@/data/tableStyle';
import { getLoginInfo } from '@/utils/auth';
import { mapGetters } from 'vuex';
import CombinationQuery from '@/components/CombinationQuery';
// import TableColumnView from '@/components/TableColumnView';
import { fieldTypesEnum, columnFixedTypesEnum } from '@/components/TableColumnView/enum';
import { getDictionary } from '@/api/user';
import PrintTable from './components/print-table.vue';
import { certificateList, getSaleCertification } from '@/api/material';
import { tableColumn } from './tableColumn';

export default {
  name: 'FinishedProductCertificate',
  // Pagination, ListLayout, UserTag, DialogBatchDetermin,
  components: { CombinationQuery, Pagination, UserTag, PrintTable },
  setup() {
    const state = reactive({
      singleTableRef: ref(),
      fieldTips: '',
      selectRow: {}, // 选中的销售订单
      dialogVisible: false,
      ruleForm: ref(),
      searchFieldList: [],
      originCertificateTemplates: [],
      materialCategoryAll: {},
      listLoading: false,
      radioData: {
        1: '未打印',
        2: '已打印',
        3: '待入库',
        4: '已入库'
      },
      status: {
        submitStatus: {
          0: { type: 'warning', label: '未送检' },
          1: { type: 'success', label: '已送检' }
        },
        isDeleted: {
          0: { type: 'warning', label: '未删除' },
          1: { type: 'success', label: '已删除' }
        }
      },
      listQuery: {
        limit: 20,
        page: 1
      },
      formInline: {
        param: '',
        tableQueryParamList: []
      },
      dictionary: {
        HGZSCPD: {
          enable: {},
          all: {}
        }
      },
      tableList: [],
      oldRow: {}, // 右侧选中的行数据（修改之前）
      tableLeft: [],
      tableSelected: [], // 表格选中的值
      dialogFormVisible: false,
      total: 0
    });

    const tableKey = ref(0);
    const tableKeyLeft = ref(0);
    const getList = query => {
      const params = { ...state.formInline };
      if (query && query.page) {
        params.page = query.page.toString();
        params.limit = query.limit.toString();
        state.listQuery.page = query.page;
        state.listQuery.limit = query.limit;
      } else {
        state.listQuery.page = 1;
        params.page = '1';
        params.limit = state.listQuery.limit.toString();
      }
      state.listLoading = true;
      getSaleCertification(params).then(res => {
        state.listLoading = false;
        if (res) {
          state.total = res.data.data.totalCount;
          state.tableList = res.data.data.list;
          state.listQuery.page = Number(params.page);
          if (state.tableList) {
            state.singleTableRef.setCurrentRow(state.tableList[0]);
          }
        }
      });
    };
    getList();
    const getDictionaryList = () => {
      Object.keys(state.dictionary).forEach(async item => {
        const response = await getDictionary(item);
        if (response) {
          state.dictionary[item] = { enable: {}, all: {} };
          response.data.data.dictionaryoption.forEach(optionItem => {
            if (optionItem.status == 1) {
              state.dictionary[item].enable[optionItem.code] = optionItem.name;
            }
            state.dictionary[item].all[optionItem.code] = optionItem.name;
          });
        }
      });
    };
    getDictionaryList();

    const handleSizeChange = val => {
      state.listQuery.limit = val;
      getList();
    };

    const reset = () => {
      state.formInline = {
        param: '',
        mateType: '',
        reviewerId: getLoginInfo().accountId, // 审核人员
        submitTime: [],
        reviewTime: [],
        ownerId: '', // 试验员
        tableQueryParamList: []
      };
      getList();
    };
    const handleCheckOpera = row => {
      router.push({
        path: '/recordReviewDetail',
        query: {
          samplesId: row.samplesId,
          capabilityId: row.capabilityId
        }
      });
    };
    const handleSelectionChange = val => {
      state.tableSelected = val;
    };
    const handleAudit = () => {};
    /** 批量判定 */
    const handleDetermin = () => {
      state.dialogVisible = true;
    };
    const handleDetail = row => {
      router.push({
        path: '/recordReview/sample/detail',
        query: {
          orderId: row.orderId,
          sampleId: row.samplesId
        }
      });
    };

    const handleCurrentChange = row => {
      state.selectRow = JSON.parse(JSON.stringify(row));
    };
    const getSingleText = val => {
      state.formInline.param = val;
      state.formInline.tableQueryParamList = [];
      getList();
    };

    const getParamList = paramList => {
      state.formInline.tableQueryParamList = paramList;
      state.formInline.param = '';
      getList();
    };
    // 编辑
    const handleEdit = index => {
      state.oldRow = JSON.parse(JSON.stringify(state.tableList[index]));
      state.tableList[index].isEdit = true;
    };
    // 入库
    const handleWarehouse = row => {
      console.log(row);
    };
    // 保存
    const handleSave = row => {
      console.log(row);
    };
    // 取消保存
    const handleCancle = index => {
      state.tableList[index] = JSON.parse(JSON.stringify(state.oldRow));
      state.tableList[index].isEdit = false;
    };

    const onUpdateColumns = columns => {
      tableKey.value = tableKey.value + 1;
      state.fieldTips = tableColumn
        .filter(item => item.isQuery == 1)
        .map(item => item.fieldName)
        .join('/');
    };
    const closeDialog = () => {
      state.dialogVisible = false;
    };
    // 获取打印模板
    const getCertificateTemplates = async () => {
      const res = await certificateList({ page: '1', limit: '-1', categoryId: '' });
      if (res) {
        state.originCertificateTemplates = res.data.data.list.filter(item => item.category === 1 && item.status === 1);
      }
    };
    onMounted(() => {
      getCertificateTemplates();
    });
    return {
      ...toRefs(state),
      closeDialog,
      tableColumn,
      handleEdit,
      handleSave,
      handleCancle,
      handleWarehouse,
      getSingleText,
      getParamList,
      handleCurrentChange,
      getPermissionBtn,
      handleDetail,
      drageHeader,
      getNameByid,
      handleSizeChange,
      getNamesByid,
      handleAudit,
      handleDetermin,
      reset,
      handleSelectionChange,
      handleCheckOpera,
      formatDate,
      getList,
      tableKey,
      tableKeyLeft,
      colWidth,
      onUpdateColumns,
      fieldTypesEnum,
      columnFixedTypesEnum
    };
  },
  computed: {
    ...mapGetters(['tenantGroup', 'tenantInfo'])
  }
};
</script>
<style lang="scss" scoped>
.page-searchbar {
  background: #fff;
  display: flex;
  justify-content: space-between;
  padding: 0 20px;
}
.page-model {
  background: #fff;
  padding: 20px 20px 0 20px;
  overflow: auto;
  flex: 1;
  display: flex;
  flex-direction: column;
}
:deep(.format-height-table2) {
  .el-table__body-wrapper {
    max-height: calc(100vh - 47.5rem) !important;
    overflow-y: auto;
  }
}
</style>
