<template>
  <el-drawer
    :model-value="drawer"
    title="预览报告"
    direction="rtl"
    :before-close="handleClose"
    size="100%"
    destroy-on-close
    custom-class="word-to-html"
  >
    <input ref="upload" type="file" accept=".docx" class="uploadBtn" @change="readFileAsArrayBuffer" />
    <!-- v-html="htmlContant" -->
    <div ref="htmlDoc" v-loading="loadingWord" class="html-contant" />
    <!-- <iframe ref="fileViewIframe" style="width: 100%; height:800px; border: 5px solid #80808021;" src="" /> -->
  </el-drawer>
</template>

<script>
import { ref, watch, reactive, toRefs } from 'vue';
import mammoth from 'mammoth';
import { exportWord } from '@/api/testReport';
import { ElMessage } from 'element-plus';
import { renderAsync } from 'docx-preview';
// import $ from 'jquery'
// import docx from 'docx-preview'
export default {
  name: 'WordToHtml',
  props: {
    drawer: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['close'],
  setup(props, context) {
    // console.log(props)
    const upload = ref();
    const datas = reactive({
      showDrawer: false,
      htmlContant: null,
      loadingWord: false,
      fileViewIframe: ref(),
      htmlDoc: ref()
    });

    const handleClose = () => {
      datas.showDrawer = false;
      context.emit('close', false);
    };

    watch(
      () => props.drawer,
      newValue => {
        console.log(newValue);
        datas.showDrawer = newValue;
        if (newValue) {
          console.log(props);
          downloadReport(props.data);
        }
      },
      { deep: true }
    );

    // 下载报告
    const downloadReport = row => {
      // console.log(row)
      datas.loadingWord = true;
      datas.htmlContant = null;
      exportWord(row.id, row.mateType, row.sampleId).then(res => {
        console.log(res);
        if (res !== false) {
          const blob = res.data;
          console.log(blob);
          var fileSRC = res.request.responseURL + '/' + decodeURI(res.headers.filename);
          console.log(fileSRC);
          if (blob.size === 0) {
            datas.loadingWord = false;
            ElMessage.warning('报告加载失败！size = 0');
          }
          var reader = new FileReader();
          reader.addEventListener('loadend', function (e) {
            console.log(e.currentTarget.result);
            const arrayBuffer = e.currentTarget.result;
            renderAsync(arrayBuffer, datas.htmlDoc);
            datas.loadingWord = false;
          });
          // reader.readAsDataURL(blob)
          reader.readAsArrayBuffer(blob);
        } else {
          datas.loadingWord = false;
        }
      });
    };

    return {
      ...toRefs(datas),
      handleClose,
      upload,
      downloadReport
    };
  },
  methods: {
    // 把image转换为canvas对象
    imgToCanvas(image) {
      console.log(image);
      var canvas = document.createElement('canvas');
      canvas.width = image.width;
      canvas.height = image.height;
      canvas.getContext('2d').drawImage(image, 0, 0);
      // anvas转换为image
      var array = ['image/webp', 'image/jpeg', 'image/png'];
      var src = canvas.toDataURL(array[2]);
      console.log(src);
      return src;
    },
    // 监听input change事件，读取文件转ArrayBuffer
    readFileAsArrayBuffer(e) {
      const that = this;
      const file = e.target.files[0];
      const reader = new FileReader();

      reader.readAsArrayBuffer(file);
      // console.log($('.html-contant'))
      reader.onload = function (event) {
        const arrayBuffer = event.target['result'];
        // console.log(arrayBuffer)
        renderAsync(arrayBuffer, that.htmlDoc);
        // that.wordToHtml(arrayBuffer)
      };
    },
    // base64 转 blob, mime为类型，base64为,后的内容
    base64ToBlob: (base64, mimeString) => {
      const byteString = window.atob(base64);
      const bufferObj = new ArrayBuffer(byteString.length);
      const uintObj = new Uint8Array(bufferObj);
      for (let i = 0; i < byteString.length; i++) {
        uintObj[i] = byteString.charCodeAt(i);
      }
      return new Blob([uintObj], { type: mimeString });
    },
    // word转HTML
    wordToHtml(arrayBuffer) {
      const that = this;
      // 转换配置，把base64图片转二进制
      // console.log(arrayBuffer)
      const options = {
        styleMap: [
          "p[style-name='Section Title'] => h1.section-title:fresh",
          "p[style-name='Subsection Title'] => h2:fresh",
          "p[style-name='Aside Heading'] => div.aside > h2:fresh"
        ],
        ignoreEmptyParagraphs: false,
        convertImage: mammoth.images.imgElement(image => {
          return image.read('base64').then(async imageBuffer => {
            // const res = await that.uploadImage(imageBuffer, image.contentType)
            // console.log(res)
            if (image.contentType === 'image/x-wmf') {
              console.log(image.contentType);
              // image.contentType = 'image/png'
              // that.imgToCanvas('data:' + image.contentType + ';base64,' + imageBuffer)
            }
            return {
              src: 'data:' + image.contentType + ';base64,' + imageBuffer
            };
          });
        }),
        transformDocument: element => {
          console.log(element.children);
          return element;
        }
      };
      mammoth.convertToHtml({ arrayBuffer }, options).then(docx => {
        console.log('最终结果：', docx);
        console.log(docx.value);
        that.htmlContant = docx.value;
      });
      // mammoth.extractRawText({ arrayBuffer }).then(function(result) {
      //   var text = result.value
      //   var messages = result.messages
      //   console.log(text)
      //   console.log(messages)
      // })
    },
    // 上传图片
    async uploadImage(base64Image, type) {
      const that = this;
      const formData = new FormData();
      formData.append('file', that.base64ToBlob(base64Image, type));
      console.log(base64Image);
      // console.log(that.base64ToBlob(base64Image, type))
      // return await axios({
      //   method: 'post',
      //   url: '/api/api/base-service/file/uploadFile',
      //   data: formData,
      //   config: { headers: { 'Content-Type': 'multipart/form-data' }}
      // })
    }
  }
};
</script>
<style lang="scss" scoped>
.uploadBtn {
  width: 100%;
  margin-left: 10px;
  margin-bottom: 10px;
}
.html-contant {
  height: 800px;
  overflow: hidden auto;
  padding: 0px 10px;
  border-width: 1px !important;
  :deep(table) {
    width: 100%;
    border-spacing: 0px;
    border-bottom: 0.5px solid black;
    td {
      border-left: 0.5px solid black;
      border-top: 0.5px solid black;
      border-width: 1px !important;
      p {
        strong {
          line-height: 20px;
        }
      }
    }
    td:last-child {
      border-width: 1px;
      border-right: 0.5px solid black;
    }
    th {
      border: 0.5px solid black;
      border-bottom: 0px;
      border-top: 1px solid black;
    }
  }
  :deep(ol) {
    text-align: left;
    li {
      height: 25px;
    }
  }
  :deep(.docx-wrapper) {
    background: #ffffff;
    section {
      article {
        p {
          line-height: 20px;
        }
      }
    }
  }
}
</style>
