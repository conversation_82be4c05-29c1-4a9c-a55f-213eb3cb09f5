<template>
  <div class="addRecord">
    <el-dialog
      v-model="dialogShow"
      :title="dialogTitle"
      :close-on-click-modal="false"
      width="1000px"
      custom-class="MaterialItem_dialog"
      @close="cancel"
    >
      <div class="searchInput">
        <el-input
          ref="inputRef"
          v-model="content"
          v-trim
          placeholder="请输编号/名称进行搜索"
          clearable
          size="small"
          @keyup.enter="searchTable"
        >
          <template #prefix>
            <i class="el-input__icon el-icon-search" />
          </template>
        </el-input>
        <el-button type="primary" size="small" @click="searchTable">查询</el-button>
      </div>
      <el-table
        ref="singleTable"
        v-loading="singleLoading"
        :data="tableData"
        size="medium"
        highlight-current-row
        border
        height="auto"
        style="width: 100%"
        class="dark-table base-table add-process-table"
        @row-click="handleRowClick"
        @current-change="changeRadio"
      >
        <el-table-column type="index" label="选择" width="70" align="center">
          <template #default="{ row }">
            <el-radio v-model="row.radio" :label="row.id" @change="changeRadio(row)">{{ '' }}</el-radio>
          </template>
        </el-table-column>
        <el-table-column label="物料编号" property="no" min-width="50">
          <template #default="{ row }">
            {{ row.no || '--' }}
          </template>
        </el-table-column>
        <el-table-column label="物料名称" property="name" min-width="100" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="nowrap">{{ row.name || '--' }}</div>
          </template>
        </el-table-column>
      </el-table>
      <pagination :page="page" :limit="limit" :total="total" @pagination="changePage" />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
          <el-button type="primary" @click="onSubmit" @keyup.prevent @keydown.enter.prevent>确 认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { reactive, toRefs, ref, watch, getCurrentInstance, nextTick } from 'vue';
import { getWlItemNew } from '@/api/mas';
import Pagination from '@/components/Pagination';
export default {
  name: 'MaterialItem',
  components: { Pagination },
  props: {
    dialogVisiable: {
      type: Boolean,
      default: false
    },
    isAdd: {
      type: Boolean,
      default: false
    },
    detailData: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['closeDialog', 'selectRow'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    watch(props, newValue => {
      state.dialogShow = newValue.dialogVisiable;
      if (state.dialogShow) {
        state.isAdd = props.isAdd;
        state.content = '';
        state.page = 1;
        state.limit = 20;
        state.detailData = props.detailData;
        state.selectedRow = {};
        if (state.isAdd) {
          state.dialogTitle = '添加物料';
        } else {
          state.dialogTitle = '选择物料';
        }
        getTableList({ limit: state.limit.toString(), groupNo: state.detailData.materialGroupNo || '', page: '1' });
        nextTick(() => {
          state.inputRef.focus();
        });
      }
    });
    const state = reactive({
      dialogShow: false,
      content: '',
      inputRef: ref(),
      dialogTitle: '',
      page: 1,
      limit: 20,
      total: 0,
      singleLoading: false,
      detailData: {}, // 详情
      isAdd: props.isAdd,
      singleTable: ref(),
      selectedRow: {}, // 选中的行数据
      tableData: []
    });
    // 获取物料列表接口
    const getTableList = params => {
      state.singleLoading = true;
      getWlItemNew(params).then(res => {
        state.singleLoading = false;
        state.tableData = res.data.data.list;
        state.total = res.data.data.totalCount;
      });
    };
    const changePage = value => {
      state.limit = value.limit;
      state.page = value.page;
      var params = {
        content: state.content,
        limit: state.limit.toString(),
        page: state.page.toString(),
        groupNo: state.detailData.materialGroupNo || ''
      };
      getTableList(params);
    };
    const changeRadio = row => {
      state.selectedRow = {
        materialId: row.id,
        materialCode: row.no,
        materialName: '',
        materialGroupId: row.materialGroupId,
        materialGroupNo: row.materialGroupNo,
        materialGroupName: row.materialGroupNo + '-' + row.materialGroupName
      };
      if (row.name && row.no) {
        state.selectedRow.materialName = row.no + '-' + row.name;
      } else if (row.name && !row.no) {
        state.selectedRow.materialName = row.name;
      } else if (!row.name && row.no) {
        state.selectedRow.materialName = row.no;
      }
      if (row.materialGroupNo && row.materialGroupName) {
        state.selectedRow.materialGroupName = row.materialGroupNo + '-' + row.materialGroupName;
      } else if (row.materialGroupName && !row.materialGroupNo) {
        state.selectedRow.materialGroupName = row.materialGroupName;
      } else if (!row.materialGroupName && row.materialGroupNo) {
        state.selectedRow.materialGroupName = row.materialGroupNo;
      }
      state.tableData.forEach(item => {
        if (item.id !== row.id) {
          item.radio = false;
        }
      });
    };
    const searchTable = () => {
      var params = {
        limit: state.limit.toString(),
        content: state.content,
        page: '1',
        groupNo: state.detailData.materialGroupNo || ''
      };
      getTableList(params);
    };
    const onSubmit = () => {
      if (state.selectedRow.materialId) {
        state.dialogShow = false;
        context.emit('selectRow', state.selectedRow);
      } else {
        proxy.$message.warning('请选择物料');
      }
    };
    const cancel = () => {
      state.dialogShow = false;
      context.emit('closeDialog', false);
    };
    const handleRowClick = row => {
      changeRadio(row);
      state.tableData.forEach(item => {
        if (item.id === row.id) {
          item.radio = row.id;
        }
      });
    };
    return {
      ...toRefs(state),
      onSubmit,
      handleRowClick,
      getTableList,
      changePage,
      searchTable,
      changeRadio,
      cancel
    };
  }
};
</script>

<style lang="scss" scoped>
.searchInput {
  width: 100%;
  margin-bottom: 15px;
  display: inline-block;
  .el-input {
    margin-right: 8px;
    width: 360px;
  }
}
.nowrap {
  overflow: hidden; // 超出的文本隐藏
  text-overflow: ellipsis; // 溢出用省略号显示
  white-space: nowrap; // 溢出不换行
}
</style>
<style lang="scss">
.MaterialItem_dialog {
  .add-process-table {
    .el-table thead th {
      background-color: #f6f6f6;
    }
    .el-table__body-wrapper {
      max-height: calc(100vh - 34.5rem);
      overflow-y: auto;
      overflow-x: hidden !important;
    }
  }
  .el-table thead th {
    background: #f6f6f6;
  }
  .el-dialog__body {
    padding-bottom: 0;
  }
}
</style>
