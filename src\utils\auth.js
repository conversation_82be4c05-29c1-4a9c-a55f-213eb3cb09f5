import Cookies from 'js-cookie';

const tokenKey = 'lims-token';
const clientIdKey = 'client-id';
const refreshTokenKey = 'lims-refresh-token';
const loginIdKey = 'loginId';
const loginInfo = 'loginInfo';
const menuList = 'menuList';
const Scale = 'Scale';
const CurrentStage = 'ReportStage';
const CurrentReportInfo = 'CurrentReportInfo';
const currentThemeColor = 'currentThemeColor';
const currentBackgroundColor = 'currentBackgroundColor';
const currentBackgroundColor2 = 'currentBackgroundColor2';
const hasWebSocket = 'hasWebSocket';
const MSGLIST = 'MsgList';
const URLCONFIG = 'URLConfig';
const permissionRouterList = 'PermissionRouterList';
const sysConfig = 'SysConfig';
const tenantConfig = 'TenantConfig';
const LIMSConfig = 'LIMSConfig';

const initToken = 'Basic d2ViQXBwOndlYkFwcA==';
const ClientId = 'webApp';

export { initToken, ClientId };

export function getToken() {
  return Cookies.get(tokenKey);
}

export function setToken(token) {
  return Cookies.set(tokenKey, token);
}

export function removeToken() {
  return Cookies.remove(tokenKey);
}

/** 获取 ClientId */
export function getClientId() {
  return Cookies.get(clientIdKey);
}

/** 设置 ClientId */
export function setClientId(clientId) {
  Cookies.set(clientIdKey, clientId);
}

/** 移除 ClientId */
export function removeClientId() {
  Cookies.remove(clientIdKey);
}

/** 获取 LoginId */
export function getLoginId() {
  return Cookies.get(loginIdKey);
}

/** 设置 LoginId */
export function setLoginId(loginId) {
  Cookies.set(loginIdKey, loginId);
}

/** 移除 LoginId */
export function removeLoginId() {
  Cookies.remove(loginIdKey);
}

export function setLoginInfo(userInfo) {
  return Cookies.set(loginInfo, userInfo);
}

export function getLoginInfo() {
  // console.log(Cookies.get(loginInfo))
  if (Cookies.get(loginInfo)) {
    return JSON.parse(Cookies.get(loginInfo));
  } else {
    removeToken();
    return null;
  }
}

export function removeLoginInfo() {
  return Cookies.remove(loginInfo);
}

export function setRefreshToken(token) {
  return localStorage.setItem(refreshTokenKey, token);
}

export function getRefreshToken() {
  return localStorage.getItem(refreshTokenKey);
}

export function removeRefreshToken() {
  return localStorage.removeItem(refreshTokenKey);
}

export function setMenuList(list) {
  return localStorage.setItem(menuList, JSON.stringify(list));
}

export function getMenuList() {
  if (localStorage.getItem(menuList)) {
    return JSON.parse(localStorage.getItem(menuList));
  } else {
    return [];
  }
}

export function removeMenuList() {
  return localStorage.removeItem(menuList);
}

export function getScale() {
  return Cookies.get(Scale);
}
export function setScale(scale) {
  return Cookies.set(Scale, scale);
}
export function removeScale() {
  return Cookies.remove(Scale);
}
// 当前报告阶段
export function getCurrentStage() {
  return Cookies.get(CurrentStage);
}
export function setCurrentStage(currentStage) {
  return Cookies.set(CurrentStage, currentStage);
}
export function removeCurrentStage() {
  return Cookies.remove(CurrentStage);
}
// 当前检测报告信息
export function setCurrentReportInfo(info) {
  return Cookies.set(CurrentReportInfo, info);
}
export function getCurrentReportInfo() {
  if (Cookies.get(CurrentReportInfo)) {
    return JSON.parse(Cookies.get(CurrentReportInfo));
  } else {
    return null;
  }
}
export function removeCurrentReportInfo() {
  return Cookies.remove(CurrentReportInfo);
}
// 主题颜色
export function setThemeColor(color) {
  return localStorage.setItem(currentThemeColor, color);
}
export function getThemeColor() {
  if (localStorage.getItem(currentThemeColor)) {
    return localStorage.getItem(currentThemeColor);
  } else {
    return null;
  }
}
export function removeThemeColor() {
  return localStorage.removeItem(currentThemeColor);
}
// 主体背景颜色
export function setBackgroundColor(color) {
  return localStorage.setItem(currentBackgroundColor, color);
}
export function getBackgroundColor() {
  if (localStorage.getItem(currentBackgroundColor)) {
    return localStorage.getItem(currentBackgroundColor);
  } else {
    return null;
  }
}
export function removeBackgroundColor() {
  return localStorage.removeItem(currentBackgroundColor);
}
// 主体背景颜色2
export function setBackgroundColor2(color) {
  return localStorage.setItem(currentBackgroundColor2, color);
}
export function getBackgroundColor2() {
  if (localStorage.getItem(currentBackgroundColor2)) {
    return localStorage.getItem(currentBackgroundColor2);
  } else {
    return null;
  }
}
export function removeBackgroundColor2() {
  return localStorage.removeItem(currentBackgroundColor2);
}
// websocket 是否手动关闭
export function getHasWebSocket() {
  return Cookies.get(hasWebSocket);
}
export function setHasWebSocket(flag) {
  return Cookies.set(hasWebSocket, flag);
}
export function removeHasWebSocket() {
  return Cookies.remove(hasWebSocket);
}
// 消息列表
export function getMsgList() {
  if (localStorage.getItem(MSGLIST)) {
    return JSON.parse(localStorage.getItem(MSGLIST));
  } else {
    return [];
  }
}
// 系统配置
export function getLIMSConfig() {
  if (localStorage.getItem(LIMSConfig)) {
    return JSON.parse(localStorage.getItem(LIMSConfig));
  } else {
    return {};
  }
}
export function setMsgList(msg) {
  const newMsg = JSON.stringify(msg);
  return localStorage.setItem(MSGLIST, newMsg);
}
export function removeMsgList() {
  return localStorage.removeItem(MSGLIST);
}

export function getURLConfig() {
  if (Cookies.get(URLCONFIG)) {
    return JSON.parse(Cookies.get(URLCONFIG));
  } else {
    return null;
  }
}
export function setURLConfig(msg) {
  return Cookies.set(URLCONFIG, msg);
}
export function removeURLConfig() {
  return Cookies.remove(URLCONFIG);
}
// 动态路由
export function getPermissionRouterList() {
  if (localStorage.getItem(permissionRouterList)) {
    return JSON.parse(localStorage.getItem(permissionRouterList));
  } else {
    return [];
  }
}
export function setPermissionRouterList(list) {
  const newList = JSON.stringify(list);
  return localStorage.setItem(permissionRouterList, newList);
}
export function removePermissionRouterList() {
  return localStorage.removeItem(permissionRouterList);
}
// 系统配置
export function getSysConfig() {
  if (localStorage.getItem(sysConfig)) {
    return JSON.parse(localStorage.getItem(sysConfig));
  } else {
    return null;
  }
}
export function getTenantConfig() {
  if (localStorage.getItem(tenantConfig)) {
    return JSON.parse(localStorage.getItem(tenantConfig));
  } else {
    return null;
  }
}
export function setSysConfig(config) {
  const newData = JSON.stringify(config);
  return localStorage.setItem(sysConfig, newData);
}
export function setTenantConfig(config) {
  const newData = JSON.stringify(config);
  return localStorage.setItem(tenantConfig, newData);
}
export function setLIMSConfig(config) {
  const newData = JSON.stringify(config);
  return localStorage.setItem(LIMSConfig, newData);
}
export function removeSysConfig() {
  return localStorage.removeItem(sysConfig);
}
export function removeSetTenantConfig() {
  return localStorage.removeItem(tenantConfig);
}
