<template>
  <!-- 设备台账列表 -->
  <ListLayout :has-left-panel="true" :aside-panel-width="asidePanelWidth" :aside-max-width="520">
    <template #search-bar>
      <div class="searchInput">
        <el-input
          v-model="param"
          v-trim
          v-focus
          class="ipt-360"
          placeholder="请输入编号/设备名称/型号规格"
          clearable
          size="large"
          @keyup.enter="getTableList"
        >
          <template #prefix>
            <i class="el-input__icon el-icon-search" />
          </template>
        </el-input>
        <el-button type="primary" size="large" @click="getTableList">查询</el-button>
        <el-button size="large" @click="reset">重置</el-button>
        <el-button class="searchBtn" size="large" type="text" @click="search" @keyup.prevent @keydown.enter.prevent>
          高级搜索
          <i class="el-icon--right" :class="[showPanel ? 'el-icon-arrow-up' : 'el-icon-arrow-down']" />
        </el-button>
      </div>
    </template>
    <template #button-group>
      <el-button
        v-if="getPermissionBtn('importEquipmentList')"
        class="fr"
        size="large"
        @click="handleImport()"
        @keyup.prevent
        @keydown.enter.prevent
        >导入</el-button
      >
      <el-dropdown v-if="getPermissionBtn('exportEquipmentList')" class="mx-3" @command="handleExport">
        <el-button size="large"> 导出 </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="select">导出选中数据</el-dropdown-item>
            <el-dropdown-item command="all">导出全部</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
      <el-button
        v-if="getPermissionBtn('downLoadAttachment')"
        class="fr"
        size="large"
        @click="handleDownFile()"
        @keyup.prevent
        @keydown.enter.prevent
        >下载附件</el-button
      >
      <el-button
        v-if="getPermissionBtn('addEquipment')"
        class="fr"
        type="primary"
        size="large"
        icon="el-icon-plus"
        @click="handleAddUnit"
        @keyup.prevent
        @keydown.enter.prevent
        >新增仪器设备</el-button
      >
    </template>
    <template #search-panel>
      <el-collapse v-model="activePanelName" class="search-collapse">
        <el-collapse-item name="1">
          <el-form ref="searchRef" :model="searchForm" class="searchLeft">
            <el-form-item label="计量截止有效期：">
              <el-date-picker
                v-model="submitTime"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                size="small"
                :shortcuts="shortcuts"
                @change="handleDatePicker"
              />
            </el-form-item>
          </el-form>
        </el-collapse-item>
      </el-collapse>
    </template>
    <template #page-left-side>
      <div class="tree-container">
        <div class="tree-header">
          <el-input
            v-model="otherForm.filterText"
            size="small"
            placeholder="请输入分类名称"
            prefix-icon="el-icon-search"
          />
          <el-button
            v-if="getPermissionBtn('addEquipmentTreeBtn')"
            size="small"
            icon="el-icon-plus"
            class="addTreeBtn"
            @click="addTreeItem"
            @keyup.prevent
            @keydown.enter.prevent
          />
        </div>
        <div class="tree-content">
          <el-tree
            ref="refTree"
            :data="otherForm.treeData"
            node-key="id"
            :props="otherForm.defaultProps"
            :expand-on-click-node="false"
            :highlight-current="true"
            :filter-node-method="filterNode"
            class="leftTree"
            draggable
            :allow-drop="allowDrop"
            @node-drop="nodeDrop"
            @filter="filterNode"
            @node-click="clickNode"
          >
            <template #default="{ node, data }">
              <span class="tree-node">{{ node.label }}</span>
              <el-dropdown
                v-if="
                  (getPermissionBtn('delEquipmentTreeBtn') || getPermissionBtn('editEquipmentTreeBtn')) &&
                  data.id !== 'all'
                "
                trigger="hover"
                :class="node.showIcon ? 'icon-show' : ''"
                class="tree-dropdown el-icon"
                @visible-change="changeIcon(node.showIcon, node)"
              >
                <i class="el-icon-more" />
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item v-if="getPermissionBtn('editEquipmentTreeBtn')" @click="editTree(node.data, node)"
                      ><i class="iconfont tes-edit" />编辑</el-dropdown-item
                    >
                    <el-dropdown-item
                      v-if="getPermissionBtn('delEquipmentTreeBtn')"
                      class="color-red"
                      @click="delTree(node.data)"
                      ><i class="iconfont tes-delete" />删除</el-dropdown-item
                    >
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-tree>
        </div>
      </div>
    </template>
    <template #radio-content>
      <el-radio-group v-model="searchForm.status" size="small" @change="getTableList">
        <el-radio-button label="">全部</el-radio-button>
        <el-radio-button v-for="(value, key, index) in dictionaryJSON['24'].enable" :key="index" :label="key">{{
          value
        }}</el-radio-button>
      </el-radio-group>
      <el-checkbox v-model="expired" label="已过期" class="ygq" @change="handlExpired" />
    </template>
    <el-table
      ref="tableRef"
      :key="tableKey"
      v-loading="tableLoading"
      :data="tableData"
      fit
      border
      height="auto"
      :size="otherForm.tableSize"
      highlight-current-row
      class="dark-table format-height-table base-table"
      :row-style="
        () => {
          return 'cursor: pointer';
        }
      "
      @header-dragend="drageHeader"
      @sort-change="handleSortChange"
      @row-click="handleRowClick"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" :width="colWidth.checkbox" label="全选" fixed="left" />
      <el-table-column label="序号" :width="colWidth.serialNo" align="center">
        <template #default="scope">{{ scope.$index + 1 }}</template>
      </el-table-column>
      <el-table-column label="仪器设备编号" prop="deviceNumber" :width="180" sortable show-overflow-tooltip>
        <template #default="{ row }">
          <div class="nowrap">{{ row.deviceNumber || '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column label="仪器设备名称" prop="name" :width="colWidth.name" show-overflow-tooltip>
        <template #default="{ row }">
          <div class="nowrap">{{ row.name || '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column label="设备管理编号" prop="deviceManagementNumber" :width="colWidth.name" show-overflow-tooltip>
        <template #default="{ row }">
          <div class="nowrap">{{ row.deviceManagementNumber || '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column label="型号规格" prop="model" :width="colWidth.model" show-overflow-tooltip>
        <template #default="{ row }">
          <div class="nowrap">{{ row.model || '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column label="保管人" prop="custodianId" :width="colWidth.person" show-overflow-tooltip>
        <template #default="{ row }">
          <UserTag v-if="row.custodianId" :name="getNameByid(row.custodianId) || row.custodianId || '--'" />
        </template>
      </el-table-column>
      <el-table-column label="计量截止有效期" prop="validEndDate" :width="190" sortable>
        <template #default="{ row }">
          <span v-if="row.isEquipmentMetering">{{ row.validEndDate ? formatDate(row.validEndDate) : '--' }}</span>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column label="设备状态" prop="status" :width="colWidth.status">
        <template #default="{ row }">
          <el-tag size="small" effect="dark" :type="equipUnitType[row.status]">{{
            dictionaryJSON['24'].all[row.status] || '--'
          }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="存放地" prop="deviceLocation" :width="colWidth.name" show-overflow-tooltip>
        <template #default="{ row }">
          <div class="nowrap">{{ dictionaryJSON['cfdd'].all[row.deviceLocation] || row.deviceLocation || '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column
        v-if="getPermissionBtn('equipmentDetail') || getPermissionBtn('deleteEquipment')"
        label="操作"
        :width="colWidth.operation"
        class-name="fixed-right"
        prop="caozuo"
        fixed="right"
      >
        <template #default="{ row }">
          <span v-if="getPermissionBtn('equipmentDetail')" class="blue-color" @click.stop="handleDetail(row)"
            >查看</span
          >
          <span v-if="getPermissionBtn('deleteEquipment')" class="blue-color" @click.stop="handleDelete(row)"
            >删除</span
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination :page="listQuery.page" :limit="listQuery.limit" :total="total" @pagination="getTableList" />
    <el-dialog
      v-model="dialogImport"
      title="数据导入"
      :close-on-click-modal="false"
      width="680px"
      @close="dialogImport = false"
    >
      <div class="title">为确保数据导入的准确性，请按照以下规则进行导入</div>
      <div class="bt">下载表单模板</div>
      <ul class="uploadRules">
        <li>
          请按照<span class="blue-color" @click="downLoadFile('设备台账导入.xlsx')">设备台账导入.xlsx</span
          >在模板内录入数据
        </li>
        <li>只导入第一张工作表（sheet1）</li>
      </ul>
      <div>
        <el-upload
          ref="uploadRef"
          :action="uploadAction"
          :headers="headerconfig"
          :auto-upload="false"
          :limit="1"
          :on-exceed="handleExceed"
          :before-upload="beforeUpload"
          :on-success="handleFileSuccess"
        >
          <el-button size="small" type="primary" plain>选择上传文件</el-button>
        </el-upload>
      </div>
      <ul class="uploadRules">
        <li>请上传*.xls，*.xlsx格式文件；</li>
        <li>目前一次性最多上传5000条数据；</li>
        <li>文件大小不超过10M；</li>
      </ul>
      <template #footer>
        <span class="dialog-footer">
          <el-button :loading="dialogLoading" @click="dialogImport = false">取 消</el-button>
          <el-button type="primary" @click="submitUpload">确认上传</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 详情抽屉 -->
    <Detail
      :drawer="detailDrawer"
      title="仪器设备详情"
      :dictionary-all="dictionaryJSON"
      :tree-data="otherForm.dialogTreeData"
      :device-id="rowId"
      @close="closeDeatilDrawer"
    />
    <DrawerUnit
      :drawer="unitVisiable"
      :dictionary-all="dictionaryJSON"
      :detail-data="detailData"
      :tree-data="otherForm.dialogTreeData"
      title="新增仪器设备"
      @close="closeDrawer"
    />
    <!-- 下载附件 -->
    <DialogDownFile
      :select-table="tableSelected"
      :dictionary-all="dictionaryJSON"
      :dialog-visible="dialogDownLoad"
      @closeDialog="handleCloseDialog"
    />
    <el-dialog
      v-model="showEditDialog"
      :title="otherForm.isAddTree === true ? '添加分类' : '编辑分类'"
      width="480px"
      :close-on-click-modal="false"
    >
      <el-form
        v-if="showEditDialog"
        ref="formTree"
        :model="dialogFrom"
        :rules="otherForm.dialogRules"
        label-position="right"
        label-width="120px"
      >
        <el-form-item
          label="分类名称："
          prop="name"
          :rules="{
            required: true,
            message: '请输入分类名称',
            trigger: 'change'
          }"
        >
          <el-input v-model.trim="dialogFrom.name" autocomplete="off" maxlength="30" placeholder="请输入分类名称" />
        </el-form-item>
        <el-form-item label="父级分类：">
          <el-cascader
            v-model="dialogFrom.parentId"
            :options="otherForm.dialogTreeData"
            :props="otherForm.categoryProps"
            clearable
            style="width: 100%"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showEditDialog = false">取 消</el-button>
          <el-button v-loading.fullscreen.lock="dialogLoading" type="primary" @click="handleEditTree">确 认</el-button>
        </span>
      </template>
    </el-dialog>
  </ListLayout>
</template>

<script>
import { reactive, ref, watch, onMounted, getCurrentInstance, toRefs } from 'vue';
import Pagination from '@/components/Pagination';
import UserTag from '@/components/UserTag';
import { formatTree, formatAllTree, formatTreeByIds, formatTreeByNames } from '@/utils/formatJson';
import Detail from './detail';
import DrawerUnit from './DrawerUnit';
import {
  getList,
  getTree,
  deleteTreeNode,
  addTreeNode,
  updateTreeNode,
  deleteEquipment,
  updateOrderTree
} from '@/api/equipment';
import { getDictionary } from '@/api/user';
import { deviceImportExcelUploadUrl } from '@/api/uploadAction';
import { colWidth } from '@/data/tableStyle';
import ListLayout from '@/components/ListLayout';
import { getNameByid, getPermissionBtn } from '@/utils/common';
import { formatDate } from '@/utils/formatTime';
import { drageHeader } from '@/utils/formatTable';
import { findTreeNode } from '@/utils/formatJson';
import router from '@/router/index.js';
import _ from 'lodash';
import { getToken } from '@/utils/auth';
// Components
import DialogDownFile from './components/DialogDownFile';

export default {
  name: 'EquipmentParameter',
  components: { ListLayout, UserTag, Pagination, Detail, DrawerUnit, DialogDownFile },
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    // const store = useStore()
    // const route = useRoute()
    const state = reactive({
      asidePanelWidth: 300,
      showPanel: false,
      dialogImport: false,
      dialogDownLoad: false,
      tableRef: ref(),
      activePanelName: '0',
      expired: false, // 已过期
      submitTime: '', // 筛选时间段
      searchForm: {}, // 查询数据
      uploadAction: deviceImportExcelUploadUrl(),
      headerconfig: {
        Authorization: getToken()
      },
      rowId: '', // 行数据id
      unitVisiable: false,
      searchRef: ref(null),
      uploadRef: ref(),
      dialogLoading: false,
      shortcuts: [
        {
          text: '一月内',
          value: (() => {
            const end = new Date();
            const start = new Date();
            end.setTime(start.getTime() + 3600 * 1000 * 24 * 30);
            return [start, end];
          })()
        },
        {
          text: '三月内',
          value: (() => {
            const end = new Date();
            const start = new Date();
            end.setTime(start.getTime() + 3600 * 1000 * 24 * 90);
            return [start, end];
          })()
        }
      ],
      isAdd: false,
      detailData: {}, // 传递给详情页的内容
      tableData: [],
      exportTableData: [], // 导出的数据
      tableSelected: [],
      copyData: {}, // 复制产品的详情
      param: '', // 模糊查询关键字
      total: 0,
      tableLoading: false, // 表格加载的loading
      detailDrawer: false,
      productTitle: '添加产品', // 产品弹出框标题
      isEdit: true, // 详情页的类型
      dialogFrom: {}, // 操作树节点的弹窗表格
      treeTitle: '', // 选中树节点的name
      checkTreeId: '', // 选中的左侧树节点的id
      dictionaryJSON: {
        cfdd: {
          enable: {},
          all: {}
        },
        SBFJLX: {
          enable: {},
          all: {}
        },
        24: {
          enable: {},
          all: {}
        }
      },
      equipUnitType: {
        // 设备状态对应的tag类型
        Running: 'success',
        Standby: 'warning',
        Maintenance: 'default',
        Fault: 'danger',
        Scrapped: 'info'
      },
      listQuery: {
        page: 1,
        limit: 20
      }
    });
    const editFrom = ref(null);
    const activeName = ref('0');
    const showS = ref(true);

    const otherForm = reactive({
      tableList: [],
      list: [],
      treeData: [],
      dialogTreeData: [],
      editData: {},
      newTree: [],
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      category: [],
      categoryProps: {
        expandTrigger: 'hover',
        checkStrictly: true,
        children: 'children',
        label: 'name',
        value: 'id'
      },
      tabsMoreData: [],
      filterText: '',
      isAddTree: true,
      dialogRules: {},
      tableSize: 'medium',
      showIcon: false
    });

    const tableKey = ref(0);
    const getDictionaryAll = async () => {
      Object.keys(state.dictionaryJSON).forEach(async item => {
        const response = await getDictionary(item);
        if (response) {
          state.dictionaryJSON[item] = {
            enable: {},
            all: {}
          };
          response.data.data.dictionaryoption.forEach(valOption => {
            if (valOption.status === 1) {
              state.dictionaryJSON[item].enable[valOption.code] = valOption.name;
            }
            state.dictionaryJSON[item].all[valOption.code] = valOption.name;
          });
        }
      });
    };
    getDictionaryAll();
    const reset = () => {
      state.searchForm = {};
      state.expired = false;
      state.submitTime = '';
      state.param = '';
      state.listQuery.page = 1;
      state.listQuery.limit = 20;
      proxy.getTableList();
    };

    const search = () => {
      state.showPanel = !state.showPanel;
      if (state.activePanelName === '0') {
        state.activePanelName = '1';
      } else {
        state.activePanelName = '0';
      }
    };
    // 试样分组再次选择
    const handleSelectionChange = val => {
      state.tableSelected = val;
    };
    // 行点击
    const handleRowClick = row => {
      state.tableRef.toggleRowSelection(
        row,
        !state.tableSelected.some(item => {
          return row.id === item.id;
        })
      );
    };
    // 下载附件
    const handleDownFile = () => {
      if (!state.tableSelected.length) {
        proxy.$message.warning('请先勾选数据！');
        return;
      }
      state.dialogDownLoad = true;
    };
    // 关闭弹出框
    const handleCloseDialog = isRefresh => {
      state.dialogDownLoad = false;
      if (isRefresh) {
        state.tableRef.clearSelection();
      }
    };
    // 列表排序
    const handleSortChange = data => {
      const { prop, order } = data;
      state.searchForm.orderBy = prop;
      if (order === 'ascending') {
        state.searchForm.isAsc = 'true';
      } else if (order === 'descending') {
        state.searchForm.isAsc = 'false';
      } else {
        state.searchForm.isAsc = '';
      }
      proxy.getTableList();
    };
    // 树节点编辑
    const showEditDialog = ref(false);
    const editTree = (data, node) => {
      otherForm.dialogTreeData = formatAllTree(data.id, otherForm.dialogTreeData);
      showEditDialog.value = true;
      otherForm.isAddTree = false;
      state.dialogFrom = JSON.parse(JSON.stringify(data));
      otherForm.category = formatTreeByIds(node.parent);
    };
    const formTree = ref(null);
    // 新增、编辑时保存树节点
    const handleEditTree = () => {
      formTree.value.validate(valid => {
        if (valid) {
          var parentId = state.dialogFrom.parentId;
          if (state.dialogFrom.parentId instanceof Array) {
            if (state.dialogFrom.parentId.length > 0) {
              parentId = state.dialogFrom.parentId[state.dialogFrom.parentId.length - 1].toString();
            } else {
              parentId = '';
            }
          }
          const params = {
            ...state.dialogFrom,
            parentId: parentId
          };
          if (otherForm.isAddTree) {
            state.dialogLoading = true;
            addTreeNode(params).then(function (res) {
              state.dialogLoading = false;
              if (res.data.code === 200) {
                if (res.data.data.isSuccess) {
                  showEditDialog.value = false;
                  proxy.$message.success('新增成功!');
                  proxy.getLeftTree();
                } else {
                  proxy.$message.error(res.data.data.message);
                }
              } else {
                proxy.$message.error(res.data.message);
              }
            });
          } else {
            state.dialogLoading = true;
            updateTreeNode(params).then(function (res) {
              state.dialogLoading = false;
              if (res.data.code === 200) {
                if (res.data.data.isSuccess) {
                  showEditDialog.value = false;
                  proxy.$message.success('编辑成功!');
                  proxy.getLeftTree();
                } else {
                  proxy.$message.error(res.data.data.message);
                }
              } else {
                proxy.$message.error(res.data.message);
              }
            });
          }
        } else {
          return false;
        }
      });
    };
    // 新增树节点
    const addTreeItem = () => {
      otherForm.dialogTreeData = formatAllTree('', otherForm.dialogTreeData);
      showEditDialog.value = true;
      otherForm.category = [];
      otherForm.isAddTree = true;
      state.dialogFrom = {};
    };
    // 树节点删除
    const delTree = node => {
      proxy
        .$confirm('是否删除该类目', '提示', {
          confirmButtonText: '确认删除',
          cancelButtonText: '取消',
          showCancelButton: true,
          closeOnClickModal: false,
          type: 'warning'
        })
        .then(() => {
          deleteTreeNode(node.id).then(function (res) {
            if (res.data.data.isSuccess) {
              proxy.$message.success('删除成功!');
              if (node.id === state.checkTreeId) {
                state.checkTreeId = '';
              }
              proxy.getLeftTree();
            } else {
              proxy.$message.error(res.data.data.message);
            }
          });
        })
        .catch(() => {});
    };

    const handleAddUnit = (type, row) => {
      if (otherForm.treeData.length === 0) {
        proxy.$message.closeAll();
        proxy.$message.warning('请先在左侧添加分类');
        return false;
      }
      state.detailData = {
        checkTreeId: state.checkTreeId // 当前节点的标准分类
      };
      state.unitVisiable = true;
    };
    // 编辑、查看详情
    const handleDetail = (row, isEdit) => {
      // 带给详情页的产品信息
      state.rowId = row.id;
      state.detailData = {
        productDetail: row, // 产品详情
        checkTreeId: state.checkTreeId, // 当前节点的标准分类
        standardTree: otherForm.treeData // 所有标准分类树节点
      };
      state.detailDrawer = true;
      state.isEdit = isEdit;
    };
    // 删除
    const handleDelete = (row, type) => {
      proxy
        .$confirm('是否确认删除？删除后不可恢复', '确认删除', {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning'
        })
        .then(() => {
          // 删除产品
          state.tableLoading = true;
          deleteEquipment(row.id).then(res => {
            state.tableLoading = false;
            if (res.data.code === 200) {
              if (res.data.data.isSuccess) {
                proxy.$message.success('删除成功');
                proxy.getTableList();
              } else {
                proxy.$message.error(res.data.data.message);
              }
            }
          });
        })
        .catch(() => {});
    };
    const closeDeatilDrawer = val => {
      state.detailDrawer = false;
      // 由于设备使用记录跳转到详情页面，要是关闭页面会重新刷新列表，因此会再次打开详情页面，这边添加传值来做判断，同时在关闭详情页面的时候为了防止刷新，这边路由会替换到原先模块的路由
      proxy.getTableList(null, false);
      router.replace({ query: {} });
    };
    // mounted
    onMounted(() => {});
    // 拖拽
    // 过滤树节点
    const refTree = ref(null);
    watch(
      () => otherForm.filterText,
      newValue => {
        refTree.value.filter(newValue);
      }
    );
    const filterNode = (value, data) => {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    };
    const handleDatePicker = value => {
      if (value) {
        state.expired = false;
        state.searchForm.startTime = formatDate(value[0]);
        state.searchForm.endTime = formatDate(value[1]);
      } else {
        state.searchForm.startTime = '';
        state.searchForm.endTime = '';
      }
    };
    // 已过期
    const handlExpired = value => {
      if (value) {
        state.submitTime = '';
        state.searchForm.startTime = '';
        state.searchForm.endTime = formatDate(new Date());
      } else {
        state.searchForm.endTime = '';
      }
      proxy.getTableList();
    };
    // 选择物资
    const changeIcon = (command, node) => {
      if (!command) {
        node.showIcon = true;
      } else {
        node.showIcon = !node.showIcon;
      }
    };
    const closeDrawer = value => {
      if (value.isRefresh) {
        proxy.getTableList();
      }
      state.unitVisiable = false;
    };
    // 允许拖拽功能 只能同级拖拽
    const allowDrop = (draggingNode, dropNode, type) => {
      if (draggingNode.level === dropNode.level) {
        // parentid是父节点id
        if (draggingNode.data.parentId === dropNode.data.parentId) {
          return type === 'prev' || type === 'next';
        }
      } else {
        // 不同级进行处理
        return false;
      }
    };
    // 树排序
    const nodeDrop = (before, after) => {
      // console.log(after)
      var orderList = [];
      if (after.parent.level === 0) {
        orderList = JSON.parse(JSON.stringify(after.parent.data));
        _.remove(orderList, function (n) {
          return n.id === 'all';
        });
        orderList.forEach((ol, index) => {
          ol.order = index + 1;
        });
      } else {
        orderList = after.parent.data.children;
        orderList.forEach((ol2, index) => {
          ol2.order = index + 1;
        });
      }
      updateOrderTree(orderList).then(res => {
        if (res !== false) {
          proxy.$message.success('排序成功');
        }
      });
    };
    const handleImport = () => {
      state.dialogImport = true;
    };
    const getAllTable = async () => {
      otherForm.tableLoading = true;
      const response = await getList({ limit: '-1', page: '1', deviceCategoryId: '' }).finally(
        (otherForm.tableLoading = false)
      );
      if (response) {
        state.exportTableData = response.data.data.list;
        exportExcel();
      }
    };
    // 导出
    const handleExport = val => {
      if (val === 'all') {
        getAllTable();
      } else {
        if (!state.tableSelected.length) {
          proxy.$message.warning('请先勾选数据！');
          return;
        }
        state.exportTableData = JSON.parse(JSON.stringify(state.tableSelected));
        exportExcel();
      }
    };
    const exportExcel = () => {
      const fieldAll = {
        所属分类: 'deviceCategoryId',
        仪器设备编号: 'deviceNumber',
        仪器设备名称: 'name',
        设备状态: 'status',
        型号规格: 'model',
        测量范围: 'measurementRange',
        '不确定度/准确度/最大允许误差': 'measurementDeviation',
        生产厂家: 'equipmentManufactureName',
        存放地点: 'deviceLocation',
        到货日期: 'arrivalDate',
        验收日期: 'acceptanceDate',
        是否支持数采: 'supportDataAcquisition',
        仪器设备计量: 'isEquipmentMetering',
        生产日期: 'equipmentManufactureDate',
        计量周期: 'measurementCycle',
        计量周期单位: 'measurementCycleUnitType',
        描述: 'remark',
        计量截止有效期: 'validEndDate'
      };
      var tHeader = Object.keys(fieldAll);
      var filterVal = Object.values(fieldAll);
      export2Excel(tHeader, filterVal);
    };
    const export2Excel = (tHeader, filterVal) => {
      otherForm.tableLoading = true;
      var fileName = '设备';
      import('@/utils/Export2Excel').then(excel => {
        const data = formatJson(filterVal, state.exportTableData);
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: fileName,
          autoWidth: true,
          bookType: 'xlsx'
        });
        otherForm.tableLoading = false;
        state.tableRef.clearSelection();
        proxy.$message.success('导出成功！');
      });
    };
    const formatJson = (filterVal, jsonData) => {
      return jsonData.map(v =>
        filterVal.map(j => {
          if (j === 'deviceCategoryId') {
            return findTreeNode(v[j], otherForm.treeData)?.name;
          } else if (j === 'status') {
            return state.dictionaryJSON['24'].all[v[j]];
          } else if (j === 'deviceLocation') {
            return state.dictionaryJSON['cfdd'].all[v[j]];
          } else if (j === 'supportDataAcquisition') {
            return v[j] ? '是' : '否';
          } else if (j === 'isEquipmentMetering') {
            return v[j] ? '需要' : '不需要';
          } else if (j === 'measurementCycleUnitType') {
            return v[j] ? '年' : '月';
          } else {
            return v[j];
          }
        })
      );
    };
    // 下载附件
    const handleDownload = () => {};
    // 上传文件的限制
    const beforeUpload = file => {
      var fileName = '';
      if (file.name) {
        fileName = file.name.substring(file.name.lastIndexOf('.') + 1);
      }
      const fileSize = file.size / 1024 / 1024 < 10;
      if (!fileSize) {
        proxy.$message.error('上传附件大小不能超过10M');
        return false;
      } else if (fileName !== 'xls' && fileName !== 'xlsx') {
        proxy.$message.error('仅支持.xls，.xlsx文件扩展名');
        return false;
      } else if (file.size === 0) {
        proxy.$message.error('上传附件大小不能为空');
        return false;
      } else {
        return true;
      }
    };
    // 下载附件
    const downLoadFile = fileName => {
      const a = document.createElement('a');
      a.href = '/staticFile/' + fileName;
      a.download = fileName;
      a.style.display = 'none';
      document.body.appendChild(a);
      a.click();
      a.remove();
    };
    const submitUpload = () => {
      state.uploadRef.submit();
    };
    const handleExceed = files => {
      state.uploadRef.clearFiles();
      state.uploadRef.handleStart(files[0]);
    };
    // 上传成功的钩子
    const handleFileSuccess = (res, file) => {
      if (res.code === 200) {
        proxy.$message.success(res.message);
        state.dialogImport = false;
        proxy.getLeftTree();
      } else {
        proxy.$message.error(res.message);
      }
    };
    return {
      ...toRefs(state),
      findTreeNode,
      handleCloseDialog,
      getDictionaryAll,
      handleSelectionChange,
      handleRowClick,
      handleDownFile,
      handleDownload,
      handleExport,
      beforeUpload,
      handleFileSuccess,
      handleExceed,
      submitUpload,
      downLoadFile,
      handleImport,
      allowDrop,
      nodeDrop,
      handleAddUnit,
      handlExpired,
      closeDrawer,
      handleDatePicker,
      getPermissionBtn,
      formatDate,
      changeIcon,
      getNameByid,
      drageHeader,
      handleDelete,
      formTree,
      addTreeItem,
      refTree,
      filterNode,
      handleEditTree,
      showEditDialog,
      delTree,
      editTree,
      closeDeatilDrawer,
      handleSortChange,
      handleDetail,
      tableKey,
      showS,
      editFrom,
      otherForm,
      activeName,
      reset,
      search,
      colWidth
    };
  },
  created() {
    // 添加过滤，从展示分析的设备使用记录那边跳转过来，需要直接展示详情，自动添加过滤项，后面点击其他树，需要清掉添加的过滤内容才行
    if (this.$route.query.checkTreeId) {
      this.checkTreeId = this.$route.query.checkTreeId;
      this.getLeftTree();
      this.param = this.$route.query.param;
      this.getTableList();
    } else {
      this.getLeftTree();
    }
  },
  methods: {
    // 获取左侧列表树接口
    getLeftTree() {
      const vm = this;
      vm.tableLoading = true;
      getTree().then(res => {
        vm.tableLoading = false;
        if (res) {
          const data = res.data.data;
          if (data.length > 0) {
            vm.otherForm.treeData = JSON.parse(JSON.stringify(formatTree(data)));
            const all = { id: 'all', name: '全部' };
            vm.otherForm.treeData.unshift(all);
          } else {
            vm.otherForm.treeData = formatTree(data);
          }
          vm.otherForm.dialogTreeData = data;
          vm.$nextTick(() => {
            // 默认高亮选中节点
            if (!vm.checkTreeId) {
              // 判断第一次加载时默认选中第一个
              if (data.length > 0) {
                vm.$refs.refTree.setCurrentKey(vm.otherForm.treeData[0].id, true);
                vm.checkTreeId = vm.otherForm.treeData[0].id;
                vm.treeTitle = vm.otherForm.treeData[0].name;
                vm.getTableList();
              } else {
                vm.treeTitle = '';
                vm.tableData = [];
              }
            } else {
              vm.$refs.refTree.setCurrentKey(vm.checkTreeId, true);
            }
          });
        }
      });
    },
    // 获取当前节点的产品列表
    getTableList(query, flag) {
      const vm = this;
      if (vm.otherForm.treeData.length === 0 && !vm.$route.query.checkTreeId) {
        vm.$message.closeAll();
        vm.$message.warning('请先在左侧添加分类');
        return false;
      }
      const params = {
        param: vm.param,
        deviceCategoryId: vm.checkTreeId === 'all' ? '' : vm.checkTreeId,
        ...vm.searchForm
      };
      if (query && query.page) {
        params.page = query.page.toString();
        params.limit = query.limit.toString();
        vm.listQuery.page = query.page;
        vm.listQuery.limit = query.limit;
      } else {
        params.page = vm.listQuery.page.toString();
        params.limit = vm.listQuery.limit.toString();
      }
      vm.tableLoading = true;
      getList(params).then(res => {
        vm.tableLoading = false;
        if (res && res.data.code === 200) {
          vm.tableData = res.data.data.list;
          vm.total = res.data.data.totalCount;
          // 自动打开详情页
          if (vm.$route.query.checkTreeId && flag !== false) {
            vm.handleDetail(vm.tableData[0]);
          }
        }
      });
    },
    getTree(type) {
      getTree(type).then(response => {
        if (response !== false) {
          const data = response.data.data;
          this.otherForm.treeData = formatTree(data);
          this.otherForm.newTree = formatTree(data);
          this.otherForm.dialogTreeData = data;
        }
      });
    },
    clickNode(data, node) {
      this.treeTitle = formatTreeByNames(node).join('/');
      if (this.checkTreeId !== data.id) {
        this.checkTreeId = data.id;
        this.getTableList();
      }
    }
  }
};
</script>
<style lang="scss" scoped>
@import '@/styles/tree.scss';
.uploadRules {
  padding: 5px 10px;
  background: #f0f2f5;
  margin: 14px 0;
  li {
    list-style: none;
    line-height: 22px;
    font-size: 13px;
  }
  .blue-color {
    margin-right: 0;
  }
}
.title {
  background-color: #f0f2f5;
  line-height: 30px;
  padding: 0 10px;
  margin-bottom: 15px;
}
.bt {
  font-size: 15px;
  line-height: 16px;
  font-weight: bold;
  // padding: 0 10px;
}
.searchInput {
  display: flex;
  .el-input {
    width: 360px;
    margin-right: 10px;
  }
}
</style>
