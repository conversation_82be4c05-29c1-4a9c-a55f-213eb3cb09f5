import request from '@/utils/request';
// import qs from 'qs'

// const Headers = {
//   'Content-Type': 'application/json; charset=utf-8'
// }
// const baseUrl = config.masUrl

// 物料组列表
// export function getWlGroup(data) {
//   return request({
//     url: baseUrl + '/api/material/material-group',
//     method: 'get',
//     headers: Headers,
//     params: data
//   })
// }
// // 工序列表
// export function getProcessList(data) {
//   return request({
//     url: baseUrl + '/api/procedure/work-procedure',
//     method: 'get',
//     headers: Headers,
//     params: data
//   })
// }

// // 物料列表
// export function getWlItem(data) {
//   return request({
//     url: baseUrl + '/api/material/material',
//     method: 'get',
//     headers: Headers,
//     params: data
//   })
// }
// 物料组列表-TES
export function getWlGroupNew(data) {
  return request({
    url: `/api-capabilitystd/capability/materialgroup/list`,
    method: 'post',
    data
  });
}
// 物料列表-TES
export function getWlItemNew(data) {
  return request({
    url: `/api-capabilitystd/capability/material/list`,
    method: 'post',
    data
  });
}
// 工序列表-TES
export function getProcessListNew(data) {
  return request({
    url: `/api-capabilitystd/capability/workingprocedure/list`,
    method: 'post',
    data
  });
}
