<template>
  <!-- 原始记录模板  -->
  <div v-if="loginResult" class="uniapp_template">
    <div v-if="!isCheck && jsonData.isower" class="uniapp_template_button">
      <div class="button_item" :loading="loading" @click="handleAddDevice">仪器设备</div>
      <div class="button_item" :loading="loading" @click="isShowPicture = true">试验图片</div>
    </div>
    <div v-loading="loading" :class="{ moban: true, scale: isZoomOutTemplate }">
      <div class="temp-wrapper" :style="isZoomOutTemplate ? { transform: `scale(${zoomOutRate})` } : null">
        <base-excel ref="excel" :json-data="jsonData" :is-standard-custom="isStandardCustom" @handleData="handleData" />
      </div>
    </div>
    <div v-if="!isCheck && jsonData.isower" class="uniapp_template_button">
      <div class="button_item" :loading="loading" @click="handleSubmit(0)">保存</div>
      <div class="button_item primary" :loading="loading" @click="dataValue.dialogSubmit = true">提交</div>
    </div>
    <!--  设备管理-->
    <el-dialog
      v-model="dialogVisible"
      title="选择仪器设备"
      :width="isVerticalDispay ? 600 : 1000"
      :close-on-click-modal="false"
      custom-class="dialog-table"
    >
      <el-button size="small" type="primary" icon="el-icon-plus" @click="addRow()">添加仪器设备</el-button>
      <el-table
        ref="multipleTable"
        :data="tableList"
        size="medium"
        fit
        border
        height="auto"
        class="dark-table base-table"
        @select-all="handleSelectAll"
        @selection-change="handleSelectionChange"
        @select="handleToggleRowSelection"
        @row-click.stop="handleRowClick"
      >
        <el-table-column type="selection" :width="colWidth.checkbox" label="全选" :selectable="rowSelect" />
        <el-table-column label="仪器设备" :width="300" prop="yqsb">
          <template #default="{ row, $index }">
            <ul>
              <li>
                <span>编号：</span>
                <el-select
                  v-model="row.deviceNumber"
                  filterable
                  placeholder="请选择"
                  class="selectBh"
                  @change="
                    val => {
                      handleSelect(val, $index);
                    }
                  "
                >
                  <el-option-group v-for="item in deviceList" :key="item.label" :label="item.label">
                    <el-option
                      v-for="val in item.group"
                      :key="val.id"
                      :label="val.deviceNumber + val.name"
                      :value="val.deviceNumber"
                      :disabled="
                        selected.some(value => {
                          return value == val.deviceId;
                        }) ||
                        val.isOverdue ||
                        val.statusNo
                      "
                    >
                      <span style="float: left">{{ val.deviceNumber }}({{ val.name }})</span>
                      <span v-if="val.isOverdue" class="fr" style="color: red">已过期</span>
                      <span v-if="val.statusNo" class="fr" style="color: red">不可用</span>
                    </el-option>
                  </el-option-group>
                </el-select>
              </li>
              <li>
                <span>名称：</span><span class="value">{{ row.deviceName || '--' }}</span>
              </li>
              <li>
                <span>型号规格：</span><span class="value">{{ row.model || '--' }}</span>
              </li>
            </ul>
          </template>
        </el-table-column>
        <el-table-column label="检定有效期" :width="colWidth.date" prop="hdrq">
          <template #default="{ row }">
            <div>{{ row.validBeginDate || '--' }} ~ <br />{{ row.validEndDate || '--' }}</div>
          </template>
        </el-table-column>
        <el-table-column label="不确定度" :width="90" prop="measurementDeviation" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="nowrap">
              {{ row.measurementDeviation || '--' }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="使用情况" :width="255" prop="syqk">
          <template #default="{ row, $index }">
            <ul>
              <li>
                <span>使用人：</span><span class="value">{{ getNameByid(accountId) }}</span>
              </li>
              <li>
                <span>时间：</span>
                <el-date-picker
                  v-if="row.isCheck"
                  v-model="row.deviceUseTime"
                  :editable="false"
                  type="date"
                  :clearable="false"
                  placeholder="选择日期"
                  @change="
                    val => {
                      handleSelectDate(val, $index);
                    }
                  "
                />
                <span v-else>--</span>
              </li>
              <li>
                <span>状态：</span>
                <el-select v-if="row.isCheck" v-model="row.deviceStatusBefore" class="status" filterable>
                  <el-option v-for="(val, key) in dictionary" :key="key" :label="val" :value="key" />
                </el-select>
                <span v-else>--</span>
                <span class="fgline">/</span>
                <el-select v-if="row.isCheck" v-model="row.deviceStatusAfter" class="status" filterable>
                  <el-option v-for="(val, key) in dictionary" :key="key" :label="val" :value="key" />
                </el-select>
                <span v-else>--</span>
              </li>
            </ul>
          </template>
        </el-table-column>
        <el-table-column label="测量范围" prop="measurementRange" :width="120">
          <template #default="{ row }">
            <el-input
              v-if="row.isCheck"
              v-model="row.measurementRange"
              type="textarea"
              :rows="3"
              placeholder="请输入"
            />
            <span v-else>--</span>
          </template>
        </el-table-column>
        <el-table-column label="备注" prop="remark" :width="120">
          <template #default="{ row }">
            <el-input v-if="row.isCheck" v-model="row.remark" type="textarea" :rows="3" placeholder="请输入" />
            <span v-else>--</span>
          </template>
        </el-table-column>
      </el-table>
      <template #footer>
        <span class="dialog-footer">
          <el-button size="medium" @click="dialogVisible = false">取 消</el-button>
          <el-button size="medium" type="primary" @click="onSubmit" @keyup.prevent @keydown.enter.prevent
            >确 认</el-button
          >
        </span>
      </template>
    </el-dialog>
    <AddTestPicture
      :show-picture="isShowPicture"
      :page-name="'addRecord'"
      :vertical="isVerticalDispay"
      @isCloseImg="closeDialogImg"
    />
    <!-- 审核弹屏-->
    <module-audit
      :data-value="dataValue"
      :json-data="jsonData"
      :is-standard-custom="isStandardCustom"
      @closedialog="closedialog"
      @sumbitData="sumbitData"
    />
  </div>
</template>

<script>
import { reactive, toRefs, getCurrentInstance, ref, nextTick, onMounted, onBeforeUnmount } from 'vue';
import { getNameByid, getPermissionBtn } from '@/utils/common';
import { getLoginInfo } from '@/utils/auth';
import { ElMessage } from 'element-plus';
import baseExcel from '@/views/excelComponents/baseExcel';
import AddTestPicture from './components/addTestPicture';
import ModuleAudit from '@/components/BusinessComponents/ModuleAudit';
import { colWidth } from '@/data/tableStyle';
import {
  experiment,
  experimentmongodatainfo,
  saveExcelInfo,
  TemplateIdByexperimentId,
  addDevice,
  downloadByCapabilityId,
  resetSave
} from '@/api/execution';
import { getDeviceList } from '@/api/sampleItemTest';
import { getCapabilityDevice, getDeviceCapabilityList } from '@/api/equipment';
import { formatDate } from '@/utils/formatTime';
import store from '@/store';
import _ from 'lodash';
import { saveSysLoginLog } from '@/api/login-log';
import { useRoute } from 'vue-router';
import uniWebview from '@dcloudio/uni-webview-js';
import { setLoginId, setSysConfig, setBackgroundColor } from '@/utils/auth';
import { getSysConfigInfo } from '@/api/sysConfig';
import { decryptCBC } from '@/utils/ASE';
export default {
  name: 'AddRecord',
  components: { baseExcel, AddTestPicture, ModuleAudit },

  setup(props, ctx) {
    const { proxy } = getCurrentInstance();
    const ruleForm = ref('');
    const route = useRoute();
    const state = reactive({
      experimentId: '',
      detailInfo: {},
      isStandardCustom: 0,
      mobanRef: ref(),
      imgBaseUrl: '',
      samplesId: '',
      disable: '',
      new: '',
      innerVisible: false,
      templateVisible: false,
      loginResult: false,
      templateList: [],
      currentRow: null,
      currentRowId: null,
      capabilityId: '',
      accountId: '',
      dialogVisible: false,
      loading: false,
      experimentData: {},
      isShowPicture: false,
      jsonData: {},
      postDeviceData: [],
      dataValue: {
        dialogSubmit: false
      },
      url: '',
      dialogSubmit: false,
      isCheck: false,
      formData: {
        realOwnerIds: [],
        reviewerId: '',
        date: []
      },
      dictionary: {
        1: '正常',
        3: '停用',
        0: '异常'
      },
      selected: [], // 已经添加的仪器设备的id集合
      multipleTable: ref(null),
      tableList: [],
      selectTable: [], // 选中的仪器设备表格数据
      userList: store.state.common.nameList,
      deviceList: [], // 仪器设备
      routeQuery: route.query,
      allDeviceList: [
        {
          // 所有仪器设备
          label: '可选择',
          group: []
        },
        {
          label: '已过期',
          group: []
        }
      ],
      isVerticalDispay: window.innerWidth < 1024,
      isZoomOutTemplate: window.innerWidth < 794,
      zoomOutRate: 0
    });
    const initLoginMobile = async () => {
      const params = {
        username: state.routeQuery.username,
        password: state.routeQuery.password,
        grant_type: 'password'
      };
      state.experimentId = state.routeQuery.experimentId;
      state.samplesId = state.routeQuery.samplesId;
      state.disable = state.routeQuery.type;
      state.capabilityId = state.routeQuery.capabilityId;
      state.new = state.routeQuery.new;
      state.isCheck = state.routeQuery.type === 'check';
      state.loginResult = await store.dispatch('user/login', params);
      if (state.loginResult) {
        const currentIP = localStorage.getItem('currentIp');
        const param = {
          ie: navigator.userAgent,
          address: currentIP
        };
        state.accountId = getLoginInfo().accountId;
        getSysConfigInfos();
        saveSysLoginLog(param).then(async res => {
          setLoginId(res.data.data);
          getDeviceTableAndRefresh();
          getall();
          initUniapp();
          await getEquipmentList();
          if (state.routeQuery.deviceId) {
            await getDeviceTable();
            const device = state.allDeviceList[0]?.group?.find(item => item.id === state.routeQuery.deviceId);
            if (device) {
              if (!state.selectTable.find(item => item.deviceId === state.routeQuery.deviceId)) {
                state.selectTable = [
                  {
                    ...device,
                    experimentId: state.experimentId,
                    deviceUseTime: formatDate(new Date()),
                    validBeginDate: formatDate(device.validBeginDate),
                    validEndDate: formatDate(device.validEndDate),
                    ownerId: state.accountId,
                    deviceStatusBefore: 1,
                    deviceStatusAfter: 1,
                    isCheck: true
                  }
                ];
              }
              onSubmit();
            }
          }
        });
      }
    };
    const getSysConfigInfos = () => {
      getSysConfigInfo({}).then(res => {
        if (res) {
          document.getElementsByTagName('body')[0].style.setProperty('--backgroundColor', '#fff');
          setSysConfig(res.data.data);
          setBackgroundColor('#fff');
        }
      });
    };
    onMounted(() => {
      document.addEventListener('UniAppJSBridgeReady', window.UniAppJSBridgeReady);
      if (state.isZoomOutTemplate) {
        state.zoomOutRate = window.innerWidth / 840;
      }
      window.addEventListener('resize', onWindowResizeChange, false);
    });
    onBeforeUnmount(() => {
      window.removeEventListener('resize', onWindowResizeChange, false);
    });
    const handlePushMessage = () => {
      uniWebview.postMessage({
        data: { action: 'click2' }
      });
      window.uni.postMessage({
        data: {
          action: 'click'
        }
      });
    };
    const initUniapp = () => {
      window.uni.getEnv(res => {
        console.log('getEnv', res);
      });
      window.uni.postMessage({
        data: {
          action: 'loadSuccess'
        }
      });
    };
    initLoginMobile();
    // 获取仪器设备编号
    const getEquipmentList = async () => {
      return getDeviceCapabilityList({ limit: '10000', page: '1' }).then(res => {
        state.allDeviceList[0].group = [];
        state.allDeviceList[1].group = [];
        if (res.data.data.list.length > 0) {
          res.data.data.list.forEach(item => {
            item.deviceId = item.id;
            item.deviceName = item.name;
            // 判断仪器设备检定日期是否已过期
            if (item.status === 'Running') {
              if (
                new Date(item.validBeginDate).getTime() < new Date().getTime() &&
                new Date(item.validEndDate).getTime() > new Date().getTime()
              ) {
                item.isOverdue = false;
                state.allDeviceList[0].group.push(item);
              } else if (!item.isEquipmentMetering) {
                item.isOverdue = false;
                state.allDeviceList[0].group.push(item);
              } else {
                item.isOverdue = true;
                state.allDeviceList[1].group.push(item);
              }
            } else {
              item.statusNo = true;
              state.allDeviceList[1].group.push(item);
            }
          });
          state.deviceList = JSON.parse(JSON.stringify(state.allDeviceList));
        }
      });
    };
    // 获取仪器设备表格
    const getDeviceTable = async () => {
      // 获取已选中的数据
      return getDeviceList(state.experimentId).then(res => {
        if (res.data.code === 200) {
          state.tableList = [];
          if (res.data.data.length > 0) {
            res.data.data.forEach(row => {
              row.isCheck = true;
              state.tableList.push(row);
            });
            state.selected = state.tableList.map(item => {
              return item.deviceId;
            });
            state.selectTable = res.data.data;
            nextTick(() => {
              if (state.dialogVisible) {
                state.tableList.forEach(row => {
                  state.multipleTable.toggleRowSelection(row, true);
                });
              }
            });
          } else {
            getItemRelation();
          }
        }
      });
    };

    const getDeviceTableAndRefresh = () => {
      // 获取已选中的数据
      getDeviceList(state.experimentId).then(res => {
        if (res.data.code === 200) {
          state.tableList = [];
          if (res.data.data.length > 0) {
            res.data.data.forEach(row => {
              row.isCheck = true;
              state.tableList.push(row);
            });
            state.selected = state.tableList.map(item => {
              return item.deviceId;
            });
            state.selectTable = res.data.data;
            nextTick(() => {
              if (state.dialogVisible) {
                state.tableList.forEach(row => {
                  state.multipleTable.toggleRowSelection(row, true);
                });
              }
            });
          } else {
            getItemRelation();
          }
          getall();
        }
      });
    };
    // 获取项目库相关数据
    const getItemRelation = () => {
      getCapabilityDevice(state.capabilityId).then(res => {
        if (res) {
          // 过滤重复的设备编号
          var tableJson = {};
          res.data.data.forEach(item => {
            const isShow =
              item.isEquipmentMetering &&
              new Date(item.validBeginDate).getTime() < new Date().getTime() &&
              new Date(item.validEndDate).getTime() > new Date().getTime();
            if (isShow || !item.isEquipmentMetering) {
              item.ownerId = getLoginInfo().accountId;
              item.deviceUseTime = formatDate(new Date());
              item.experimentid = state.routeQuery.experimentId;
              item.deviceStatusBefore = '1';
              item.deviceStatusAfter = '1';
              tableJson[item.deviceId] = item;
            }
          });
          state.tableList = Object.values(tableJson);
          state.selected = state.tableList.map(item => {
            return item.deviceId;
          });
        }
      });
    };
    const init1 = () => {
      return new Promise((resolve, reject) => {
        // 获取单个模板信息
        experiment(state.experimentId).then(res => {
          if (res.data.code === 200 && res.data.data) {
            // state.jsonData = { ...res.data.data }
            res.data.data.isower = _.indexOf(res.data.data.ownerIds.split(','), state.accountId) !== -1;
            resolve(res.data.data);
          } else {
            resolve(res.data.data);
            // reject('接口错误')
          }
        });
      });
    };
    const init2 = () => {
      return new Promise((resolve, reject) => {
        // TemplateIdByexperimentId(state.capabilityId).then(res => {
        //   if (res.data.code === 200) {
        //     // state.jsonData.excelHtml = res.data.data.html
        //     // console.log(state.jsonData)
        //     resolve(res.data.data)
        //   } else {
        //     reject('接口错误')
        //   }
        // })
        if (state.new) {
          downloadByCapabilityId({ capabilityId: state.capabilityId, samplesId: state.samplesId }).then(res => {
            if (res.data.code === 200) {
              resolve(res.data.data);
            } else {
              reject('接口错误');
            }
          });
        } else {
          TemplateIdByexperimentId(state.experimentId).then(res => {
            if (res.data.code === 200) {
              resolve(res.data.data);
            } else {
              reject('接口错误');
            }
          });
        }
        // TemplateIdByexperimentId({capabilityId:state.capabilityId,samplesId:state.samplesId}).then(res => {
      });
    };
    const getall = () => {
      Promise.all([init1(), init2()])
        .then(async allres => {
          // 两个都调成功以后执行的操作
          // 模板实验值信息
          await experimentmongodatainfo(state.experimentId).then(async resdata => {
            if (resdata) {
              const experimentData = resdata.data.data;
              experimentData.coreNumber = allres[1].coreNumber;
              state.jsonData = {
                ...allres[0],
                templateId: state.new ? allres[1].templateId : allres[0].templateId,
                excelHtml: decryptCBC(allres[1].html),
                templateValue: allres[1].templateValue,
                fileNo: allres[1].fileNo,
                coreNumber: allres[1].coreNumber,
                showType: allres[1].showType,
                source: allres[1].source,
                isUseStandard: allres[1].isUseStandard,
                disabled: state.disable !== 'check',
                experimentData: experimentData,
                realReviewerIdimg: experimentData.reviewerSignUrl.split(','),
                realOwnerIdsimgs: experimentData.ownerSignUrls.split(',')
              };
            }
          });
        })
        .catch(err => {
          // 抛出错误信息
          ElMessage(err);
        });
    };
    // 保存
    const excel = ref(null);
    const handleData = thisValue => {
      state.experimentData = thisValue;
    };
    const sumbitData = () => {
      excel.value.handleData();
    };
    const handleSubmit = type => {
      const postdata = getExcelPostData();
      if (type === 0) {
        // 保存
        state.loading = true;
        saveExcelInfo(postdata).then(res => {
          state.loading = false;
          if (res) {
            uniWebview.postMessage({
              data: { action: 'saveSuccess' }
            });
          }
        });
      }
    };

    function getExcelPostData() {
      excel.value.handleData();
      var postdata = {};
      postdata.capabilityId = state.jsonData.capabilityId;
      postdata.experimentId = state.jsonData.experimentId;
      postdata.templateId = state.jsonData.templateId;
      postdata.samplesId = state.jsonData.samplesId;
      postdata.showType = state.jsonData.showType; // 区分模板横向纵向
      postdata.source = state.jsonData.source;
      postdata.remark = '';
      postdata.experimentData = state.experimentData;
      postdata.saveColorNumber = 0;
      postdata.isUseStandard = state.jsonData.isUseStandard; // 是否是需要判定标准的模板
      postdata.isStandardCustom = state.isStandardCustom; // 判断是否自定义标准
      if (!state.jsonData.saveColorNumber) {
        if (state.jsonData.experimentData.coreRecord.coreColourList?.length > 0) {
          postdata.saveColorNumber = state.jsonData.experimentData.coreRecord.coreColourList.length;
        }
      }
      return postdata;
    }

    const handleSelectDate = (val, index) => {
      state.tableList[index].deviceUseTime = formatDate(val);
    };
    // 仪器设备表单选中的数据
    const handleSelectionChange = value => {
      state.selectTable = value;
    };
    // 仪器设备单选切换
    const handleToggleRowSelection = (selection, row) => {
      row.isCheck = !row.isCheck;
    };
    // 仪器设备全选和全不选的判断
    const handleSelectAll = datas => {
      if (datas.length > 0) {
        state.tableList.forEach(item => {
          item.isCheck = true;
        });
      } else {
        state.tableList.forEach(item => {
          item.isCheck = false;
        });
      }
    };
    // 仪器设备判断选框是否可以选择
    const rowSelect = (row, index) => {
      if (row.deviceId || row.deviceid) {
        return true;
      } else {
        return false;
      }
    };
    const handleSelect = (val, index) => {
      const addJson = state.deviceList[0].group.find(item => item.deviceNumber === val);
      state.tableList[index] = JSON.parse(JSON.stringify(addJson));
      state.tableList[index].experimentId = state.experimentId;
      state.tableList[index].deviceUseTime = formatDate(new Date());
      state.tableList[index].validBeginDate = formatDate(addJson.validBeginDate);
      state.tableList[index].validEndDate = formatDate(addJson.validEndDate);
      state.tableList[index].ownerId = state.accountId;
      state.tableList[index].deviceStatusBefore = '1';
      state.tableList[index].deviceStatusAfter = '1';
      state.tableList[index].isCheck = true;
      state.multipleTable.toggleRowSelection(state.tableList[index], true);
      state.selected = state.tableList.map(item => {
        return item.deviceId;
      });
    };
    function handleRowClick(row) {
      if (row && row.deviceNumber) {
        const rowIndex = state.tableList.findIndex(item => item.id === row.id);
        if (rowIndex !== -1) {
          row.isCheck = !row.isCheck;
          state.multipleTable.toggleRowSelection(state.tableList[rowIndex], row.isCheck);
        }
      }
    }
    const onSubmit = () => {
      // 提交仪器设备
      if (state.selectTable.length > 0) {
        const params = {
          experimentId: state.routeQuery.experimentId,
          samplesId: state.routeQuery.samplesId,
          devices: state.selectTable
        };
        addDevice(params).then(res => {
          if (res) {
            proxy.$message.success('操作成功');
            state.dialogVisible = false;
            const postdata = getExcelPostData();
            saveExcelInfo(postdata).then(res => {
              if (res) {
                getDeviceTableAndRefresh();
              }
            });
          }
        });
      } else {
        // proxy.$message.closeAll()
        proxy.$message.error('请至少勾选一条数据');
      }
    };
    // 仪器设备选择框筛选
    const handleFilter = val => {
      if (val) {
        const newData1 = state.allDeviceList[0].group.filter(item => {
          return item.name.indexOf(val) > -1 || item.deviceNumber.indexOf(val) > -1;
        });
        const newData2 = state.allDeviceList[1].grouaddRowp.filter(item => {
          return item.name.indexOf(val) > -1 || item.deviceNumber.indexOf(val) > -1;
        });
        state.deviceList[0].group = JSON.parse(JSON.stringify(newData1));
        state.deviceList[1].group = JSON.parse(JSON.stringify(newData2));
      } else {
        state.deviceList = JSON.parse(JSON.stringify(state.allDeviceList));
      }
    };
    const addRow = () => {
      state.tableList.push({
        deviceStatusBefore: '',
        deviceStatusAfter: ''
      });
    };
    const deleteRow = index => {
      state.tableList.splice(index, 1);
      state.selected = state.tableList.map(item => {
        return item.deviceId;
      });
    };
    const closedialog = () => {
      state.dataValue.dialogSubmit = false;
      // ctx.emit('getdata')
    };
    const closeDialogImg = () => {
      state.isShowPicture = false;
    };
    const handleAddDevice = () => {
      state.dialogVisible = true;
      const postdata = getExcelPostData();
      saveExcelInfo(postdata).then(res => {
        if (res) {
          getDeviceTable();
        }
      });
    };
    const submitChange = () => {
      const data = {
        experimentId: state.experimentId,
        templateId: state.currentRow.id
      };
      resetSave(data).then(async res => {
        state.jsonData.templateId = state.currentRow.id;
        state.jsonData.experimentData = res.data.data;
        state.jsonData.excelHtml = decryptCBC(res.data.data.html);
        state.jsonData.showType = state.currentRow.showType;
        state.jsonData.source = state.currentRow.source;
        nextTick(() => {
          excel.value.setData();
        });
        templateVisibleClose();
        ElMessage.success({
          message: '提交成功',
          type: 'success'
        });
      });
    };
    const templateVisibleClose = () => {
      state.templateVisible = false;
      state.currentRowId = null;
      state.innerVisible = false;
    };

    const saveSampleColor = colorList => {
      state.jsonData.experimentData.saveColorNumber = colorList.length;
      state.jsonData.experimentData.coreRecord.coreColourList = colorList;
    };
    // 切换标准取值自定义或系统定义
    const beforeSwitchChange = val => {
      return new Promise((resolve, reject) => {
        return resolve(true);
        // ElMessageBox({
        //   title: '提示',
        //   message: '切换标准定义会导致模板刷新',
        //   confirmButtonText: '继续',
        //   cancelButtonText: '取消',
        //   showCancelButton: true,
        //   closeOnClickModal: false,
        //   type: 'info'
        // }).then(() => {
        //   return resolve(true)
        // }).catch(() => {
        //   return resolve(false)
        // })
      });
    };
    // 监听 uniapp 旋转变化
    const onWindowResizeChange = () => {
      state.isVerticalDispay = window.innerWidth < 1024;
      state.isZoomOutTemplate = window.innerWidth < 794;
      if (state.isZoomOutTemplate) {
        state.zoomOutRate = window.innerWidth / 840;
      }
    };
    return {
      ...toRefs(state),
      excel,
      handlePushMessage,
      beforeSwitchChange,
      ruleForm,
      colWidth,
      handleFilter,
      templateVisibleClose,
      submitChange,
      handleSelectionChange,
      handleToggleRowSelection,
      handleSelectAll,
      rowSelect,
      closedialog,
      getPermissionBtn,
      getEquipmentList,
      getItemRelation,
      handleData,
      sumbitData,
      handleSubmit,
      handleAddDevice,
      getDeviceTableAndRefresh,
      onSubmit,
      addRow,
      deleteRow,
      handleSelect,
      handleSelectDate,
      getLoginInfo,
      closeDialogImg,
      getNameByid,
      saveSampleColor,
      handleRowClick
    };
  }
};
</script>

<style lang="scss" scoped>
.uniapp_template {
  padding: 10px 20px 20px 20px;
  height: 100%;
  overflow: auto;
  display: flex;
  flex-direction: column;
}
.uniapp_template_button {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  .button_item {
    padding: 20px 10px;
    width: 100%;
    color: #00b38a;
    border: 1px solid #00b38a;
    border-radius: 5px;
    font-size: 18px;
    &.primary {
      background-color: #00b38a;
      color: #fff;
    }
    &:nth-child(odd) {
      margin-right: 10px;
    }
  }
}
.moban {
  overflow: auto;
  flex: 1 1 0%;
  margin-top: 10px;

  &.scale {
    overflow-x: hidden;
    overflow-y: auto;

    .temp-wrapper {
      margin-left: 6px;
      margin-right: 6px;
    }
  }
  &.scale::-webkit-scrollbar {
    width: 0;
  }
}
.temp-wrapper {
  margin: 8px auto 0 auto;
  width: 794px;
  transform-origin: left top;
}
:deep(.el-container .el-main .page-main) {
  background: #f0f2f5;
  padding-top: 10px;
}

.dialog-table {
  .selectBh {
    width: 60%;
  }
}

.fgline {
  margin: 0 5px;
}
ul {
  padding: 0;
}
li {
  list-style: none;
  margin: 8px 0;
}
:deep(.el-badge__content--primary) {
  background-color: #f56c6c;
}
.base-table {
  margin-top: 12px;
  .status.el-select--medium {
    width: 30%;
  }
  :deep(.el-input--medium .el-input__icon) {
    line-height: 24px;
  }
  :deep(.el-date-editor.el-input, .el-date-editor.el-input__inner) {
    width: 64.5%;
  }
  :deep(.el-input--medium .el-input__inner) {
    height: 24px;
    line-height: 24px;
  }
  :deep(.el-select--medium),
  :deep(.el-input--medium) {
    line-height: 24px;
  }
}

.addRecord {
  margin: 0 1rem;
  padding: 10px 14px 10px 1rem;
  :deep(.el-form .el-form-item) {
    margin-bottom: 20px;
  }
  :deep(.dialogRecord .el-dialog__body) {
    padding-top: 35px;
    min-height: 500px;
  }
}
</style>
