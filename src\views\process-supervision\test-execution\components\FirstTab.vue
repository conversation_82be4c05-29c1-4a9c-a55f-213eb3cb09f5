<template>
  <div class="originalRecord">
    <div class="btn-group">
      <el-button
        v-if="jsonData.isower && getPermissionBtn('dataAcquisition')"
        class=""
        type="warning"
        size="small"
        icon="el-icon-pie-chart"
        @click="handleDataAcquisition"
        @keyup.prevent
        @keydown.enter.prevent
        >数据采集</el-button
      >
      <el-button
        v-if="jsonData.isower"
        class=""
        type="primary"
        size="small"
        icon="el-icon-plus"
        @click="handleAddRecord"
        @keyup.prevent
        @keydown.enter.prevent
        >原始记录</el-button
      >
    </div>
    <el-form label-width="110px" :model="jsonData" label-position="right" size="small">
      <el-form-item label="检测项目：">
        <span>{{ jsonData.capabilityName }}</span>
      </el-form-item>
      <el-form-item label="试验员：">
        <UserTag
          v-for="(item, index) in getNamesByid(jsonData.ownerIds)"
          :key="index"
          :name="index === getNamesByid(jsonData.ownerIds).length - 1 ? item : item"
        />
      </el-form-item>
      <el-form-item label="日期要求：">
        <span>{{ jsonData.startdatetime }} ~ {{ jsonData.finishdatetime }}</span>
      </el-form-item>
      <el-form-item label="关键参数：">
        <span>
          <div v-if="jsonData.capabilityPara.length === 0">-</div>
          <span v-for="(item, index) in jsonData.capabilityPara" v-else :key="index"
            >{{ item }}{{ jsonData.capabilityPara.length === index + 1 ? ' ' : ' ,' }}</span
          ></span
        >
      </el-form-item>
      <el-form-item label="试验方法：">
        <div v-if="jsonData.method">
          <span
            v-for="item in jsonData.method.split(',')"
            :key="item"
            class="method-link"
            @click="showTestMethod(item)"
          >
            {{ item }} &nbsp;
          </span>
        </div>
        <span v-else>--</span>
      </el-form-item>
      <el-form-item label="试验要求：">
        <template v-if="!isEditRequirement">
          <Tooltip :content="jsonData.requirement" placement="bottom-start" effect="light">
            <div class="requirement-text nowrap">{{ jsonData.requirement ? jsonData.requirement : '--' }}</div>
          </Tooltip>
          <el-button class="no-select" circle icon="el-icon-edit" size="small" @click="onRequirementEdit" />
        </template>
        <template v-else>
          <el-input
            v-model="requirement"
            type="textarea"
            :autosize="{ minRows: 1, maxRows: 3 }"
            class="requirement-input"
            size="small"
          />
          <el-button class="no-select" size="small" @click="onRequirementCancel">取消</el-button>
          <el-button :loading="saving" class="no-select" type="primary" size="small" @click="onRequirementSave"
            >保存</el-button
          >
        </template>
      </el-form-item>
      <el-form-item label="样品编号：">
        <span>{{ jsonData.secSampleNum ? jsonData.secSampleNum : '--' }}</span>
      </el-form-item>
      <el-form-item label="样品名称：">
        <span>{{ jsonData.samplesName ? jsonData.samplesName : '--' }}</span>
      </el-form-item>
      <el-form-item label="型号规格：">
        <span>{{ jsonData.prodType ? jsonData.prodType : '--' }}</span>
      </el-form-item>
      <el-form-item label="样品数量：">
        <span
          >{{ jsonData.sampleNum }}{{ filterSampleUnitToName(jsonData.sampleUnitId) || jsonData.sampleUnitId }}</span
        >
      </el-form-item>
    </el-form>
    <DialogAssociatedDevice
      :dialog-show="dialogAssociated"
      :secsamplenum="jsonData.secSampleNum"
      :capability-id="jsonData.capabilityId"
      :device-list="associatedDevice"
      @closeDialog="closeDialog"
    />
  </div>
</template>

<script>
import { reactive, toRefs, watch } from 'vue';
import { useRoute } from 'vue-router';
import { getNamesByid, getPermissionBtn } from '@/utils/common';
import UserTag from '@/components/UserTag';
import {
  downloadByCapabilityId,
  getInfoByExperimentId,
  findDeviceByCapabilityId,
  saveExperimentRequirement
} from '@/api/execution';
import router from '@/router';
import DialogAssociatedDevice from './dialog-associated-device.vue';
import { ElMessage } from 'element-plus';
import { filterSampleUnitToName } from '@/utils/formatJson';
import { getCapabilityById } from '@/api/capability';
import Tooltip from '@/components/Tooltip';

export default {
  name: 'FirstTab',
  components: { UserTag, DialogAssociatedDevice, Tooltip },
  props: {
    jsonData: {
      type: Object,
      default: function () {
        return {};
      }
    },
    sampleCollectionList: {
      type: Array,
      default: function () {
        return [];
      }
    }
  },
  emits: ['showRawTemplate', 'refreshDetail'],
  setup(props, context) {
    const route = useRoute();
    watch(props, newValue => {
      state.jsonData = newValue.jsonData;
      state.collectionList = newValue.sampleCollectionList;
      state.isEditRequirement = false;
    });
    const state = reactive({
      jsonData: {},
      isShow: false,
      dialogAssociated: false,
      associatedDevice: [], // 关联的仪器设备
      collectionList: [],
      requirement: '',
      isEditRequirement: false,
      saving: false
    });
    // 添加线芯维护
    const handleSafeguard = () => {
      state.isShow = true;
    };
    const closeDialog = () => {
      state.dialogAssociated = false;
    };
    const showTestMethod = methodName => {
      const postBody = {
        name: methodName,
        capabilityId: state.jsonData.capabilityId
      };
      getCapabilityById(postBody).then(res => {
        if (res.data.code === 200 && res.data.data.id) {
          const result = res.data.data;
          const param = {
            type: 'detail',
            capabilityId: state.jsonData.capabilityId,
            capabilityName: encodeURIComponent(state.jsonData.capabilityName),
            id: result.id,
            methodId: result.methodId
          };
          const newRouter = router.resolve({
            path:
              '/add-or-update-operation-steps/' +
              param.type +
              '/' +
              param.capabilityName +
              '/' +
              param.capabilityId +
              '/' +
              param.id +
              '/' +
              param.methodId
          });
          window.open(newRouter.href, 'OperationSteps');
          // const newTime = setInterval(() => {
          //   if (newpage.opener === null) {
          //     clearInterval(newTime)
          //     getMethodLists(props.item.id)
          //   }
          // }, 500)
        } else {
          ElMessage.warning({
            message: '当前试验方法未维护，请前往检测项目进行维护!'
          });
        }
      });
    };
    const onRequirementEdit = () => {
      state.isEditRequirement = true;
      state.requirement = state.jsonData.requirement || '';
    };
    const onRequirementCancel = () => {
      state.isEditRequirement = false;
      state.requirement = state.jsonData.requirement || '';
    };
    const onRequirementSave = () => {
      const params = {
        experimentId: state.jsonData.experimentId,
        requirement: state.requirement
      };
      state.saving = true;
      saveExperimentRequirement(params).then(res => {
        state.saving = false;
        if (res) {
          context.emit('refreshDetail', state.jsonData);
          state.isEditRequirement = false;
          ElMessage.success({ message: res.data.data });
        }
      });
    };
    // 添加原始记录
    const handleAddRecord = () => {
      const isItemView = route.name === 'item-execution-detail';
      if (state.jsonData.isReceiveVerification && state.collectionList.length === 0) {
        ElMessage.warning({
          message: '开始试验前请先进行样品领用！'
        });
        return false;
      }
      const postdata = {};
      postdata.capabilityId = state.jsonData.capabilityId;
      postdata.experimentId = state.jsonData.experimentId;
      postdata.samplesId = state.jsonData.samplesId;
      // if (state.jsonData.mateType === '266013' || state.jsonData.mateType === '266015') {
      getInfoByExperimentId({ sampleId: postdata.samplesId, capabilityId: postdata.capabilityId }).then(res => {
        const info = res.data.data;
        // 判断是否需要维护了线芯
        if (info.coreExist) {
          // 判断是否维护了线芯
          if (info.coreColour) {
            if (isItemView) {
              context.emit('showRawTemplate', 'edit');
            } else {
              add(postdata);
            }
          } else {
            ElMessage.warning({
              message: '请先去维护试样分组，再添加模板'
            });
          }
        } else {
          if (isItemView) {
            context.emit('showRawTemplate', 'edit');
          } else {
            add(postdata);
          }
        }
      });
      return false;
    };
    // 绑定模板
    const add = postdata => {
      downloadByCapabilityId(postdata).then(res => {
        if (res) {
          if (route.name === 'recordReviewDetail') {
            router.push({
              path: '/recordReview/addRecord',
              query: {
                experimentId: state.jsonData.experimentId,
                samplesId: state.jsonData.samplesId,
                capabilityId: state.jsonData.capabilityId,
                type: 'edit',
                new: true
              }
            });
          } else {
            router.push({
              path: '/execution/addRecord',
              query: {
                experimentId: state.jsonData.experimentId,
                samplesId: state.jsonData.samplesId,
                capabilityId: state.jsonData.capabilityId,
                type: 'edit',
                new: true
              }
            });
          }
        }
      });
    };
    // 数据采集
    const handleDataAcquisition = () => {
      findDeviceByCapabilityId(state.jsonData.capabilityId).then(res => {
        if (res) {
          state.associatedDevice = res.data.data;
          if (state.associatedDevice.length) {
            state.dialogAssociated = true;
          } else {
            ElMessage.warning({
              message: '请先绑定设备!'
            });
          }
        }
      });
    };
    return {
      ...toRefs(state),
      closeDialog,
      getPermissionBtn,
      handleDataAcquisition,
      handleSafeguard,
      handleAddRecord,
      getNamesByid,
      add,
      filterSampleUnitToName,
      showTestMethod,
      onRequirementEdit,
      onRequirementCancel,
      onRequirementSave
    };
  },
  computed: {},
  created() {}
};
</script>

<style lang="scss" scoped>
.method-link {
  color: $tes-primary;
  cursor: pointer;
}

.originalRecord {
  margin-top: 20px;
  position: relative;
  .btn-group {
    position: absolute;
    top: 0;
    right: 20px;
    text-align: right;
    z-index: 9;
  }
}
:deep(.el-form-item__content) {
  color: $tes-font;
}
:deep(.el-form-item--medium .el-form-item__content) {
  line-height: 32px;
}
:deep(.el-form-item--medium .el-form-item__label) {
  color: $tes-font1;
  height: 32px;
  line-height: 32px;
}
:deep(.el-form .el-form-item) {
  margin-bottom: 8px;
}
.requirement-text {
  display: inline-block;
  max-width: 50%;
  vertical-align: bottom;
}
.requirement-input {
  width: 300px;
  margin-right: 10px;
}
.no-select.el-button.is-circle {
  border: none;
}
</style>
