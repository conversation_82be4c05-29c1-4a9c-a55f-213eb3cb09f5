export const tableColumn = [
  {
    id: '*********3332053085',
    viewId: '*********3227195399',
    fieldKey: 'salesOrderCode',
    fieldName: '销售订单号',
    fieldType: 'text',
    fieldTenantType: -1,
    columnWidth: '160',
    columnFixedType: 0,
    isMinWidth: 1,
    order: 0,
    isQuery: 1,
    isShow: 1,
    isHide: 0,
    isSortable: 0,
    groupCode: ''
  },
  {
    id: '1945306711733669918',
    viewId: '*********3227195399',
    fieldKey: 'salesOrderItemNo',
    fieldName: '销售行号',
    fieldType: 'text',
    fieldTenantType: -1,
    columnWidth: '200',
    columnFixedType: 0,
    isMinWidth: 0,
    order: 1,
    isQuery: 1,
    isShow: 1,
    isHide: 0,
    isSortable: 0,
    groupCode: ''
  },
  {
    id: '*********3428522016',
    viewId: '*********3227195399',
    fieldKey: 'customerName',
    fieldName: '客户名称',
    fieldType: 'text',
    fieldTenantType: -1,
    columnWidth: '160',
    columnFixedType: 0,
    isMinWidth: 0,
    order: 6,
    isQuery: 1,
    isShow: 1,
    isHide: 0,
    isSortable: 0,
    groupCode: ''
  },
  {
    id: '*********3428522015',
    viewId: '*********3227195399',
    fieldKey: 'projectName',
    fieldName: '项目名称',
    fieldType: 'text',
    fieldTenantType: -1,
    columnWidth: '160',
    columnFixedType: 0,
    isMinWidth: 0,
    order: 5,
    isQuery: 1,
    isShow: 1,
    isHide: 0,
    isSortable: 0,
    groupCode: ''
  },
  {
    id: '*********3428522015',
    viewId: '*********3227195399',
    fieldKey: 'materialNo',
    fieldName: '物料编号',
    fieldType: 'text',
    fieldTenantType: -1,
    columnWidth: '160',
    columnFixedType: 0,
    isMinWidth: 0,
    order: 5,
    isQuery: 1,
    isShow: 1,
    isHide: 0,
    isSortable: 0,
    groupCode: ''
  },
  {
    id: '*********3420133400',
    viewId: '*********3227195399',
    fieldKey: 'materialDesc',
    fieldName: '物料名称',
    fieldType: 'text',
    fieldTenantType: -1,
    columnWidth: '160',
    columnFixedType: 0,
    isMinWidth: 1,
    order: 3,
    isQuery: 1,
    isShow: 1,
    isHide: 0,
    isSortable: 0,
    groupCode: ''
  },
  {
    id: '*********3424327704',
    viewId: '*********3227195399',
    fieldKey: 'companyModel',
    fieldName: '型号规格',
    fieldType: 'text',
    fieldTenantType: -1,
    columnWidth: '120',
    columnFixedType: 0,
    isMinWidth: 1,
    order: 4,
    isQuery: 1,
    isShow: 1,
    isHide: 0,
    isSortable: 0,
    groupCode: ''
  },
  {
    id: '*********3424327714',
    viewId: '*********3227195399',
    fieldKey: 'companyContractCode',
    fieldName: '公司合同号',
    fieldType: 'text',
    fieldTenantType: -1,
    columnWidth: '120',
    columnFixedType: 0,
    isMinWidth: 1,
    order: 4,
    isQuery: 1,
    isShow: 1,
    isHide: 0,
    isSortable: 0,
    groupCode: ''
  },
  {
    id: '*********3424327705',
    viewId: '*********3227195399',
    fieldKey: 'buyerContractCode',
    fieldName: '买方合同号',
    fieldType: 'text',
    fieldTenantType: -1,
    columnWidth: '120',
    columnFixedType: 0,
    isMinWidth: 0,
    order: 7,
    isQuery: 0,
    isShow: 1,
    isHide: 0,
    isSortable: 0,
    groupCode: ''
  },
  {
    id: '*********3436910623',
    viewId: '*********3227195399',
    fieldKey: 'customerModel',
    fieldName: '客户型号',
    fieldType: 'text',
    fieldTenantType: -1,
    columnWidth: '180',
    columnFixedType: 0,
    isMinWidth: 0,
    order: 8,
    isQuery: 0,
    isShow: 1,
    isHide: 0,
    isSortable: 0,
    groupCode: ''
  },
  {
    id: '*********3441104985',
    viewId: '*********3227195399',
    fieldKey: 'customerSpecifications',
    fieldName: '客户规格',
    fieldType: 'text',
    fieldTenantType: -1,
    columnWidth: '120',
    columnFixedType: 0,
    isMinWidth: 0,
    order: 9,
    isQuery: 0,
    isShow: 0,
    isHide: 0,
    isSortable: 0,
    groupCode: ''
  },
  {
    id: '*********3445299260',
    viewId: '*********3227195399',
    fieldKey: 'customerVoltage',
    fieldName: '客户电压',
    fieldType: 'text',
    fieldTenantType: -1,
    columnWidth: '120',
    columnFixedType: 0,
    isMinWidth: 0,
    order: 10,
    isQuery: 0,
    isShow: 1,
    isHide: 0,
    isSortable: 0,
    groupCode: ''
  },
  {
    id: '*********3445299260',
    viewId: '*********3227195399',
    fieldKey: 'companyModel',
    fieldName: '公司型号规格',
    fieldType: 'text',
    fieldTenantType: -1,
    columnWidth: '120',
    columnFixedType: 0,
    isMinWidth: 0,
    order: 10,
    isQuery: 0,
    isShow: 1,
    isHide: 0,
    isSortable: 0,
    groupCode: ''
  },
  {
    id: '*********3445299260',
    viewId: '*********3227195399',
    fieldKey: 'model',
    fieldName: '物料特征值-型号',
    fieldType: 'text',
    fieldTenantType: -1,
    columnWidth: '120',
    columnFixedType: 0,
    isMinWidth: 0,
    order: 10,
    isQuery: 0,
    isShow: 1,
    isHide: 0,
    isSortable: 0,
    groupCode: ''
  },
  {
    id: '*********3445299260',
    viewId: '*********3227195399',
    fieldKey: 'specifications',
    fieldName: '物料特征值-规格',
    fieldType: 'text',
    fieldTenantType: -1,
    columnWidth: '120',
    columnFixedType: 0,
    isMinWidth: 0,
    order: 10,
    isQuery: 0,
    isShow: 1,
    isHide: 0,
    isSortable: 0,
    groupCode: ''
  },
  {
    id: '*********3445299260',
    viewId: '*********3227195399',
    fieldKey: 'voltageLevel',
    fieldName: '物料特征值-电压等级',
    fieldType: 'text',
    fieldTenantType: -1,
    columnWidth: '180',
    columnFixedType: 0,
    isMinWidth: 0,
    order: 10,
    isQuery: 0,
    isShow: 1,
    isHide: 0,
    isSortable: 0,
    groupCode: ''
  },
  {
    id: '*********3445299260',
    viewId: '*********3227195399',
    fieldKey: 'standard',
    fieldName: '物料特征值-标准',
    fieldType: 'text',
    fieldTenantType: -1,
    columnWidth: '120',
    columnFixedType: 0,
    isMinWidth: 0,
    order: 10,
    isQuery: 0,
    isShow: 1,
    isHide: 0,
    isSortable: 0,
    groupCode: ''
  },
  {
    id: '*********3445299260',
    viewId: '*********3227195399',
    fieldKey: 'color',
    fieldName: '物料特征值-颜色',
    fieldType: 'text',
    fieldTenantType: -1,
    columnWidth: '120',
    columnFixedType: 0,
    isMinWidth: 0,
    order: 10,
    isQuery: 0,
    isShow: 1,
    isHide: 0,
    isSortable: 0,
    groupCode: ''
  },
  {
    id: '*********3445299260',
    viewId: '*********3227195399',
    fieldKey: 'nuclearMarker',
    fieldName: '核标识',
    fieldType: 'custom',
    fieldTenantType: -1,
    columnWidth: '120',
    columnFixedType: 0,
    isMinWidth: 0,
    order: 10,
    isQuery: 0,
    isShow: 1,
    isHide: 0,
    isSortable: 0,
    groupCode: ''
  },
  {
    id: '*********3445299260',
    viewId: '*********3227195399',
    fieldKey: 'segmentLengthTxt',
    fieldName: '生产段长描述',
    fieldType: 'text',
    fieldTenantType: -1,
    columnWidth: '120',
    columnFixedType: 0,
    isMinWidth: 0,
    order: 10,
    isQuery: 0,
    isShow: 1,
    isHide: 0,
    isSortable: 0,
    groupCode: ''
  },
  {
    id: '*********3445299260',
    viewId: '*********3227195399',
    fieldKey: 'printRequirement',
    fieldName: '印字要求',
    fieldType: 'text',
    fieldTenantType: -1,
    columnWidth: '120',
    columnFixedType: 0,
    isMinWidth: 0,
    order: 10,
    isQuery: 0,
    isShow: 1,
    isHide: 0,
    isSortable: 0,
    groupCode: ''
  },
  {
    id: '*********3445299260',
    viewId: '*********3227195399',
    fieldKey: 'orderQuantity',
    fieldName: '销售订单数量',
    fieldType: 'text',
    fieldTenantType: -1,
    columnWidth: '120',
    columnFixedType: 0,
    isMinWidth: 0,
    order: 10,
    isQuery: 0,
    isShow: 1,
    isHide: 0,
    isSortable: 0,
    groupCode: ''
  },
  {
    id: '*********3445299260',
    viewId: '*********3227195399',
    fieldKey: 'quantityUnit',
    fieldName: '数量单位',
    fieldType: 'text',
    fieldTenantType: -1,
    columnWidth: '120',
    columnFixedType: 0,
    isMinWidth: 0,
    order: 10,
    isQuery: 0,
    isShow: 1,
    isHide: 0,
    isSortable: 0,
    groupCode: ''
  },
  {
    id: '*********3445299260',
    viewId: '*********3227195399',
    fieldKey: 'plannedCompletionDate',
    fieldName: '计划完成日期',
    fieldType: 'date',
    fieldTenantType: -1,
    columnWidth: '120',
    columnFixedType: 0,
    isMinWidth: 0,
    order: 10,
    isQuery: 0,
    isShow: 1,
    isHide: 0,
    isSortable: 0,
    groupCode: ''
  },
  {
    id: '*********3445299260',
    viewId: '*********3227195399',
    fieldKey: 'remark',
    fieldName: '备注',
    fieldType: 'text',
    fieldTenantType: -1,
    columnWidth: '120',
    columnFixedType: 0,
    isMinWidth: 0,
    order: 10,
    isQuery: 0,
    isShow: 1,
    isHide: 0,
    isSortable: 0,
    groupCode: ''
  },
  {
    id: '*********3445299260',
    viewId: '*********3227195399',
    fieldKey: 'shippingDate',
    fieldName: '发货日期',
    fieldType: 'date',
    fieldTenantType: -1,
    columnWidth: '120',
    columnFixedType: 0,
    isMinWidth: 0,
    order: 10,
    isQuery: 0,
    isShow: 1,
    isHide: 0,
    isSortable: 0,
    groupCode: ''
  }
];
