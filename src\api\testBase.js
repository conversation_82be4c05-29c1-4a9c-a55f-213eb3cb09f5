import request from '@/utils/request';

// 检测依据查询列表
export function getProductList(data) {
  return request({
    url: '/api-capabilitystd/standard/standardproduct/list/',
    method: 'post',
    data
  });
}

// 标准库项目分类树
export function getItemSort(id) {
  return request({
    url: `/api-capabilitystd/standard/projectcategory/listTree/${id}`,
    method: 'post'
  });
}

// 保存检测依据项目分类树
export function addItemTree(data) {
  return request({
    url: `/api-capabilitystd/standard/projectcategory/save`,
    method: 'post',
    data
  });
}

// 编辑检测依据项目分类树
export function updateItemTree(data) {
  return request({
    url: `/api-capabilitystd/standard/projectcategory/update`,
    method: 'post',
    data
  });
}

// 判断是否能删除检测依据项目分类树
export function canDelete(id) {
  return request({
    url: `/api-capabilitystd/standard/standardcategory/candelete/${id}`,
    method: 'post'
  });
}
// 判断检测依据项目分类树是否能删除
export function canDeleteSort(id) {
  return request({
    url: `/api-capabilitystd/standard/projectcategory/candelete/${id}`,
    method: 'post'
  });
}
// 删除检测依据项目分类树
export function deleteItemTree(id) {
  return request({
    url: `/api-capabilitystd/standard/projectcategory/delete/${id}`,
    method: 'post',
    id
  });
}

// 检测依据提交
export function saveTable(data) {
  return request({
    url: `/api-capabilitystd/standard/parameter/saveList`,
    method: 'post',
    data
  });
}
// 标准库保存
export function updateTable(data) {
  return request({
    url: `/api-capabilitystd/standard/standardproduct/updateDraft`,
    method: 'post',
    data
  });
}
// 标准库发布
export function issueTable(data) {
  return request({
    url: `/api-capabilitystd/standard/standardproduct/publish`,
    method: 'post',
    data
  });
}
// 检测依据项目删除
export function deleteTable(data) {
  return request({
    url: `/api-capabilitystd/standard/parameter/delete`,
    method: 'post',
    data
  });
}
// 根据Id查询检测项目信息
export function getItemList(data) {
  return request({
    url: `/api-capabilitystd/standard/parameter/list`,
    method: 'post',
    data
  });
}
// 保存检测项目信息
export function saveCapability(data) {
  return request({
    url: '/api-capabilitystd/capability/capability/save',
    method: 'post',
    data
  });
}
// 修改检测项目信息
export function updateCapability(data) {
  return request({
    url: '/api-capabilitystd/capability/capability/update',
    method: 'post',
    data
  });
}
// 删除检测项目信息
export function deleteCapability(data) {
  return request({
    url: '/api-capabilitystd/capability/capability/delete',
    method: 'post',
    data
  });
}
// 检测依据标准分类树结构数据
export function getTree(data) {
  return request({
    url: `/api-capabilitystd/standard/standardcategory/listTree/${data}`,
    method: 'post'
  });
}
// 根据Id查询检测项目分类信息
export function getCapabilityTreeById(id) {
  return request({
    url: `/api-capabilitystd/capabilitycategory/info/${id}`,
    method: 'get'
  });
}
// 新增检测依据树节点
export function addTreeNode(data) {
  return request({
    url: '/api-capabilitystd/standard/standardcategory/save',
    method: 'post',
    data
  });
}
// 更新检测依据列表树节点
export function updateTreeNode(data) {
  return request({
    url: '/api-capabilitystd/standard/standardcategory/update',
    method: 'post',
    data
  });
}
// 删除检测依据树节点
export function deleteProductTree(id) {
  return request({
    url: `/api-capabilitystd/standard/standardcategory/delete/${id}`,
    method: 'post'
  });
}

// 检测依据添加产品
export function saveProduct(data) {
  return request({
    url: `/api-capabilitystd/standard/standardproduct/save`,
    method: 'post',
    data
  });
}

// 检测依据复制产品
export function copyProduct(data) {
  return request({
    url: `/api-capabilitystd/standard/standardproduct/copy`,
    method: 'post',
    data
  });
}

// 检测依据编辑产品
export function updateProduct(data) {
  return request({
    url: `/api-capabilitystd/standard/standardproduct/update`,
    method: 'post',
    data
  });
}

// 删除检测依据产品
export function deleteProductList(data) {
  return request({
    url: `/api-capabilitystd/standard/standardproduct/delete`,
    method: 'post',
    data
  });
}

// 检测依据列表启用和停用
export function enableProduct(data) {
  return request({
    url: `/api-capabilitystd/standard/standardproduct/updaeSwitch`,
    method: 'post',
    data
  });
}

// 获取关键参数
export function getCapabilityInfo(capabilityId) {
  return request({
    url: `/api-capabilitystd/capability/capabilitypara/info/${capabilityId}`,
    method: 'get'
  });
}
// 保存关键参数
export function saveCapabilitypara(data) {
  return request({
    url: '/api-capabilitystd/capability/capabilitypara/save',
    method: 'post',
    data
  });
}
// 更新关键参数
export function updateCapabilitypara(data) {
  return request({
    url: '/api-capabilitystd/capability/capabilitypara/update',
    method: 'post',
    data
  });
}
// 删除关键参数
export function deleteCapabilitypara(data) {
  return request({
    url: '/api-capabilitystd/capability/capabilitypara/delete',
    method: 'delete',
    data
  });
}
// 根据检测项目Id获取第三方库
export function extcapabilitymapinfo(capabilityId) {
  return request({
    url: '/api-capabilitystd/capability/extcapabilitymap/info/' + capabilityId,
    method: 'get'
  });
}
// 删除检测项目关联第三方库
export function deleteExtcapabilitymap(data) {
  return request({
    url: '/api-capabilitystd/capability/extcapabilitymap/delete',
    method: 'delete',
    data
  });
}
// 更新检测项目关联第三方库
export function extcapabilitymapSave(data) {
  return request({
    url: '/api-capabilitystd/capability/extcapabilitymap/save',
    method: 'post',
    data
  });
}
// 检测依据获取产品型号
export function getProductModel(categoryId) {
  return request({
    url: `/api-material/material/categoryproductmodel/info/${categoryId}`,
    method: 'get'
  });
}
// 树分类拖动排序
export function updateOrderTree(data) {
  return request({
    url: '/api-capabilitystd/standard/standardcategory/updateOrder ',
    method: 'post',
    data
  });
}
// 项目树分类拖动排序
export function updateOrderTree2(data) {
  return request({
    url: '/api-capabilitystd/standard/projectcategory/updateOrder ',
    method: 'post',
    data
  });
}
// 新增型号
export function addModelForm(data) {
  return request({
    url: '/api-capabilitystandard/capabilitystandard/standardmodel/save',
    method: 'post',
    data
  });
}
// 编辑型号
export function editModelForm(data) {
  return request({
    url: '/api-material/material/categoryproductmodel/update',
    method: 'post',
    data
  });
}
// 删除型号
export function deleteModelForm(ids) {
  return request({
    url: '/api-material/material/categoryproductmodel/delete',
    method: 'post',
    ids
  });
}
// 查询产品详情
export function getProductDetail(id) {
  return request({
    url: `/api-capabilitystd/standard/standardproduct/info/${id}`,
    method: 'get'
  });
}
// 查询版本
export function getVersionApi(id) {
  return request({
    url: `/api-capabilitystd/standard/standardproduct/info/${id}`,
    method: 'get'
  });
}
// 删除草稿状态
export function deleteDraftApi(versionId) {
  return request({
    url: `/api-capabilitystd/standard/standardproduct/deleteDraft/${versionId}`,
    method: 'get'
  });
}

// #region 标准库详情-导入/导出标准

/**
 * 导入标准Excel文件
 * @param {*} data
 * @returns
 */
export function importStandardExcel(data, standardProductVersionId) {
  return request({
    url: `/api-capabilitystd/standard/projectcategory/importExcel/${standardProductVersionId}`,
    method: 'post',
    headers: { 'Content-Type': 'multipart/form-data' },
    onUploadProgress: function (progressEvent) {
      // 原生获取上传进度的事件
    },
    data
  });
}

/**
 * 导出标准Excel文件
 * @param {*} data
 * @returns
 */
export function exportStandardExcel(data) {
  return request({
    url: '/api-capabilitystd/capability/inspectionstrategy/itemconfig/inspectionExport',
    method: 'post',
    data
  });
}
// 根据型号id获取型号检测依据
export function standardcategoryApi(data) {
  return request({
    url: '/api-capabilitystd/standard/standardcategory/list',
    method: 'post',
    data
  });
}
// 根据型号id获取型号检测依据
export function findByFeats(data) {
  return request({
    url: '/api-capabilitystd/capability/capabilityStandardBasis/findByFeats',
    method: 'post',
    data
  });
}
// 导出标准库
export function standardcategoryExport(data) {
  return request({
    url: '/api-capabilitystd/standard/standardcategory/export',
    responseType: 'blob',
    method: 'post',
    data
  });
}

// #endregion
