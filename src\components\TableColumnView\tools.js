export const filterTableColumnDataByTenantType = (tableColumnData, tenantType) => {
  const fieldKeyMap = new Map();
  for (let index = 0; index < tableColumnData.length; index++) {
    const item = tableColumnData[index];
    if (fieldKeyMap.has(item.fieldKey)) {
      const mapItem = fieldKeyMap.get(item.fieldKey);
      mapItem.push(item);
      fieldKeyMap.set(item.fieldKey, mapItem);
    } else {
      fieldKeyMap.set(item.fieldKey, [item]);
    }
  }
  const uniqueFieldItemMap = new Map();
  for (const [fieldKey, mapItem] of fieldKeyMap) {
    if (mapItem.length > 1) {
      let uniqueFieldItem = mapItem.find(item => item.fieldTenantType === tenantType);
      if (!uniqueFieldItem) {
        uniqueFieldItem = mapItem.find(item => item.fieldTenantType === -1);
      }
      if (uniqueFieldItem) {
        uniqueFieldItemMap.set(fieldKey, uniqueFieldItem);
      }
    }
  }
  return tableColumnData.filter(item => {
    if (uniqueFieldItemMap.has(item.fieldKey)) {
      const uniqueFieldItem = uniqueFieldItemMap.get(item.fieldKey);
      return item.fieldTenantType === uniqueFieldItem.fieldTenantType;
    } else {
      return item.fieldTenantType === -1 || item.fieldTenantType === tenantType;
    }
  });
};
