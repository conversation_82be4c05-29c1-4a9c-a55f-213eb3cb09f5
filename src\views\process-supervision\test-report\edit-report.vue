<template>
  <!-- 检测报告编辑 -->
  <DetailLayout :main-offset-top="100">
    <template #page-header>
      <div class="header-flex flex-start">
        <el-space :size="40" :spacer="spacer">
          <div class="item-column">
            <span class="item-label">样品编号</span>
            <div class="item-content">{{ samplesDetails.secSampleNum }}</div>
          </div>
          <div class="item-column">
            <span class="item-label">报告编号</span>
            <div class="item-content">{{ samplesDetails.reportNo }}</div>
          </div>
        </el-space>
        <el-button v-if="currentStepActive !== 5" size="large" type="primary" @click="handlePreview">预览</el-button>
      </div>
    </template>
    <div class="report-main">
      <el-steps v-if="currentStepActive !== 5" :active="currentStepActive" finish-status="success" align-center simple>
        <el-step v-for="(step, index) in stepData" :key="index" :icon="step.icon">
          <template #title>
            <span>{{ step.title }}</span>
          </template>
        </el-step>
      </el-steps>
      <div v-if="currentStepActive !== 5" class="main-info">
        <!-- 样品信息 -->
        <edit-report-sample-info
          v-if="currentStepActive === 0"
          ref="editReportSIRef"
          :data="samplesDetails"
          @setInfo="getNewInfo"
        />
        <!-- 检测结果汇总 -->
        <edit-report-summary
          v-if="currentStepActive === 1"
          ref="editSummaryRef"
          v-loading="loadingStep2"
          :select="summarySelect"
          :table="summaryTableData"
          @setInfo="getNewSummaryTableData"
        />
        <!-- 仪器设备 -->
        <instruments-equipment
          v-if="currentStepActive === 2"
          :show="showInstruments"
          :table="instrumentsTableData"
          @setInfo="getInstrumentsList"
        />
        <!-- 试验图片 -->
        <test-picture
          v-if="currentStepActive === 3"
          :data="imgList"
          :key-list="imgKeyList"
          @setInfo="getImgList"
          @refreshImg="refreshImg"
        />
        <!-- 检测结论 -->
        <test-result
          v-if="currentStepActive === 4"
          ref="testNewResultRef"
          :data="testResultDetails"
          @setInfo="getNewResultInfo"
        />
      </div>
      <div v-if="currentStepActive === 5">
        <!-- <el-button style="margin:20px;" @click="preViewPdf">预览</el-button>
        <el-button style="margin:20px;" @click="downloadPdf">下载</el-button> -->
        <submit-sucess
          :data="submitSucessDetail"
          :loading="downloadLoading"
          @download="downloadPdf"
          @detail="showDetailPage"
          @preview="handlePreview"
        />
      </div>
    </div>
    <!-- pdf预览 -->
    <!-- <el-image-viewer v-if="showPdfViewer" :url-list="pdfList" @close="closePdfViewer" /> -->
    <!-- 判定标准更新弹出框 -->
    <test-basis-update :show="showTestBasisUpdate" :data="testBasisUpdateList" @setInfo="showTestBasicUpdate" />
    <!-- 选择审批人 -->
    <el-dialog
      v-model="showApprovalDialog"
      title="选择审批人"
      width="480px"
      :close-on-click-modal="false"
      @close="batchApprovalDialog = false"
    >
      <el-form ref="selectformRef" :model="selectFrom" label-position="right" label-width="110px" size="small">
        <el-form-item label="审批人：" prop="assigneeList">
          <el-select
            v-model="selectFrom.assigneeList"
            multiple
            placeholder="请选择审批人"
            clearable
            filterable
            style="width: 100%"
          >
            <el-option v-for="item in userOptions" :key="item.id" :label="item.nickname" :value="item.id" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showApprovalDialog = false">取 消</el-button>
          <el-button
            type="primary"
            :loading="approvalSuccessLoading"
            @click="approvalSuccess"
            @keyup.prevent
            @keydown.enter.prevent
            >确 定</el-button
          >
        </span>
      </template>
    </el-dialog>
    <!-- 预览功能 -->
    <!-- <word-to-html :drawer="isShowWordDrawer" :data="showWordDrawerData" @close="closeDrawer" /> -->

    <template #other>
      <BottomPanel v-if="currentStepActive != 5">
        <template #panel-content>
          <div style="text-align: left; width: 50%; float: left; margin-bottom: 5px">
            <el-button
              v-if="currentStepActive === 1"
              type="primary"
              @click="resetCapabilitydata()"
              @keyup.prevent
              @keydown.enter.prevent
              >数据重置</el-button
            >
          </div>
          <div style="text-align: right; width: 50%; float: right; margin-bottom: 5px">
            <el-button v-if="currentStepActive > 0" @click="preStep" @keyup.prevent @keydown.enter.prevent
              >上一步</el-button
            >
            <el-button
              v-if="currentStepActive <= 4 && currentInfo.reportStage >= 1"
              type="primary"
              @click="saveCurrentStep"
              @keyup.prevent
              @keydown.enter.prevent
              >保存</el-button
            >
            <el-button
              v-if="currentStepActive < 4"
              type="primary"
              :loading="nextStepLoading"
              @click="nextStep"
              @keyup.prevent
              @keydown.enter.prevent
              >下一步</el-button
            >
            <el-button
              v-if="currentStepActive === 4"
              type="warning"
              @click="submitApproval"
              @keyup.prevent
              @keydown.enter.prevent
              >提交审批</el-button
            >
          </div>
        </template>
      </BottomPanel>
    </template>
  </DetailLayout>
</template>

<script>
import { h, reactive, toRefs, getCurrentInstance, watch, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import {
  getSampleInfoByReportId,
  updateSampleInfoReport,
  getDeviceList,
  saveDeviceList,
  getReportImgList,
  saveReportImgList,
  getResultByReportId,
  saveResultByIds,
  saveReportExpInfo,
  saveReportExpInfo2,
  exportWord,
  processSubmit,
  processExecute
} from '@/api/testReport';
import EditReportSampleInfo from './edit-report-sample-info.vue';
import EditReportSummary from './edit-report-summary.vue';
import InstrumentsEquipment from './instruments-equipment.vue';
import TestPicture from './test-picture.vue';
import TestResult from './test-result.vue';
import TestBasisUpdate from './test-basis-update.vue';
import SubmitSucess from './submit-sucess.vue';
import pdf from '@/utils/preview-or-download-pdf';
// import { formatDateTimeName } from '@/utils/formatTime'
// import { getFloatByNum } from '@/utils/formatJson'
import router from '@/router/index.js';
import { getNameByid, getNamesByid } from '@/utils/common';
// import { ElMessageBox } from 'element-plus'
import { setCurrentStage, getCurrentReportInfo, setCurrentReportInfo, getLoginInfo } from '@/utils/auth';
import { useStore } from 'vuex';
import { ElMessage } from 'element-plus';
// import { reportAudit } from '@/api/permission'
import { addByTemp } from '@/api/messageAgent';
import DetailLayout from '@/components/DetailLayout';
import { ElDivider } from 'element-plus';
import { getReportUserList } from '@/api/user';
import BottomPanel from '@/components/PageComponents/BottomPanel';
import { getImgLabelListByMaterialCode } from '@/api/material';
// import WordToHtml from '@/components/WordToHtml/index.vue'
// import _ from 'lodash'

export default {
  name: 'EditReport',
  components: {
    DetailLayout,
    BottomPanel,
    EditReportSampleInfo,
    EditReportSummary,
    InstrumentsEquipment,
    TestPicture,
    TestResult,
    TestBasisUpdate,
    SubmitSucess
  },
  setup() {
    const { proxy } = getCurrentInstance();
    const store = useStore().state;
    const route = useRoute();
    const currentInfo = getCurrentReportInfo();
    const spacer = h(ElDivider, { direction: 'vertical' });
    // console.log(getCurrentReportInfo())
    const datas = reactive({
      currentAccountId: getLoginInfo().accountId,
      newCurrentInfo: currentInfo,
      list: [],
      currentStepActive: 0,
      stepData: [
        { title: '确认样品信息', icon: 'el-icon-time' },
        { title: '检测结果汇总及排序', icon: 'el-icon-time' },
        { title: '仪器设备', icon: 'el-icon-time' },
        { title: '试验图片', icon: 'el-icon-time' },
        { title: '检测结论', icon: 'el-icon-time' }
      ],
      SampleId: route.query.sampleId,
      orderId: route.query.orderId,
      downloadLoading: false,
      samplesDetails: {},
      auditNameList: [],
      oldSamplesDetails: {},
      summaryTableData: [],
      summarySelect: null,
      instrumentsTableData: [],
      imgList: [],
      showPdfViewer: false,
      pdfList: [],
      currentSampleInfoParam: null,
      currentSummaryTableData: [],
      currentInstrumentsList: [],
      currentImgsList: [],
      showTestBasisUpdate: false,
      showInstruments: false,
      noAddStep: false,
      testBasisUpdateList: [],
      testResultDetails: {},
      currentResultsInfo: {},
      showApprovalDialog: false,
      userOptions: store.common.nameList,
      selectFrom: {
        assigneeList: [],
        businessKey: '',
        isAssent: 1
      },
      submitSucessDetail: {},
      loadingStep2: false,
      approvalSuccessLoading: false,
      isShowWordDrawer: false,
      showWordDrawerData: {},
      imgKeyList: [],
      nextStepLoading: false
    });

    watch(
      () => datas.list,
      (newValue, oldValue) => {
        proxy.isChangeDatas(newValue, datas.oldList);
      },
      {
        deep: true,
        immediate: false
      }
    );
    // 监控当前阶段更新
    watch(
      () => datas.currentStepActive,
      (newValue, oldValue) => {
        // console.log(newValue)
        setCurrentStage(newValue + 1);
      },
      {
        deep: true,
        immediate: false
      }
    );
    // 获取具有检测报告权限的人员列表
    const getReportUser = () => {
      datas.nextStepLoading = true;
      getReportUserList({}).then(res => {
        datas.nextStepLoading = false;
        if (res.data.code === 200) {
          datas.userOptions = res.data.data;
        }
      });
    };
    // 上一步
    const preStep = async () => {
      if (datas.currentStepActive === 0) {
        return false;
      }
      if (datas.currentStepActive === 1) {
        // 第二步 -> 1
        await getSamplesDetails();
      } else if (datas.currentStepActive === 2) {
        // 第三步 -> 2
        // 第二步详情接口
        // await proxy.getReportSummaryInfo(currentInfo)
      } else if (datas.currentStepActive === 3) {
        // 第四步 -> 3
        datas.showInstruments = true;
        await proxy.getDeviceLists(currentInfo);
      } else {
        // 第五步 -> 4
        await proxy.getReportImgLists(currentInfo);
      }
      datas.currentStepActive -= 1;
    };
    // 下一步
    const nextStep = async () => {
      if (datas.currentStepActive === 5) {
        return false;
      }
      if (datas.currentStepActive === 0) {
        // 第一步 -> 2
        var flag = true;
        const currentRef = proxy.$refs.editReportSIRef.$refs;
        currentRef.reportSampleInfoRef.validate(async valid => {
          if (valid) {
            if (datas.currentSampleInfoParam) {
              await proxy.saveStepOneReport(datas.currentSampleInfoParam);
              datas.currentStepActive += 1;
            }
          } else {
            flag = false;
          }
          return flag;
        });
        if (flag === false) {
          return false;
        }
      } else if (datas.currentStepActive === 1) {
        // 第二步 -> 3
        datas.showInstruments = true;
        await proxy.saveReportExpInfos(currentInfo);
        await proxy.getDeviceLists(currentInfo);
      } else if (datas.currentStepActive === 2) {
        // 第三步 -> 4
        datas.showInstruments = false;
        await proxy.saveDeviceLists(currentInfo);
        await proxy.getReportImgLists(currentInfo);
      } else if (datas.currentStepActive === 3) {
        // 第四步 -> 5
        await proxy.saveImgList(currentInfo.reportId, currentInfo.reportStage);
        await proxy.getResultsByReportId(currentInfo.reportId);
        getReportUser();
      }
      if (datas.noAddStep === false && datas.currentStepActive !== 0) {
        datas.currentStepActive += 1;
      }
      // datas.nextStepLoading = false
    };
    // 保存当前步骤数据
    const saveCurrentStep = () => {
      switch (datas.currentStepActive) {
        case 0:
          proxy.saveStepOneReport(datas.currentSampleInfoParam);
          break;
        case 1:
          proxy.saveReportExpInfos(currentInfo);
          break;
        case 2:
          proxy.saveDeviceLists(currentInfo);
          break;
        case 3:
          proxy.saveImgList(currentInfo.reportId, currentInfo.reportStage);
          break;
        case 4:
          proxy.saveResultsInfo();
          break;
      }
    };
    // 提交审批
    const submitApproval = () => {
      const currentRef = proxy.$refs.testNewResultRef.$refs;
      currentRef.testResultRef.validate(valid => {
        if (valid) {
          datas.selectFrom.businessKey = currentInfo.reportId;
          datas.showApprovalDialog = true;
        }
      });
    };
    // 选择审批人-确定
    const approvalSuccess = () => {
      if (datas.selectFrom.assigneeList.length) {
        Promise.all([proxy.saveResultsInfo(true)]).then(async res => {
          if (res[0]) {
            datas.approvalSuccessLoading = true;
            datas.currentResultsInfo.reportId = currentInfo.reportId;
            datas.selectFrom.tenantType = store.user.tenantInfo.type;
            if (currentInfo.processInstanceId) {
              datas.selectFrom.processInstanceId = currentInfo.processInstanceId;
              processExecute(datas.selectFrom).then(res => {
                if (res !== false) {
                  addMsg();
                  ElMessage.success('提交成功！');
                  datas.currentStepActive += 1;
                  datas.showApprovalDialog = false;
                  datas.approvalSuccessLoading = false;
                } else {
                  datas.approvalSuccessLoading = false;
                }
              });
            } else {
              processSubmit(datas.selectFrom).then(res => {
                if (res !== false) {
                  addMsg();
                  ElMessage.success('提交成功！');
                  datas.currentStepActive += 1;
                  datas.showApprovalDialog = false;
                  datas.approvalSuccessLoading = false;
                } else {
                  datas.approvalSuccessLoading = false;
                }
              });
            }
          }
        });
      } else {
        ElMessage.error('请选择审批人');
      }
    };
    // 添加消息待办
    const addMsg = () => {
      // 添加消息待办
      const params = {
        eventCode: 'M012',
        receiverType: '1',
        senderName: getNameByid(datas.currentAccountId),
        receiverIds: datas.selectFrom.assigneeList.toString(),
        receiverNames: getNamesByid(datas.selectFrom.assigneeList.toString()).toString(),
        c_ids: datas.samplesDetails.reportNo,
        c_b_samplesIdArray: datas.samplesDetails.sampleId,
        c_b_sampleNoArray: datas.samplesDetails.secSampleNum,
        c_b_reportNoArray: datas.samplesDetails.reportNo
      };
      addByTemp(params).then(res => {
        if (res !== false) {
          // console.log(res.data)
        }
      });
    };
    // 获取样品详情
    const getSamplesDetails = () => {
      const reportId = route.query.reportId;
      datas.nextStepLoading = true;
      getSampleInfoByReportId(reportId).then(resdata => {
        datas.nextStepLoading = false;
        if (resdata !== false) {
          datas.samplesDetails = resdata.data.data;
          getImgLabelList();
          datas.oldSamplesDetails = JSON.parse(JSON.stringify(datas.samplesDetails));
          datas.currentSampleInfoParam = datas.oldSamplesDetails;
          // console.log(datas.samplesDetails)
          datas.submitSucessDetail = {
            sampleName: datas.samplesDetails.sampleName,
            reportNo: datas.samplesDetails.reportNo,
            chargeName: getNameByid(datas.samplesDetails.chargeId),
            issueDate: ''
          };
        }
      });
    };
    // 获取修改的样品信息
    const getNewInfo = data => {
      // console.log(data)
      datas.currentSampleInfoParam = data;
    };
    // 判定标准更新
    const showTestBasicUpdate = async data => {
      // console.log(data)
      if (data === false) {
        datas.showTestBasisUpdate = data;
      }
      datas.noAddStep = false;
      // await proxy.getReportSummaryInfo(currentInfo)
      datas.currentStepActive += 1;
    };
    // 获取汇总信息
    const getNewSummaryTableData = data => {
      // console.log(data)
      datas.currentSummaryTableData = data;
    };
    // 获取选择的仪器设备
    const getInstrumentsList = data => {
      // console.log(data)
      datas.currentInstrumentsList = data;
    };
    // 获取试验图片
    const getImgList = data => {
      datas.currentImgsList = data;
    };
    // 刷新图片列表
    const refreshImg = data => {
      if (data === true) {
        proxy.getReportImgLists(currentInfo);
      }
    };
    // 获取检测结论
    const getNewResultInfo = data => {
      // console.log(data)
      datas.currentResultsInfo = data;
      // 签发日期
      datas.submitSucessDetail.issueDate = data.issueDate;
    };
    // 预览功能
    const preViewPdf = () => {
      const dom = proxy.$refs['reportRef']; // document.getElementById('app')
      const img = pdf.previewImg(dom);
      img.then(res => {
        // console.log(res)
        datas.pdfList.push(res);
        datas.showPdfViewer = true;
      });
    };
    // 预览点击
    const handlePreview = () => {
      // console.log(row)
      // datas.showWordDrawerData = currentInfo
      // datas.isShowWordDrawer = true
      const newRouter = currentInfo.pdfSealUrl
        ? router.resolve({
            path: `/preview-report-pdf/${currentInfo.reportId}/${currentInfo.mateType}/${currentInfo.sampleId}/${currentInfo.reportNo}`
          })
        : router.resolve({
            path: `/preview-report/${currentInfo.reportId}/${currentInfo.mateType}/${currentInfo.sampleId}/${currentInfo.reportNo}`
          });
      window.open(newRouter.href, '_blank');
    };
    // 关闭预览popver
    const closeDrawer = () => {
      datas.isShowWordDrawer = false;
    };
    // 查看详情页
    const showDetailPage = () => {
      // console.log('showDetailPage')
      router.push({
        path: '/detail-report',
        query: {
          reportId: currentInfo.reportId,
          sampleId: currentInfo.sampleId,
          reportStage: 6
        }
      });
    };
    // 下载功能
    const downloadPdf = () => {
      datas.downloadLoading = true;
      exportWord(currentInfo.reportId, currentInfo.mateType, currentInfo.sampleId).then(res => {
        datas.downloadLoading = false;
        if (res !== false) {
          const reader = new FileReader();
          reader.addEventListener('loadend', () => {
            try {
              const resdata = JSON.parse(reader.result);
              if (resdata.code === 400) {
                ElMessage({
                  message: resdata.message,
                  type: 'error',
                  duration: 3000
                });
              }
            } catch (error) {
              const blob = res.data;
              var suffix = datas.samplesDetails.pdfSealUrl ? '.pdf' : '.doc';
              var fileName = currentInfo.reportNo + '-' + currentInfo.sampleName + suffix;
              var downloadElement = document.createElement('a');
              var href = window.URL.createObjectURL(blob);
              downloadElement.style.display = 'none';
              downloadElement.href = href;
              downloadElement.download = decodeURI(fileName);
              document.body.appendChild(downloadElement);
              downloadElement.click();
              document.body.removeChild(downloadElement);
              window.URL.revokeObjectURL(href);
            }
          });
          reader.readAsText(res.data, 'utf-8');
        }
      });
    };
    // 获取资源权限
    // reportAudit('recordAudit').then(res => {
    //   datas.auditNameList = res.data
    // })
    // 关闭pdf预览
    const closePdfViewer = () => {
      datas.showPdfViewer = false;
      datas.pdfList = [];
    };

    // #region 检测项目数据重置

    function resetCapabilitydata() {
      proxy.$refs.editSummaryRef.handleReset();
    }

    function getImgLabelList() {
      // console.log(datas.samplesDetails)
      getImgLabelListByMaterialCode(datas.samplesDetails.mateType).then(res => {
        if (res) {
          datas.imgKeyList = res.data.data;
        }
      });
    }
    onMounted(() => {});

    // #endregion
    return {
      ...toRefs(datas),
      getNamesByid,
      preStep,
      nextStep,
      submitApproval,
      saveCurrentStep,
      addMsg,
      handlePreview,
      closeDrawer,
      getSamplesDetails,
      getNewInfo,
      getNewResultInfo,
      getInstrumentsList,
      getImgList,
      getNewSummaryTableData,
      preViewPdf,
      downloadPdf,
      closePdfViewer,
      approvalSuccess,
      showDetailPage,
      showTestBasicUpdate,
      refreshImg,
      currentInfo,
      spacer,
      resetCapabilitydata
    };
  },
  created() {
    this.getSamplesDetails();
    this.bus.$on('saveEditReport', msg => {
      this.bus.reloadEditReport = false;
    });
  },
  methods: {
    // 判断数据是否更新
    isChangeDatas(newList, oldList) {
      const _this = this;
      if (newList.length !== oldList.length) {
        _this.bus.reloadEditReport = true;
      }
    },
    // 确认样品信息保存
    saveStepOneReport(param) {
      var that = this;
      that.submitSucessDetail.sampleName = param.sampleName;
      return new Promise(resolve => {
        updateSampleInfoReport(param).then(res => {
          if (res !== false) {
            if (that.currentInfo.reportStage < 1) {
              that.newCurrentInfo.reportStage = 1;
              setCurrentReportInfo(that.newCurrentInfo);
            }
            ElMessage.success('数据保存成功!');
            resolve(res.data.data);
          }
        });
      });
    },
    // 保存修改的汇总信息
    saveReportExpInfos(currentInfo) {
      this.currentSummaryTableData.forEach(item => {
        item.childList.forEach((param, index) => {
          param.order = index;
        });
      });
      const param = {
        reportId: currentInfo.reportId,
        reportdetailexpinfoList: this.currentSummaryTableData
      };
      var that = this;
      if (currentInfo.reportStage >= 2) {
        that.nextStepLoading = true;
        saveReportExpInfo2(param).then(res => {
          that.nextStepLoading = false;
          if (res !== false) {
            // console.log(res)
            if (that.currentInfo.reportStage < 2) {
              that.newCurrentInfo.reportStage = 2;
              setCurrentReportInfo(that.newCurrentInfo);
            }
            ElMessage.success('数据保存成功!');
          }
        });
      } else {
        saveReportExpInfo(param).then(res => {
          if (res !== false) {
            // console.log(res)
            if (that.currentInfo.reportStage < 2) {
              that.newCurrentInfo.reportStage = 2;
              setCurrentReportInfo(that.newCurrentInfo);
            }
          }
        });
      }
    },
    // 获取设备列表
    getDeviceLists(currentInfo) {
      const that = this;
      const param = {
        reportStage: currentInfo.reportStage,
        sampleId: currentInfo.sampleId,
        reportId: currentInfo.reportId
      };
      that.nextStepLoading = true;
      return getDeviceList(param).then(res => {
        that.nextStepLoading = false;
        if (res !== false) {
          // console.log(res.data.data.deviceysageResponseList)
          that.instrumentsTableData = res.data.data.deviceysageResponseList;
        }
      });
    },
    // 更新仪器设备
    saveDeviceLists(currentInfo) {
      const that = this;
      const param = {
        deviceysageResponseList: that.currentInstrumentsList,
        reportStage: currentInfo.reportStage,
        reportId: currentInfo.reportId,
        samplesid: currentInfo.sampleId
      };
      that.nextStepLoading = true;
      saveDeviceList(param).then(res => {
        that.nextStepLoading = false;
        if (res !== false) {
          // console.log(res)
          if (that.currentInfo.reportStage < 3) {
            that.newCurrentInfo.reportStage = 3;
            setCurrentReportInfo(that.newCurrentInfo);
          }
          ElMessage.success('数据保存成功!');
        }
      });
    },
    // 获取试验图片列表
    getReportImgLists(currentInfo) {
      const that = this;
      const param = {
        sampleId: currentInfo.sampleId,
        reportStage: currentInfo.reportStage,
        reportId: currentInfo.reportId
      };
      that.nextStepLoading = true;
      return getReportImgList(param).then(res => {
        that.nextStepLoading = false;
        if (res !== false) {
          const list1 = res.data.data.list;
          const list2 = res.data.data.reportimageEntityList;
          const imageList = list1.concat(list2);
          imageList.forEach(item => {
            item.labelKeyList = item.labelList.map(item => item.labelKey);
          });
          that.imgList = imageList;
        }
      });
    },
    // 保存图片列表
    saveImgList(reportId, reportStage) {
      var that = this;
      const param = {
        reportId: reportId,
        reportStage: reportStage,
        list: this.currentImgsList
      };
      if (this.currentImgsList.length === 0) {
        param.list = this.imgList;
      }
      return saveReportImgList(param).then(img => {
        if (img !== false) {
          // console.log(img)
          if (that.currentInfo.reportStage < 4) {
            that.newCurrentInfo.reportStage = 4;
            setCurrentReportInfo(that.newCurrentInfo);
          }
          ElMessage.success('数据保存成功!');
        }
      });
    },
    // 获取检测结果信息
    getResultsByReportId(reportId) {
      const that = this;
      that.nextStepLoading = true;
      return getResultByReportId(reportId).then(res => {
        that.nextStepLoading = false;
        if (res !== false) {
          // console.log(res.data.data)
          that.testResultDetails = res.data.data;
        }
      });
    },
    // 保存检测结果界面信息
    saveResultsInfo(isSubmit) {
      var that = this;
      this.currentResultsInfo.reportId = this.currentInfo.reportId;
      if (isSubmit) {
        return new Promise((resolve, reject) => {
          saveResultByIds(this.currentResultsInfo).then(res => {
            if (res !== false) {
              if (that.currentInfo.reportStage < 5) {
                that.newCurrentInfo.reportStage = 5;
                setCurrentReportInfo(that.newCurrentInfo);
              }
              resolve(true);
              ElMessage.success('数据保存成功!');
            }
          });
        });
      } else {
        saveResultByIds(this.currentResultsInfo).then(res => {
          if (res !== false) {
            if (that.currentInfo.reportStage < 5) {
              that.newCurrentInfo.reportStage = 5;
              setCurrentReportInfo(that.newCurrentInfo);
            }
            ElMessage.success('数据保存成功!');
          }
        });
      }
    }
  }
};
</script>
<style lang="scss" scoped>
:deep(.el-divider--vertical) {
  height: 40px;
}
.report-main {
  .el-steps {
    background: $background-color;
    border-radius: 0;
  }
  .el-steps--simple {
    padding: 12px 80px;
    :deep(.el-step__head) {
      .el-step__line {
        display: none;
      }
      &.is-process {
        color: $tes-primary;
      }
      .el-step__icon-inner[class*='el-icon']:not(.is-status) {
        font-size: 16px;
      }
      .el-step__icon-inner.is-status.el-icon-check {
        font-size: 18px;
      }
    }
    :deep(.el-step__title) {
      max-width: 100% !important;
      line-height: 24px;
      &.is-process {
        color: $tes-primary;
        font-weight: normal;
      }
    }
    :deep(.el-step__arrow) {
      &::before,
      &::after {
        height: 14px;
        width: 1px;
      }
    }
  }
  .main-info {
    margin-top: 18px;
  }
}
.bottom-btn {
  background: #fff;
  padding: 12px 24px;
  position: fixed;
  bottom: -1px;
  right: 0;
  z-index: 10;
  display: flex;
  width: 100vw;
  justify-content: flex-end;
  align-items: center;
  box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.12);
}
.flex-start {
  justify-content: space-between !important;
}
</style>
