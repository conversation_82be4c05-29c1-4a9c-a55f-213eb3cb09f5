<template>
  <!-- 检测项目 详情 -->
  <el-drawer
    v-model="showDrawer"
    :title="titles"
    direction="rtl"
    :before-close="handleClose"
    size="88%"
    destroy-on-close
    :close-on-click-modal="false"
    custom-class="testItemDrawer"
    @opened="handleOpened"
  >
    <DrawerLayout :has-left-panel="false" class="h-full" :has-button-group="getPermissionBtn('itemEdit')">
      <template #drawer-title>
        <span>{{ otherDatas.detailDataInfo.name || '名称' }}</span>
        <el-tag v-if="otherDatas.detailDataInfo.status === 1" effect="dark" size="mini" type="success">启用</el-tag>
        <el-tag v-else size="mini" type="danger">停用</el-tag>
      </template>
      <template #button-group>
        <div class="toggle-btn-group">
          <el-button-group>
            <el-button size="small" :disabled="otherDatas.showPrevBtn" icon="el-icon-arrow-left" @click="handleUp()" />
            <el-button
              size="small"
              :disabled="otherDatas.showNextBtn"
              icon="el-icon-arrow-right"
              @click="handleDown()"
            />
          </el-button-group>
          <el-button
            v-if="getPermissionBtn('itemEdit')"
            size="small"
            @click="editDetial"
            @keyup.prevent
            @keydown.enter.prevent
            >编辑项目</el-button
          >
        </div>
      </template>
      <el-form class="isCheck">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="所属分类：" prop="categoryName">
              <div>{{ otherDatas.detailDataInfo.categoryName || '--' }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="项目编号：" prop="number">
              <div>{{ otherDatas.detailDataInfo.number || '--' }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="英文名：" prop="name1">
              <div>{{ otherDatas.detailDataInfo.name1 || '--' }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="盖章范围：" prop="sealrange">
              <div>
                {{
                  otherDatas.detailDataInfo.sealrange ? JSON.parse(otherDatas.detailDataInfo.sealrange).join(',') : '--'
                }}
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="项目等级：" prop="testcapability">
              <div
                v-if="
                  otherDatas.detailDataInfo.testcapability &&
                  JSON.parse(otherDatas.detailDataInfo.testcapability).length > 0
                "
              >
                <span v-for="item in JSON.parse(otherDatas.detailDataInfo.testcapability)" :key="item">{{
                  otherDatas.dictionaryJSON['XMDJ'].all[item] || '--'
                }}</span>
              </div>
              <span v-else>--</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="物资分类：">
              <div>{{ otherDatas.detailDataInfo.currentMaterial.name || '--' }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="基准价(¥)：">
              <div>{{ otherDatas.detailDataInfo.price || '--' }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="试验方法：">
              <el-tooltip :content="testMethods" placement="bottom" effect="light">
                <div class="overflowHidden">
                  {{ testMethods }}
                </div>
              </el-tooltip>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否是特殊项目：" prop="type">
              <div>{{ otherDatas.detailDataInfo.type ? '是' : '否' }}</div>
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item label="基础工分：" prop="workPoints">
              <el-row>
                <el-col v-for="(val, key) in otherDatas.dictionaryJSON['JCGF'].enable" :key="key" :span="4"
                  >{{ val }}：{{ otherDatas.detailDataInfo[key] || '--' }}</el-col
                >
              </el-row>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="项目描述：">
              <div>{{ otherDatas.detailDataInfo.remark || '--' }}</div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-tabs v-model="activeName" class="marginTop" @tab-click="tabsChange">
        <!-- 关键参数 tab -->
        <el-tab-pane label="关键参数" name="1">
          <div class="btn-group">
            <el-button
              v-if="getPermissionBtn('keyAdd')"
              icon="el-icon-plus"
              size="small"
              @click="addItemCZ"
              @keyup.prevent
              @keydown.enter.prevent
              >新增关键参数</el-button
            >
            <el-button
              v-if="!otherDatas.showBtn && otherDatas.tableData.length > 0 && getPermissionBtn('keyEdit')"
              icon="el-icon-edit"
              size="small"
              @click="editItemCZ"
              @keyup.prevent
              @keydown.enter.prevent
              >编辑关键参数</el-button
            >
            <el-button
              v-show="keyParamEditable"
              type="primary"
              size="small"
              @click="onSubmit"
              @keyup.prevent
              @keydown.enter.prevent
              >保存</el-button
            >
            <el-button v-show="otherDatas.showBtn" size="small" @click="closeGJ">取消</el-button>
          </div>
          <el-form ref="formDataRef" :model="otherDatas" style="margin: 0px">
            <el-table
              id="paramTable"
              ref="paramTableRef"
              :key="otherDatas.paramTableKey"
              :data="otherDatas.tableData"
              fit
              border
              height="auto"
              highlight-current-row
              class="detail-table dark-table base-table"
            >
              <el-table-column label="" :width="keyParamEditable ? 55 : 10">
                <i v-show="keyParamEditable" class="tes-move iconfont" style="font-size: 12px; cursor: move" />
              </el-table-column>
              <el-table-column label="序号" type="index" :width="colWidth.serialNo" />
              <el-table-column prop="templatekey" label="模板Key" width="160">
                <template #default="{ row, $index }">
                  <span v-if="row.disabled"> {{ row.templatekey || '--' }}</span>
                  <el-form-item
                    v-else
                    :prop="`tableData.${$index}.templatekey`"
                    style="margin: 0px"
                    :rules="{ required: true, message: '请输入模板Key', trigger: 'change' }"
                  >
                    <el-input v-model="row.templatekey" placeholder="请输入模板Key" />
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column prop="name" label="数据名称">
                <template #default="{ row, $index }">
                  <span v-if="row.disabled"> {{ row.name }}</span>
                  <el-form-item
                    v-else
                    :prop="`tableData.${$index}.name`"
                    style="margin: 0px"
                    :rules="{ required: true, message: '请输入数据名称', trigger: 'change' }"
                  >
                    <el-input v-model="row.name" maxlength="50" placeholder="数据名称" />
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column prop="name1" label="英文名称">
                <template #default="{ row, $index }">
                  <span v-if="row.disabled"> {{ row.name1 || '--' }}</span>
                  <el-form-item v-else :prop="`tableData.${$index}.name1`" style="margin: 0px">
                    <el-input v-model="row.name1" maxlength="50" placeholder="英文名" />
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column prop="unitname" label="单位" width="120">
                <template #default="{ row, $index }">
                  <span v-if="row.disabled"> {{ row.unitname || '--' }}</span>
                  <el-form-item v-else :prop="`tableData.${$index}.unitname`" style="margin: 0px">
                    <el-select
                      v-model="row.unitname"
                      placeholder="单位"
                      clearable
                      filterable
                      size="small"
                      style="width: 100%"
                      @change="changeUnit(row)"
                    >
                      <el-option
                        v-for="item in otherDatas.units"
                        :key="item.id"
                        :label="item.name"
                        :value="item.name"
                      />
                    </el-select>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column prop="resulttype" label="数据类型" width="140">
                <template #default="{ row, $index }">
                  <span v-if="row.disabled"> {{ row.resulttype }}</span>
                  <el-form-item
                    v-else
                    :prop="`tableData.${$index}.resulttype`"
                    style="margin: 0px"
                    :rules="{ required: true, message: '请选择数据类型', trigger: 'change' }"
                  >
                    <el-select
                      v-model="row.resulttype"
                      placeholder="数据类型"
                      clearable
                      filterable
                      size="small"
                      style="width: 100%"
                      @change="changeSelect(row)"
                    >
                      <el-option
                        v-for="item in otherDatas.resulttypeOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column prop="applylabel" label="应用场景" :min-width="180">
                <template #default="{ row, $index }">
                  <span v-if="row.disabled">{{ filterGJCS(row.applylabel) || '--' }}</span>
                  <el-form-item v-else :prop="`tableData.${$index}.applylabels`" style="margin: 0px">
                    <el-select
                      v-model="row.applylabels"
                      multiple
                      placeholder="请选择应用场景"
                      clearable
                      size="small"
                      class="multiple-select"
                      @change="changeApplylabel(row)"
                    >
                      <el-option
                        v-for="item in otherDatas.applylabels"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                      />
                    </el-select>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column prop="capabilitySubCategoryName" label="子项目" :min-width="120">
                <template #default="{ row, $index }">
                  <span v-if="row.disabled">{{ row.capabilitySubCategoryName || '--' }}</span>
                  <el-form-item v-else :prop="`tableData.${$index}.capabilitySubCategoryId`" style="margin: 0px">
                    <el-select
                      v-model="row.capabilitySubCategoryId"
                      placeholder="请选择子项目"
                      clearable
                      size="small"
                      @change="changeSubItem(row)"
                    >
                      <el-option
                        v-for="item in otherDatas.subProjectList"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                      />
                    </el-select>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column
                prop="resultoption"
                label="参数设置"
                :width="keyParamEditable ? 280 : 200"
                show-overflow-tooltip
              >
                <template #default="{ row, $index }">
                  <div v-if="row.resulttype === '数值型'" class="option-number">
                    <el-row style="width: 100%">
                      <el-col :span="14">
                        <div v-if="row.disabled">
                          {{ row.resultOptionType === 1 ? '有效数字约束：' : '约束小数位：' }}
                        </div>
                        <el-form-item v-else :prop="`tableData.${$index}.resultOptionType`" style="margin: 0px">
                          <el-select v-model="row.resultOptionType" placeholder="请选择类型">
                            <el-option label="约束小数位" :value="0" />
                            <el-option label="有效数字约束" :value="1" />
                          </el-select>
                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <div v-if="row.disabled" class="text-center">
                          <span v-if="row.resultoption || row.resultoption === 0">{{ row.resultoption }}</span>
                          <span v-else>--</span>
                        </div>
                        <el-form-item v-else :prop="`tableData.${$index}.resultoption`" style="margin: 0px">
                          <el-input v-model="row.resultoption" size="small" placeholder="位数" />
                        </el-form-item>
                      </el-col>
                      <el-col
                        :span="2"
                        class="text-right"
                        style="display: flex; flex-direction: column; justify-content: center"
                      >
                        <div>位</div>
                      </el-col>
                    </el-row>
                  </div>
                  <div v-if="row.resulttype === '枚举型'" class="result-option">
                    <span v-if="row.disabled">{{ filterEnumOptions(row.resultoption) }}</span>
                    <el-form-item v-else :prop="`tableData.${$index}.resultoption`" style="margin: 0px">
                      <el-select
                        v-model="row.resultoption"
                        placeholder="请选择"
                        clearable
                        size="small"
                        class="multiple-select"
                        filterable
                        :filter-method="changeEnumOptions"
                        @focus="changeEnumOptions(null)"
                      >
                        <el-option
                          v-for="item in otherDatas.enumOptions"
                          :key="item.code"
                          :label="item.name"
                          :value="item.code"
                        />
                      </el-select>
                    </el-form-item>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态" width="100">
                <template #default="{ row }">
                  <el-tag v-if="row.disabled" :type="row.statusflag ? 'success' : 'info'" size="small">
                    {{ row.statusflag === true ? '已启用' : '已停用' }}
                  </el-tag>
                  <el-switch
                    v-else
                    v-model="row.statusflag"
                    class="inner-switch"
                    :active-text="row.statusflag === true ? '启用' : '停用'"
                    @change="handleQY(row.statusflag, row)"
                  />
                </template>
              </el-table-column>
              <el-table-column
                v-if="getPermissionBtn('keyDelete') && !otherDatas.tableData[otherDatas.tableData.length - 1]?.id"
                prop="status"
                label="操作"
                width="100"
              >
                <template #default="{ row, $index }">
                  <span
                    v-if="!row.id"
                    class="blue-color"
                    @click="handleDel(row, $index)"
                    @keyup.prevent
                    @keydown.enter.prevent
                    >删除</span
                  >
                </template>
              </el-table-column>
            </el-table>
          </el-form>
        </el-tab-pane>
        <!-- 关联仪器设备 tab -->
        <el-tab-pane label="关联仪器设备" name="2">
          <AssociatedEquipment
            :capability-id="otherDatas.detailDataInfo.id"
            :active-name="activeName"
            :key-parameter="otherDatas.tableData"
          />
        </el-tab-pane>
        <!-- 模板引用 tab -->
        <el-tab-pane label="模板引用" name="3">
          <el-button
            v-if="getPermissionBtn('mbRefAdd')"
            class="add-btn"
            icon="el-icon-plus"
            size="small"
            @click="addTemplateref"
            @keyup.prevent
            @keydown.enter.prevent
            >新增引用项目</el-button
          >
          <el-table
            :data="otherDatas.templaterefList"
            fit
            border
            height="auto"
            highlight-current-row
            size="medium"
            class="base-table dark-table detail-table"
            :span-method="objectSpanMethod"
          >
            <el-table-column type="index" label="序号" width="70" align="center" />
            <el-table-column prop="refCapabilityName" label="被引用项目名称" show-overflow-tooltip>
              <template #default="{ row }">
                {{ row.refCapabilityName }}
              </template>
            </el-table-column>
            <el-table-column prop="refCapabilityParaName" label="被引用项目关键参数名称" show-overflow-tooltip>
              <template #default="{ row }">
                <span v-if="row.disable">{{ row.refCapabilityParaName || '--' }}</span>
                <el-select
                  v-else
                  v-model="otherDatas.paramsATR.refCapabilityParaId"
                  placeholder="请输入被引用项目关键参数名称"
                >
                  <el-option
                    v-for="item in row.capabilityparaVoList"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  />
                </el-select>
              </template>
            </el-table-column>
            <el-table-column prop="capabilityParaName" label="本项目关键参数名称">
              <template #default="{ row }">
                <span v-if="row.disable">{{ row.capabilityParaName || '--' }}</span>
                <el-select
                  v-else
                  v-model="otherDatas.paramsATR.capabilityParaId"
                  placeholder="请输入本项目关键参数名称"
                >
                  <el-option v-for="item in otherDatas.tableData" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="操作" width="140" fixed="right" class-name="fixed-right">
              <template #default="{ row, $index }">
                <span
                  v-if="row.showEditTR && getPermissionBtn('mbRefEdit')"
                  class="blue-color"
                  @click="editTemplateref(row)"
                  @keyup.prevent
                  @keydown.enter.prevent
                  >编辑</span
                >
                <span
                  v-if="row.showSaveTR && getPermissionBtn('mbRefSave')"
                  class="blue-color"
                  @click="saveTemplateref(row)"
                  @keyup.prevent
                  @keydown.enter.prevent
                  >保存</span
                >
                <span
                  v-if="row.showDeleteTR && getPermissionBtn('mbRefDelete')"
                  class="blue-color"
                  @click="deleteTemplateref(row)"
                  @keyup.prevent
                  @keydown.enter.prevent
                  >删除</span
                >
                <span
                  v-if="row.showCancleTR && getPermissionBtn('mbRefDelete')"
                  class="blue-color"
                  @click="cancleTemplateref(row, $index)"
                  @keyup.prevent
                  @keydown.enter.prevent
                  >取消</span
                >
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <!-- 试验方法 tab -->
        <el-tab-pane label="试验方法" name="4">
          <operation-steps :item="otherDatas.detailDataInfo" :active-name="activeName" />
        </el-tab-pane>
        <!-- 国网项目关联 tab -->
        <el-tab-pane v-if="getPermissionBtn('sgLimsRefDetail')" label="国网项目关联" name="5">
          <el-button
            v-if="getPermissionBtn('sgLimsRefAdd')"
            class="add-btn"
            icon="el-icon-plus"
            size="small"
            @click="addSgTemplateref"
            @keyup.prevent
            @keydown.enter.prevent
            >新增引用项目</el-button
          >
          <el-table
            :data="otherDatas.sgTemplaterefList"
            fit
            border
            height="auto"
            highlight-current-row
            size="medium"
            class="base-table dark-table detail-table"
            :span-method="sgObjectSpanMethod"
          >
            <el-table-column type="index" label="序号" width="70" align="center" />
            <el-table-column prop="refMaterialCategoryName" label="国网项目物资分类" show-overflow-tooltip>
              <template #default="{ row }">
                {{ row.refMaterialCategoryName }}
              </template>
            </el-table-column>
            <el-table-column prop="refCapabilityName" label="国网项目名称" show-overflow-tooltip>
              <template #default="{ row }">
                {{ row.refCapabilityName }}
              </template>
            </el-table-column>
            <el-table-column prop="refCapabilityParaName" label="国网项目关键参数名称" show-overflow-tooltip>
              <template #default="{ row }">
                <span v-if="row.disable">{{ row.refCapabilityParaName || '--' }}</span>
                <el-select
                  v-else
                  v-model="otherDatas.paramsATR.refCapabilityParaId"
                  placeholder="国网项目关键参数名称"
                  clearable
                  @change="changeRefCapabilityPara(row)"
                  @clear="clearRefCapabilityPara(row)"
                >
                  <el-option
                    v-for="item in row.capabilityparaVoList"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  />
                </el-select>
              </template>
            </el-table-column>
            <el-table-column prop="capabilityParaName" label="LIMS项目关键参数名称">
              <template #default="{ row }">
                <span v-if="row.disable">{{ row.capabilityParaName || '--' }}</span>
                <el-select
                  v-else
                  v-model="otherDatas.paramsATR.capabilityParaId"
                  placeholder="请输入本项目关键参数名称"
                  clearable
                  @change="changeSgCapabilityPara(row)"
                  @clear="clearSgCapabilityPara(row)"
                >
                  <el-option v-for="item in otherDatas.tableData" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
              </template>
            </el-table-column>
            <el-table-column prop="operation" label="操作" width="140" fixed="right" class-name="fixed-right">
              <template #default="{ row, $index }">
                <span
                  v-if="row.showEditTR && getPermissionBtn('sgLimsRefEdit')"
                  class="blue-color"
                  @click="editSgTemplateref(row)"
                  @keyup.prevent
                  @keydown.enter.prevent
                  >编辑</span
                >
                <span
                  v-if="row.showSaveTR && getPermissionBtn('sgLimsRefSave')"
                  class="blue-color"
                  @click="saveSgTemplateref(row)"
                  @keyup.prevent
                  @keydown.enter.prevent
                  >保存</span
                >
                <span
                  v-if="row.showDeleteTR && getPermissionBtn('sgLimsRefDelete')"
                  class="blue-color"
                  @click="deleteSgTemplateref(row)"
                  @keyup.prevent
                  @keydown.enter.prevent
                  >删除</span
                >
                <span
                  v-if="row.showCancleTR && getPermissionBtn('sgLimsRefDelete')"
                  class="blue-color"
                  @click="cancelSgTemplateref(row, $index)"
                  @keyup.prevent
                  @keydown.enter.prevent
                  >取消</span
                >
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <!-- 试验人员 -->
        <el-tab-pane label="试验人员" name="6">
          <project-personnel :detail-data="otherDatas.detailDataInfo" :active-name="activeName" />
        </el-tab-pane>
        <el-tab-pane label="图片标签" name="7">
          <TabImageTag :current-data="otherDatas.detailDataInfo" :parent-type="0" :active-name="activeName" />
        </el-tab-pane>
        <el-tab-pane v-if="getPermissionBtn('TestProjectSubproject')" label="子项目" name="8">
          <AssociatedSubitem :capability-id="otherDatas.detailDataInfo.id" :active-name="activeName" />
        </el-tab-pane>
        <el-tab-pane label="检测依据" name="9">
          <DetectionBasis
            :capability-id="otherDatas.detailDataInfo.id"
            :key-parameter="otherDatas.tableData"
            :item-name="otherDatas.detailDataInfo.name"
            :active-name="activeName"
          />
        </el-tab-pane>
        <el-tab-pane label="检测报告模板" name="10">
          <TestReportTemplate :capability-id="otherDatas.detailDataInfo.id" :active-name="activeName" />
        </el-tab-pane>
      </el-tabs>
      <edit
        :drawer="otherDatas.drawer"
        title="编辑项目"
        :dictionary="otherDatas.dictionaryJSON"
        :tree="otherDatas.newTree"
        :edit-data="otherDatas.editData"
        @close="closeDrawer"
      />
      <model-add :show="showAdd" :data="modeldata" :table="otherDatas.curList" title="新增项目" @close="closeModel" />
      <!-- 新增关联仪器设备-弹出框 -->
      <!-- <add-instrumentation
        :show="otherDatas.showYQSB"
        :data="otherDatas.oldItemsYQSB"
        @selectData="selectYQSBData"
        @close="closeYQSB"
      /> -->
      <!-- 新增模板引用-弹出框 -->
      <add-template-ref
        :show="otherDatas.showATR"
        :tree="otherDatas.treeItemsATR"
        :data="otherDatas.oldItemsATR"
        @selectData="selectATRData"
        @close="closeATR"
      />
      <AddSgSingleItem
        :show="otherDatas.showSgATR"
        :data="otherDatas.oldSgItemsATR"
        @selectData="selectSgATRData"
        @close="closeSgATR"
      />
    </DrawerLayout>
  </el-drawer>
</template>
<script>
import { ref, watch, reactive, getCurrentInstance, toRefs, computed, nextTick } from 'vue';
import {
  saveCapabilitypara,
  deleteExtcapabilitymap,
  deleteCapabilitypara,
  extcapabilitymapinfo,
  getCapabilityInfo,
  getCapabilityTemplateRefList,
  updateTemplateRef,
  deleteTemplateRef,
  getCapabilityById
} from '@/api/user';
import { colWidth } from '@/data/tableStyle';
import { formatTestcapabilityByValue } from '@/utils/formatJson';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getPermissionBtn } from '@/utils/common';
import Edit from './add';
import DrawerLayout from '@/components/DrawerLayout';
import { getGuoWangByMaterialCategory } from '@/api/login';
import ModelAdd from '@/views/test-configuration/test-items/ModuleAdd';
import AddTemplateRef from './add-template-ref.vue';
import { useStore } from 'vuex';
import _ from 'lodash';
import { getDictionaryList } from '@/api/dictionary';
import OperationSteps from '@/views/test-configuration/test-items/operation-steps.vue';
import ProjectPersonnel from '@/views/test-configuration/test-items/project-personnel.vue';
import Sortable from 'sortablejs';
import {
  getMapKeyParamsByCapabilityId,
  saveExternalKeyParamMap,
  deleteExternalKeyParamMap
} from '@/api/sg-capabilitystd';
import { capabilitySubCategory } from '@/api/capability';
import AddSgSingleItem from '@/components/BusinessComponents/AddSgSingleItem';
import { getSgMaterialClassifcation } from '@/views/test-configuration/sg-test-items/func/sgData';
import TabImageTag from '@/components/BusinessComponents/TabImageTag.vue';
import AssociatedSubitem from './components/associated-subitem.vue';
import AssociatedEquipment from './components/associated-equipment.vue';
import DetectionBasis from './components/DetectionBasis.vue';
import TestReportTemplate from './components/TestReportTemplate.vue';

export default {
  name: 'DETAIL',
  components: {
    ModelAdd,
    DetectionBasis,
    TestReportTemplate,
    Edit,
    AssociatedEquipment,
    AddTemplateRef,
    OperationSteps,
    ProjectPersonnel,
    DrawerLayout,
    AddSgSingleItem,
    TabImageTag,
    AssociatedSubitem
  },
  props: {
    drawer: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      required: true
    },
    dictionary: {
      type: Object,
      default: function () {
        return {};
      }
    },
    testItemList: {
      type: Array,
      default: () => {
        return [];
      }
    },
    detailData: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['close'],
  setup(props, context) {
    // 抽屉事件
    const { proxy } = getCurrentInstance();
    const stores = useStore().state.user;
    const showAdd = ref(false);
    const modeldata = reactive({
      isshow: false,
      capabilityid: ''
    });
    const showDrawer = ref(props.drawer);
    const titles = ref(props.title);
    const paramTableRef = ref(null);
    // formInline
    const detailDatas = reactive({
      status: 0,
      number: '',
      name: '',
      name1: '',
      workhours: '',
      price: null,
      testcapability: '',
      sealrange: '',
      method: '',
      description: '',
      remark: ''
    });
    const state = reactive({
      formDataRef: ref()
    });
    const otherDatas = reactive({
      newFlag: true,
      tableData: [],
      dictionaryJSON: {
        // 字典集合
        JCGF: {
          enable: {},
          all: {}
        },
        XMDJ: {
          enable: {},
          all: {}
        },
        GZFW: {
          enable: {},
          all: {}
        }
      },
      detailDataInfo: {}, // 详情信息
      testItemList: [], // 外部列表用于上下切换
      tableDataGW: [],
      tableDataGLYQ: [],
      tableDataGL: [],
      thislist: [],
      curList: [],
      subProjectList: [], // 子项目列表
      // itemLevel: {},
      // sealScopeOf: {},
      testcapabilityOptions: {
        '339081954591256576': 'A',
        '339081954591256577': 'B',
        '339081954591256578': 'C'
      },
      resulttypeOptions: [
        { value: '数值型', label: '数值型' },
        { value: '字符串', label: '字符串' },
        { value: '枚举型', label: '枚举型' },
        { value: '日期型', label: '日期型' },
        { value: '自定义枚举', label: '自定义枚举' }
      ],
      enumOptions: [],
      copyEnumOptions: [],
      applylabels: [
        { id: '1', name: '数据采集' },
        { id: '2', name: '检测报告' },
        { id: '3', name: 'SPC' }
      ],
      dynamicTags: [],
      inputVisible: false,
      inputValue: '',
      drawer: false,
      newTree: [],
      editData: {},
      showBtn: false,
      showBtn1: false,
      showPrevBtn: false,
      showNextBtn: false,
      units: stores.unit,
      keywordRules: {
        name: [{ required: true, message: '', trigger: 'blur' }],
        resulttype: [{ required: true, message: '', trigger: 'blur' }],
        value: [{ required: true, message: '', trigger: 'blur' }]
      },
      // keywordFlag: true,
      dataChangeFlag: false,
      templaterefList: [],
      sgTemplaterefList: [],
      currentCapabilityId: '',
      treeItemsATR: [],
      oldItemsATR: [],
      oldSgItemsATR: [],
      showATR: false,
      showSgATR: false,
      showAddTR: true,
      showSgAddTR: true,
      paramsATR: {
        id: '',
        capabilityId: '',
        capabilityName: '',
        capabilityParaId: '',
        capabilityParaName: '',
        refCapabilityId: '',
        refCapabilityName: '',
        refCapabilityParaId: '',
        refCapabilityParaName: '',
        refKeyId: '',
        keyId: '',
        keyName: ''
      },
      deviceList: [],
      paramsDevice: {
        capabilityId: '',
        capabilityParaId: '',
        codePointId: '',
        deviceId: '',
        id: '',
        paraConvert: 1,
        pointConvert: 1
      },
      showAddDevice: true,
      paramTableKey: 0,
      sgMaterialList: []
    });
    // 切换上一检测项目
    const handleUp = () => {
      const index = props.testItemList.findIndex(item => {
        return item.id === otherDatas.currentCapabilityId;
      });
      if (props.testItemList[index - 1]?.id) {
        getDetailInfo(props.testItemList[index - 1].id);
        otherDatas.showNextBtn = false;
      } else {
        ElMessage.warning('已经是第一个项目详情。');
        otherDatas.showPrevBtn = true;
      }
    };
    // 切换下一检测项目
    const handleDown = () => {
      const index = props.testItemList.findIndex(item => {
        return item.id === otherDatas.currentCapabilityId;
      });
      if (props.testItemList[index + 1]?.id) {
        getDetailInfo(props.testItemList[index + 1].id);
        otherDatas.showPrevBtn = false;
      } else {
        ElMessage.warning('已经是最后一个项目详情。');
        otherDatas.showNextBtn = true;
      }
    };
    const getDetailInfo = detailDataId => {
      getCapabilityById(detailDataId).then(response => {
        if (response) {
          const { data } = response.data;
          getCapabilityInfo(detailDataId).then(function (res) {
            if (res) {
              data.tableData = res.data.data;
              initDetail(data);
            }
          });
        }
      });
    };
    // 关闭抽屉
    const handleClose = () => {
      if (otherDatas.dataChangeFlag === true) {
        ElMessageBox({
          title: '提示',
          message: '当前页面数据未保存，是否确认离开？',
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          showCancelButton: true,
          closeOnClickModal: false,
          type: 'info'
        })
          .then(() => {
            showDrawer.value = false;
            context.emit('close', false);
            return true;
          })
          .catch(() => {
            return false;
          });
      } else {
        showDrawer.value = false;
        context.emit('close', false);
      }
    };
    // 过滤关键参数数据
    const filterTabs = tableDatas => {
      if (tableDatas.length > 0) {
        tableDatas.forEach(item => {
          if (item.status === 1) {
            item.statusflag = true;
          } else {
            item.statusflag = false;
          }
          item.disabled = true;
          if (item.resulttype === '数值型') {
            if (Number(item.resultoption) || item.resultoption === '0' || item.resultoption === 0) {
              item.resultoption = Number(item.resultoption);
            } else {
              item.resultoption = undefined;
            }
          }
          if (item.applylabel) {
            item.applylabels = item.applylabel.split(',');
          }
        });
      }
      return tableDatas;
    };

    watch(
      () => props.drawer,
      async newValue => {
        if (newValue) {
          initDetail(props.detailData);
          otherDatas.enumOptions = await proxy.getMeiJu();
          otherDatas.copyEnumOptions = JSON.parse(JSON.stringify(otherDatas.enumOptions));
          otherDatas.testItemList = props.testItemList;
          otherDatas.dictionaryJSON = props.dictionary || {
            // 字典集合
            JCGF: {
              enable: {},
              all: {}
            },
            XMDJ: {
              enable: {},
              all: {}
            },
            GZFW: {
              enable: {},
              all: {}
            }
          };
          showDrawer.value = props.drawer;
          if (showDrawer.value) {
            otherDatas.dataChangeFlag = false;
          }
          activeName.value = '1';
          otherDatas.showBtn = false;
        }
      },
      { deep: true }
    );
    const initDetail = detailItem => {
      otherDatas.detailDataInfo = { ...props.detailData, ...detailItem };
      if (getPermissionBtn('sgLimsRefDetail')) {
        getSgTemplateRefList(otherDatas.detailDataInfo.id);
      }
      const treeList = JSON.parse(JSON.stringify(otherDatas.detailDataInfo.treelist));
      _.remove(treeList, function (n) {
        return n.name === '全部';
      });
      otherDatas.newTree = otherDatas.detailDataInfo.apabilityTree;
      otherDatas.treeItemsATR = [
        {
          id: '-1',
          isDeleted: false,
          parentId: '0',
          name: '全部',
          code: '全部',
          materialCategoryCode: treeList[0].materialCategoryCode,
          order: 0
        }
      ].concat(treeList);
      otherDatas.currentCapabilityId = otherDatas.detailDataInfo.id;
      otherDatas.tableData = filterTabs(otherDatas.detailDataInfo.tableData);
      capabilityId.value = otherDatas.detailDataInfo.id;
      getTemplateRefList(otherDatas.detailDataInfo.id);
      getSubProjectList(); // 获取子项目列表
    };
    const getSubProjectList = () => {
      capabilitySubCategory(otherDatas.currentCapabilityId).then(res => {
        if (res) {
          otherDatas.subProjectList = res.data.data;
        }
      });
    };

    // tabs标签页
    const activeName = ref('1');
    const tabsChange = () => {
      if (activeName.value === '1') {
        // 刷新关键参数里的子项目
        getSubProjectList();
      }
    };
    // 关键参数 保存编辑
    const onSubmit = () => {
      var newTabDatas = JSON.parse(JSON.stringify(otherDatas.tableData));
      if (newTabDatas.length === 0) {
        return false;
      }
      state.formDataRef
        .validate()
        .then(valid => {
          if (valid) {
            if (newTabDatas.length > 0) {
              newTabDatas.forEach(item => {
                if (item.resulttype === '枚举型' && item.resultoption.length > 0) {
                  // item.resultoption = item.resultoption.toString()
                } else if (item.resulttype === '枚举型' && item.resultoption.length === 0) {
                  // item.resultoption = ''
                } else if (item.resulttype === '数值型') {
                  item.resultoption = item.resultoption === undefined ? '' : item.resultoption + '';
                }
              });
            }
            const params = { capabilityparaVoList: newTabDatas };
            saveCapabilitypara(params).then(function (res) {
              if (res !== false && res.data.code === 200) {
                const { data } = res.data;
                otherDatas.newFlag = true;
                otherDatas.dataChangeFlag = false;
                otherDatas.tableData = filterTabs(data.capabilityparaVoList);
                otherDatas.showBtn = false;
                ElMessage.success('保存成功！');
              }
            });
          }
        })
        .catch(error => {
          const fieldName = Object.keys(error)[0];
          proxy.$message.warning(error[fieldName][0].message);
          return false;
        });
    };
    // 过滤应用场景
    const filterGJCS = v => {
      var list = [];
      var name = [];
      if (v) {
        list = v.split(',');
      }
      const param = {
        1: '数据采集',
        2: '检测报告',
        3: 'SPC'
      };
      if (list.length > 0) {
        list.forEach(l => {
          name.push(param[l]);
        });
      }
      return name.join(',');
    };
    // 编辑项目
    const editDetial = () => {
      otherDatas.editData = otherDatas.detailDataInfo;
      otherDatas.drawer = true;
    };
    // 关闭编辑项目
    const closeDrawer = isEdit => {
      otherDatas.drawer = false;
      if (isEdit) {
        getDetailInfo(otherDatas.detailDataInfo.id);
      }
    };
    // 关闭新增项目
    const closeModel = () => {
      showAdd.value = false;
      const materialCategoryId = otherDatas.detailDataInfo.currentMaterial.id;
      getExtcapabilitymapinfo(materialCategoryId);
      otherDatas.dataChangeFlag = true;
    };
    // 国网项目关联的第三方数据
    const capabilityId = ref('');
    // 调取tes的国网数据
    const gettableDataGW = () => {
      return new Promise((resolve, reject) => {
        extcapabilitymapinfo(capabilityId.value).then(resdata => {
          if (resdata.data.code === 200) {
            otherDatas.tableDataGW = resdata.data.data;
            resolve(resdata.data.data);
          } else {
            reject();
          }
        });
      });
    };
    // 调取外部的国网数据
    const gettableDataWB = materialCategoryIds => {
      return new Promise((resolve, reject) => {
        getGuoWangByMaterialCategory({
          materialCategoryIds: materialCategoryIds
        }).then(res => {
          if (res !== false) {
            otherDatas.tableDataWB = res.data;
            resolve(res.data);
          } else {
            reject();
          }
        });
      });
    };
    // 国网数据的处理
    const getExtcapabilitymapinfo = materialCategoryIds => {
      Promise.all([gettableDataGW(), gettableDataWB(materialCategoryIds)])
        .then(res => {
          const GWList = res[0];
          const WBList = res[1];
          otherDatas.thislist = [];
          otherDatas.curList = [];
          WBList.forEach(obj => {
            const newObj = {};
            newObj.extcapabilitycode = obj.number;
            newObj.name = obj.name;
            newObj.number = obj.number;
            newObj.testCapability = obj.testCapability;
            newObj.extcapabilitycode = obj.number;
            newObj.description = obj.description;
            newObj.status = obj.status;
            newObj.checked = false;
            newObj.id = '';
            newObj.extcapabilityid = obj.id;
            GWList.forEach(item => {
              if (item.extcapabilityid === obj.id) {
                newObj.checked = true;
                newObj.id = item.id;
                otherDatas.thislist.push(newObj);
              }
            });
            otherDatas.curList.push(newObj);
          });
        })
        .catch(err => {
          // 抛出错误信息
          console.log(err);
        });
    };
    // 新增-国网项目
    const addItemGWCZ = () => {
      showAdd.value = true;
      modeldata.capabilityid = capabilityId.value;
    };
    // 删除-国网项目
    const handleDelGW = row => {
      ElMessageBox({
        title: '提示',
        message: '此操作将永久删除该条数据, 是否继续?',
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        showCancelButton: true,
        closeOnClickModal: true,
        type: 'warning'
      })
        .then(() => {
          const ids = [];
          ids.push(row.id);
          deleteExtcapabilitymap(ids).then(res => {
            const materialCategoryId = otherDatas.detailDataInfo.currentMaterial.id;
            getExtcapabilitymapinfo(materialCategoryId);
            if (res.data.code === 200) {
              ElMessage({
                type: 'success',
                message: '删除成功!'
              });
            }
          });
        })
        .catch(() => {
          ElMessage({
            type: 'info',
            message: '已取消删除'
          });
        });
    };
    // 新增-关键参数
    const addItemCZ = () => {
      otherDatas.newFlag = false;
      otherDatas.dataChangeFlag = true;
      const data = {
        capabilityid: otherDatas.detailDataInfo.id,
        number: '',
        name: '',
        name1: '',
        unitname: '',
        resulttype: '',
        resultoption: '',
        status: 1,
        disabled: false
      };
      data.statusflag = true;
      data.number = otherDatas.tableData.length + 1;
      otherDatas.tableData.push(data);
      otherDatas.showBtn = true;
    };
    // 编辑关键参数
    const editItemCZ = () => {
      otherDatas.newFlag = false;
      otherDatas.tableData.forEach(res => {
        res.disabled = false;
      });
      otherDatas.showBtn = true;
    };
    // 过滤枚举型数据
    const filterEnumOptions = value => {
      var name = '';
      if (value) {
        const items = _.filter(otherDatas.enumOptions, function (eo) {
          return value === eo.code;
        });
        if (items.length > 0) {
          name = items[0].name;
        }
      }
      return name;
    };
    // 删除-关键参数
    const handleDel = (row, index) => {
      ElMessageBox({
        title: '提示',
        message: '确认删除关键参数吗？删除后不可恢复!',
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        showCancelButton: true,
        closeOnClickModal: true,
        type: 'warning'
      })
        .then(() => {
          const newArr = [];
          if (row.id) {
            newArr.push(row.id);
            deleteCapabilitypara(newArr).then(function (res) {
              if (res !== false && res.data.code === 200) {
                otherDatas.newFlag = true;
                otherDatas.tableData.splice(index, 1);
                ElMessage.success('删除成功！');
              }
            });
          } else {
            // otherDatas.newFlag = true
            otherDatas.tableData.splice(index, 1);
          }
          return true;
        })
        .catch(() => {
          return false;
        });
      otherDatas.newFlag = false;
    };
    // 启用 1  停用 -5
    const handleQY = (flag, row) => {
      var num = 0;
      otherDatas.tableData.forEach(data => {
        if (data.status === 1) {
          num += 1;
        }
      });
      if (num < 2 && flag === false) {
        ElMessage.warning('至少需要一个启动项');
        row.statusflag = true;
        return false;
      }
      if (flag === false) {
        row.status = -5;
      } else {
        row.status = 1;
      }
      otherDatas.dataChangeFlag = true;
    };
    // 选择单位
    const changeUnit = row => {
      otherDatas.dataChangeFlag = true;
    };
    // 关键参数--数据类型
    const changeSelect = (row, index) => {
      if (row.resulttype === '枚举型' || row.resultType === '自定义枚举') {
        row.resultoption = '';
        row.inputVisible = false;
        row.inputValue = '';
      } else if (row.resulttype === '字符串' || row.resultType === '日期型') {
        row.resultoption = '';
      } else {
        row.resultoption = '';
        row.resultOptionType = 0;
      }
      otherDatas.dataChangeFlag = true;
    };
    // 关键参数-应用场景修改
    const changeApplylabel = row => {
      row.applylabel = row.applylabels.join(',');
    };
    // 关键参数-子项目的修改
    const changeSubItem = row => {
      row.capabilitySubCategoryName = otherDatas.subProjectList.filter(item => {
        return item.id === row.capabilitySubCategoryId;
      })[0]?.name;
    };
    // 关键参数-枚举型修改
    const changeEnumOptions = val => {
      if (val) {
        const list = [];
        otherDatas.copyEnumOptions.forEach(user => {
          if (user.name.indexOf(val) !== -1) {
            list.push(user);
          }
        });
        otherDatas.enumOptions = list;
      } else {
        otherDatas.enumOptions = otherDatas.copyEnumOptions;
      }
    };
    // 关键参数--参数设置
    const handleCloseTag = (row, tag) => {
      row.resultoption.splice(row.resultoption.indexOf(tag), 1);
      otherDatas.dataChangeFlag = true;
    };
    const showInput = row => {
      row.inputVisible = true;
      if (row.resultoption === '' || row.resultoption === undefined || row.resultoption === null) {
        row.resultoption = [];
      }
    };
    const handleInputConfirm = row => {
      const inputValue = row.inputValue;
      if (inputValue) {
        row.resultoption.push(inputValue);
      }
      row.inputVisible = false;
      row.inputValue = '';
    };
    // 关键参数 取消保存
    const closeGJ = () => {
      otherDatas.showBtn = false;
      getCapabilityInfo(otherDatas.detailDataInfo.id).then(function (res) {
        if (res !== false && res.data.code === 200) {
          const { data } = res.data;
          otherDatas.tableData = filterTabs(data);
          showDrawer.value = true;
          // context.emit('close', false)
        }
      });
    };
    const formatValue = val => {
      return formatTestcapabilityByValue(val);
    };

    // #endregion

    // #region 模板引用模块

    // 新增-模板引用
    const addTemplateref = () => {
      if (otherDatas.showAddTR === false) {
        ElMessage.warning('只能同时编辑一行');
        return false;
      } else if (otherDatas.tableData.length === 0) {
        ElMessage.warning('本项目关键参数为空,请新增关键参数');
        return false;
      }
      otherDatas.oldItemsATR.push({
        refCapabilityId: otherDatas.currentCapabilityId
      });
      otherDatas.showATR = true;
    };
    // 编辑-模板引用
    const editTemplateref = async row => {
      row.capabilityparaVoList = await getCapabilityList(row.refCapabilityId);
      row.disable = false;
      otherDatas.paramsATR = Object.assign(otherDatas.paramsATR, row);
      row.showEditTR = false;
      row.showSaveTR = true;
      row.showDeleteTR = false;
      row.showCancleTR = true;
      otherDatas.templaterefList.forEach(trl => {
        if (trl.id !== row.id) {
          trl.showEditTR = false;
          trl.showSaveTR = false;
          trl.showDeleteTR = false;
          trl.showCancleTR = false;
        }
      });
      otherDatas.showAddTR = false;
    };
    // 保存-模板引用
    const saveTemplateref = async row => {
      if (!row.capabilityParaId) {
        ElMessage.error('请选择本项目关键参数');
        return false;
      } else if (!row.refCapabilityParaId) {
        ElMessage.error('请选择被引用项目关键参数');
        return false;
      }
      const flag = await updateTemplateRefList();
      if (flag === true) {
        row.disable = true;
        otherDatas.showAddTR = true;
      }
    };
    // 删除-模板引用
    const deleteTemplateref = row => {
      ElMessageBox({
        title: '',
        message: '是否删除该项目？删除后不可恢复',
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        showCancelButton: true,
        closeOnClickModal: true,
        type: 'warning'
      })
        .then(() => {
          deleteTemplateRefList(row.id);
        })
        .catch(() => {});
    };
    // 取消-模板引用
    const cancleTemplateref = (row, index) => {
      getTemplateRefList(otherDatas.currentCapabilityId);
    };
    // 获取模板引用列表
    const getTemplateRefList = capabilityId => {
      return new Promise((resolve, reject) => {
        getCapabilityTemplateRefList(capabilityId)
          .then(res => {
            if (res !== false) {
              const { data } = res;
              otherDatas.templaterefList = data.data;
              proxy.getSpanArr(otherDatas.templaterefList);
              if (otherDatas.templaterefList.length > 0) {
                otherDatas.templaterefList.forEach(list => {
                  list.disable = true;
                  list.showEditTR = true;
                  list.showSaveTR = false;
                  list.showDeleteTR = true;
                  list.showCancleTR = false;
                });
              }
              otherDatas.showAddTR = true;
              resolve(data.data);
            }
          })
          .catch(error => {
            reject(error);
          });
      });
    };
    // 新增、编辑-模板引用列表
    const updateTemplateRefList = () => {
      return new Promise((resolve, reject) => {
        updateTemplateRef(otherDatas.paramsATR)
          .then(res => {
            if (res !== false) {
              ElMessage.success('更新成功');
              getTemplateRefList(otherDatas.currentCapabilityId);
              resolve(true);
            } else {
              resolve(false);
            }
          })
          .catch(error => {
            reject(error);
          });
      });
    };
    // 删除-模板引用
    const deleteTemplateRefList = id => {
      return new Promise((resolve, reject) => {
        deleteTemplateRef(id)
          .then(res => {
            if (res !== false) {
              ElMessage.success('删除成功');
              getTemplateRefList(otherDatas.currentCapabilityId);
              resolve(res.data.data);
            }
          })
          .catch(error => {
            reject(error);
          });
      });
    };
    // 选择新增模板引用-弹出框
    const selectATRData = value => {
      otherDatas.paramsATR = {
        id: '',
        capabilityId: otherDatas.currentCapabilityId,
        capabilityparaVoList: value.capabilityparaVoList,
        capabilityParaId: '',
        capabilityParaName: '',
        capabilityParaTemplateKey: '',
        refCapabilityId: value.id,
        refCapabilityName: value.name,
        refCapabilityParaId: '',
        refCapabilityParaName: '',
        refCapabilityParaTemplateKey: '',
        disable: false,
        showEditTR: false,
        showSaveTR: true,
        showDeleteTR: false,
        showCancleTR: true
      };
      otherDatas.showAddTR = false;
      const newData = JSON.parse(JSON.stringify(otherDatas.templaterefList));
      newData.push(otherDatas.paramsATR);
      proxy.getSpanArr(newData);
      otherDatas.templaterefList.push(otherDatas.paramsATR);
      otherDatas.templaterefList.forEach(trl => {
        if (trl.id) {
          trl.showEditTR = false;
          trl.showSaveTR = false;
          trl.showDeleteTR = false;
          trl.showCancleTR = false;
        }
      });
    };
    // 关闭弹出框---新增模板引用
    const closeATR = value => {
      otherDatas.showATR = false;
      // getTemplateRefList(otherDatas.currentCapabilityId)
    };

    // #endregion

    // #region 国网项目关联
    // 新增-国网项目引用
    const addSgTemplateref = () => {
      if (otherDatas.showSgAddTR === false) {
        ElMessage.warning('只能同时编辑一行');
        return false;
      } else if (otherDatas.tableData.length === 0) {
        ElMessage.warning('本项目关键参数为空,请新增关键参数');
        return false;
      }
      // otherDatas.oldSgItemsATR.push({
      //   // refCapabilityId: otherDatas.currentCapabilityId
      // })
      otherDatas.showSgATR = true;
    };
    // 编辑-国网项目引用
    const editSgTemplateref = async row => {
      row.disable = false;
      otherDatas.paramsATR = Object.assign(otherDatas.paramsATR, row);
      row.showEditTR = false;
      row.showSaveTR = true;
      row.showDeleteTR = false;
      row.showCancleTR = true;
      otherDatas.sgTemplaterefList.forEach(trl => {
        if (trl.id !== row.id) {
          trl.showEditTR = false;
          trl.showSaveTR = false;
          trl.showDeleteTR = false;
          trl.showCancleTR = false;
        }
      });
      otherDatas.showSgAddTR = false;
    };
    // 保存-国网项目引用
    const saveSgTemplateref = async row => {
      if (!row.capabilityParaId) {
        ElMessage.error('请选择本项目关键参数');
        return false;
      } else if (!row.refCapabilityParaId) {
        ElMessage.error('请选择被引用项目关键参数');
        return false;
      }
      const flag = await updateSgTemplateRefList();
      if (flag === true) {
        row.disable = true;
        otherDatas.showSgAddTR = true;
      }
    };
    // 删除-模板引用
    const deleteSgTemplateref = row => {
      ElMessageBox({
        title: '',
        message: '是否删除该项目？删除后不可恢复',
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        showCancelButton: true,
        closeOnClickModal: true,
        type: 'warning'
      })
        .then(() => {
          deleteExternalKeyParamMap(row.id).then(res => {
            if (res) {
              ElMessage.success('删除成功');
              getSgTemplateRefList(otherDatas.currentCapabilityId);
            }
          });
        })
        .catch(() => {});
    };
    // 取消-模板引用
    const cancelSgTemplateref = (row, index) => {
      otherDatas.showSgAddTR = true;
      getSgTemplateRefList(otherDatas.currentCapabilityId);
    };
    // 获取模板引用列表
    const getSgTemplateRefList = capabilityId => {
      return new Promise((resolve, reject) => {
        getMapKeyParamsByCapabilityId(capabilityId)
          .then(res => {
            if (res !== false) {
              const { data } = res;
              otherDatas.sgTemplaterefList = [];
              getSgMaterialClassifcation().then(response => {
                if (response && response.length > 0) {
                  otherDatas.sgMaterialList = response;
                }
                if (data.data) {
                  data.data.forEach(item => {
                    otherDatas.sgTemplaterefList.push({
                      id: item.id,
                      capabilityId: item.capabilityId,
                      capabilityName: item.capabilityName,
                      capabilityParaId: item.capabilityParaId,
                      capabilityParaName: item.capabilityParaName,
                      refCapabilityId: item.externalCapabilityId,
                      refCapabilityName: item.externalCapabilityName,
                      refCapabilityParaId: item.externalCapabilityParaId,
                      refCapabilityParaName: item.externalCapabilityParaName,
                      refMaterialCategoryName: otherDatas.sgMaterialList.find(
                        ele => ele.code === item.materialClassificationCode
                      )?.name,
                      refMaterialCategoryCode: item.materialClassificationCode,
                      capabilityparaVoList: item.externalCapabilityParaVoList,
                      capabilityParaTemplateKey: item.capabilityParaTemplateKey
                    });
                  });
                  proxy.getSgSpanArr(otherDatas.sgTemplaterefList);
                  // otherDatas.oldSgItemsATR = JSON.parse(JSON.stringify(data.data))
                  if (otherDatas.sgTemplaterefList.length > 0) {
                    otherDatas.sgTemplaterefList.forEach(list => {
                      list.disable = true;
                      list.showEditTR = true;
                      list.showSaveTR = false;
                      list.showDeleteTR = true;
                      list.showCancleTR = false;
                    });
                  }
                  otherDatas.showSgAddTR = true;
                  resolve(data.data);
                }
              });
            }
          })
          .catch(error => {
            reject(error);
          });
      });
    };
    // 新增、编辑-模板引用列表
    const updateSgTemplateRefList = () => {
      return new Promise((resolve, reject) => {
        const keyParamBody = {
          capabilityId: otherDatas.detailDataInfo.id,
          capabilityName: otherDatas.detailDataInfo.name,
          capabilityParaId: otherDatas.paramsATR.capabilityParaId,
          capabilityParaName: otherDatas.paramsATR.capabilityParaName,
          externalCapabilityId: otherDatas.paramsATR.refCapabilityId,
          externalCapabilityName: otherDatas.paramsATR.refCapabilityName,
          externalCapabilityParaId: otherDatas.paramsATR.refCapabilityParaId,
          externalCapabilityParaName: otherDatas.paramsATR.refCapabilityParaName,
          id: otherDatas.paramsATR.id || ''
        };
        if (keyParamBody.capabilityParaId && keyParamBody.externalCapabilityParaId) {
          const refList = otherDatas.sgTemplaterefList.filter(item => {
            return item.capabilityParaId === keyParamBody.capabilityParaId && item.id;
          });
          if (refList.length > 0) {
            const paraFilterList = refList.filter(item => {
              return item.refCapabilityParaId === keyParamBody.externalCapabilityParaId;
            });
            if (otherDatas.paramsATR.id) {
              const finalList = paraFilterList.filter(item => {
                return item.id !== otherDatas.paramsATR.id;
              });
              if (finalList.length > 0) {
                ElMessage.warning('该关联关系已添加，请勿重复添加!');
                return;
              }
            } else {
              if (paraFilterList.length > 0) {
                ElMessage.warning('该关联关系已添加，请勿重复添加!');
                return;
              }
            }
          }
        }
        saveExternalKeyParamMap(keyParamBody)
          .then(res => {
            if (res !== false) {
              // const { data } = res
              ElMessage.success(`${keyParamBody.id ? '更新成功!' : '新增成功!'} `);
              getSgTemplateRefList(otherDatas.currentCapabilityId);
              resolve(true);
            } else {
              resolve(false);
            }
          })
          .catch(error => {
            reject(error);
          });
      });
    };
    // 删除-模板引用
    const deleteSgTemplateRefList = id => {
      return new Promise((resolve, reject) => {
        deleteExternalKeyParamMap(id)
          .then(res => {
            if (res !== false) {
              const { data } = res;
              ElMessage.success('删除成功');
              getSgTemplateRefList(otherDatas.currentCapabilityId);
              resolve(data.data);
            }
          })
          .catch(error => {
            reject(error);
          });
      });
    };
    // 选择新增模板引用-弹出框
    const selectSgATRData = value => {
      otherDatas.showSgATR = false;
      if (value) {
        otherDatas.paramsATR = {
          id: '',
          capabilityId: otherDatas.currentCapabilityId,
          capabilityparaVoList: value.externalCapabilityParaList,
          capabilityParaId: '',
          capabilityParaName: '',
          capabilityParaTemplateKey: '',
          refCapabilityId: value.id,
          refCapabilityName: value.name,
          refMaterialCategoryName: value.materialCategoryName,
          refMaterialCategoryCode: value.materialCode,
          refCapabilityParaId: '',
          refCapabilityParaName: '',
          refCapabilityParaTemplateKey: '',
          disable: false,
          showEditTR: false,
          showSaveTR: true,
          showDeleteTR: false,
          showCancleTR: true
        };
        otherDatas.showSgAddTR = false;
        const newData = JSON.parse(JSON.stringify(otherDatas.sgTemplaterefList));
        newData.push(otherDatas.paramsATR);
        proxy.getSgSpanArr(newData);
        otherDatas.sgTemplaterefList.push(otherDatas.paramsATR);
        otherDatas.sgTemplaterefList.forEach(trl => {
          if (trl.id) {
            trl.showEditTR = false;
            trl.showSaveTR = false;
            trl.showDeleteTR = false;
            trl.showCancleTR = false;
          }
        });
      }
    };
    // 关闭弹出框---新增模板引用
    const closeSgATR = value => {
      otherDatas.showSgATR = false;
      // getSgTemplateRefList(otherDatas.currentCapabilityId)
    };

    function changeRefCapabilityPara(row) {
      const selectKeyParam = row.capabilityparaVoList.find(item => item.id === row.refCapabilityParaId);
      otherDatas.sgTemplaterefList.forEach(obj => {
        if (obj.refCapabilityParaId === row.refCapabilityParaId) {
          obj.refCapabilityParaName = selectKeyParam.name;
          obj.refCapabilityParaTemplateKey = selectKeyParam.templatekey;
        }
      });
    }

    function clearRefCapabilityPara(row) {
      otherDatas.paramsATR.refCapabilityParaName = '';
    }

    function changeSgCapabilityPara(row) {
      const selectSgKeyParam = otherDatas.tableData.find(item => item.id === row.capabilityParaId);
      otherDatas.sgTemplaterefList.forEach(obj => {
        if (obj.capabilityParaId === row.capabilityParaId) {
          obj.capabilityParaName = selectSgKeyParam.name;
          obj.capabilityParaTemplateKey = selectSgKeyParam.identifierKey;
        }
      });
    }

    function clearSgCapabilityPara(row) {
      otherDatas.paramsATR.capabilityParaName = '';
    }

    // #endregion

    const atrDatas = reactive({
      spanArr: [],
      pos: 0,
      sgSpanArr: [],
      sgPos: 0,
      spanArr1: [],
      pos1: 0
    });

    // 获取关键参数列表
    const getCapabilityList = id => {
      return new Promise(resolve => {
        getCapabilityInfo(id).then(function (res) {
          if (res !== false) {
            resolve(res.data.data);
          }
        });
      });
    };

    const testMethods = computed(() => otherDatas.detailDataInfo.method.toString().replace(',', ', ') || '--');

    const keyParamEditable = computed(() => otherDatas.showBtn && getPermissionBtn('keyEdit'));

    // 行拖拽
    function elTableRowDrop() {
      // 获取当前表格
      const tbodyEle = document.getElementById('paramTable').querySelector('.el-table__body-wrapper tbody');
      if (tbodyEle) {
        Sortable.create(tbodyEle, {
          animation: 150,
          handle: '.tes-move',
          draggable: '.el-table__row',
          ghostClass: 'ghost',
          // 拖动对象移动样式
          dragClass: 'drag',
          forceFallback: true,
          onChoose(evt) {},
          onEnd(evt) {
            if (evt.oldIndex !== evt.newIndex) {
              // 移除原来的数据
              const currRow = otherDatas.tableData.splice(evt.oldIndex, 1)[0];
              // 移除原来的数据并插入新的数据
              otherDatas.tableData.splice(evt.newIndex, 0, currRow);
              otherDatas.tableData.forEach((value, index) => {
                value.number = index + 1;
              });
              otherDatas.paramTableKey += 1;
            }
          }
        });
      }
    }

    // #region 表格功能
    watch(
      () => otherDatas.paramTableKey,
      (newValue, oldValue) => {
        nextTick(() => {
          elTableRowDrop();
        });
      }
    );
    // #endregion

    function handleOpened() {
      nextTick(() => {
        elTableRowDrop();
      });
    }

    return {
      ...toRefs(state),
      editItemCZ,
      handleDown,
      handleUp,
      initDetail,
      changeUnit,
      changeApplylabel,
      changeSubItem,
      formatValue,
      showAdd,
      modeldata,
      handleDelGW,
      capabilityId,
      closeGJ,
      closeDrawer,
      closeModel,
      editDetial,
      changeSelect,
      handleInputConfirm,
      showInput,
      handleCloseTag,
      handleQY,
      handleDel,
      addItemGWCZ,
      tabsChange,
      activeName,
      addItemCZ,
      otherDatas,
      onSubmit,
      detailDatas,
      handleClose,
      showDrawer,
      titles,
      selectATRData,
      closeATR,
      colWidth,
      addTemplateref,
      editTemplateref,
      saveTemplateref,
      deleteTemplateref,
      cancleTemplateref,
      getTemplateRefList,
      updateTemplateRefList,
      deleteTemplateRefList,
      getPermissionBtn,
      filterGJCS,
      ...toRefs(atrDatas),
      getCapabilityById,
      changeEnumOptions,
      filterEnumOptions,
      testMethods,
      paramTableRef,
      handleOpened,
      keyParamEditable,
      addSgTemplateref,
      editSgTemplateref,
      saveSgTemplateref,
      deleteSgTemplateref,
      cancelSgTemplateref,
      getSgTemplateRefList,
      updateSgTemplateRefList,
      deleteSgTemplateRefList,
      selectSgATRData,
      closeSgATR,
      changeRefCapabilityPara,
      clearRefCapabilityPara,
      changeSgCapabilityPara,
      clearSgCapabilityPara
    };
  },
  methods: {
    // 国网项目关联合并table
    getSgSpanArr(data) {
      this.sgSpanArr = [];
      // data就是我们从后台拿到的数据
      for (var i = 0; i < data.length; i++) {
        if (i === 0) {
          this.sgSpanArr.push(1);
          this.sgPos = 0;
        } else {
          // 判断当前元素与上一个元素是否相同
          if (data[i].refCapabilityId === data[i - 1].refCapabilityId) {
            this.sgSpanArr[this.sgPos] += 1;
            this.sgSpanArr.push(0);
          } else {
            this.sgSpanArr.push(1);
            this.sgPos = i;
          }
        }
      }
    },
    // 模板引用合并table
    getSpanArr(data) {
      this.spanArr = [];
      // data就是我们从后台拿到的数据
      for (var i = 0; i < data.length; i++) {
        if (i === 0) {
          this.spanArr.push(1);
          this.pos = 0;
        } else {
          // 判断当前元素与上一个元素是否相同
          if (data[i].refCapabilityName === data[i - 1].refCapabilityName) {
            this.spanArr[this.pos] += 1;
            this.spanArr.push(0);
          } else {
            this.spanArr.push(1);
            this.pos = i;
          }
        }
      }
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0 || columnIndex === 1) {
        const _row = this.spanArr[rowIndex];
        const _col = _row > 0 ? 1 : 0;
        return {
          // [0,0] 表示这一行不显示， [2,1]表示行的合并数
          rowspan: _row,
          colspan: _col
        };
      }
    },
    sgObjectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0 || columnIndex === 1 || columnIndex === 2) {
        const _row = this.sgSpanArr[rowIndex];
        const _col = _row > 0 ? 1 : 0;
        return {
          // [0,0] 表示这一行不显示， [2,1]表示行的合并数
          rowspan: _row,
          colspan: _col
        };
      }
    },
    // 获取枚举值列表
    getMeiJu() {
      return new Promise((resolve, reject) => {
        const p = {
          condition: '',
          orderby: 'desc',
          dictionaryType: '3',
          page: '1',
          limit: '10000'
        };
        getDictionaryList(p).then(res => {
          if (res !== false) {
            resolve(res.data.data.list);
          }
        });
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.toggle-btn-group {
  display: flex;
  gap: 10px;
}
::v-deep(.drawer-wrapper .drawer-title) {
  margin-bottom: 0 !important;
}
.overflowHidden {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.drawer-fotter {
  display: flex;
  align-items: center;
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  border-top: 1px solid #e4e7ed;
  text-align: right;
  padding: 10px 40px;
}
.add-btn {
  float: left;
  margin-bottom: 10px;
  // margin: 0px 0px 14px 14px;
}
.save-btn {
  float: left;
  margin: 0px 0px 14px 0px;
}
.canle-btn {
  float: left;
  margin: 0px 0px 14px 14px;
}
.result-option {
  .el-tag {
    white-space: pre-wrap;
    height: auto;
    margin: 2px 0px;
  }
}
.isCheck {
  margin-top: 12px;
  padding: 10px 20px;
  background: #f5f7fa;
  border-radius: 3px;
}
.marginTop {
  margin-top: 20px;
}
::v-deep(.dark-table) {
  .el-table__body-wrapper {
    max-height: calc(100vh - 32rem) !important;
    overflow-x: hidden !important;
    overflow-y: auto;
  }
}
.option-number {
  display: flex;
  align-items: center;
  width: 100%;
}
.multiple-select {
  width: 100%;
}
.btn-group {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 10px;
}
</style>
<style lang="scss">
.testItemDrawer .el-drawer__header {
  padding: 10px 40px 0px !important;
}
.testItemDrawer .el-drawer__body {
  padding: 10px 40px !important;
  // background: #f0f2f5;
}
.testItemDrawer {
  .el-tabs__header {
    margin-bottom: 10px;
  }
}
</style>
