<template>
  <div class="addPicture">
    <el-dialog
      v-model="dialogVisible"
      title="上传试验图片"
      :width="vertical ? 600 : 900"
      top="5vh"
      :close-on-click-modal="false"
      custom-class="imgDialog"
      @close="closeDialog"
    >
      <el-form
        v-if="dialogVisible"
        ref="formPicture"
        v-loading="imgLoading"
        :element-loading-text="loadingText"
        :model="formDataPicture"
      >
        <el-upload
          ref="uploadImg"
          class="upload-demo"
          :action="imgAction"
          :accept="'.jpg, .png'"
          multiple
          :headers="headerconfig"
          :file-list="fileList"
          :auto-upload="false"
          :show-file-list="false"
          :on-success="uploadSuccess"
          :on-change="handleChange"
          :before-upload="beforeUpload"
          :data="{ experimentId: experimentId }"
        >
          <el-button type="primary" icon="el-icon-upload2" size="small" @keyup.prevent @keydown.enter.prevent
            >选择文件</el-button
          >
          <template #tip>
            <span class="uploadTitle el-upload__tip">只能上传 jpg/png 文件，且不超过 20M</span>
          </template>
        </el-upload>
        <el-table
          v-if="dialogVisible"
          :data="formDataPicture.tablePicture"
          fit
          border
          class="imgTable dark-table"
          @header-dragend="drageHeader"
        >
          <el-table-column prop="date" label="名称" :width="200">
            <template #default="{ row, $index }">
              <el-form-item
                v-if="row.isDraft"
                :prop="`tablePicture[${$index}].name`"
                :show-message="false"
                class="pictureName"
                :rules="{ required: true, message: '请输入图片名称', trigger: 'change' }"
              >
                <el-input v-model="row.name" type="text" clearable placeholder="请输入图片名称" />
              </el-form-item>
              <div v-else class="nowrap">
                {{ row.name }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="isReport" label="生成报告" :width="120" align="left">
            <template #default="{ row }">
              <el-select v-if="row.isDraft" v-model="row.isReport" placeholder="请选择">
                <el-option v-for="(val, key) in options" :key="key" :label="val" :value="Number(key)" />
              </el-select>
              <div v-else class="nowrap">
                {{ row.isReport == '1' ? '是' : '否' }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="labelKey" label="图片key" :min-width="140" align="left">
            <template #default="{ row, $index }">
              <el-select
                v-if="row.isDraft"
                v-model="row.labelKeyList"
                popper-class="select-popper-no-tick"
                multiple
                placeholder="请选择"
                @change="
                  val => {
                    return handleSelectKey(val, $index);
                  }
                "
              >
                <el-option v-for="item in labelOptions" :key="item.id" :label="item.labelKey" :value="item.labelKey">
                  <span style="float: left">{{ item.labelKey }}</span>
                  <span style="float: right; font-size: 13px">({{ item.labelName }})</span>
                </el-option>
              </el-select>
              <div v-else class="nowrap">
                <span v-if="row.labelKeyList && row.labelKeyList.length">{{ row.labelKeyList.toString() }}</span>
                <span v-else>--</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="name" label="描述">
            <template #default="{ row }">
              <el-input
                v-if="row.isDraft"
                v-model="row.description"
                type="textarea"
                :rows="2"
                placeholder="请输入描述"
              />
              <div v-else class="nowrap">
                {{ row.description }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="address" label="图片" :width="120">
            <template #default="{ row }">
              <el-image
                v-if="row.id"
                class="curson"
                style="display: flex; width: 40px; height: 40px"
                :src="row.imageUrl"
                fit="cover"
                @click="row.showViewer = true"
              >
                <template #placeholder>
                  <div class="demo-image__error">
                    <div class="image-slot">
                      <span class="loading">加载中...&nbsp;&nbsp;&nbsp;</span><i class="el-icon-picture-outline" />
                    </div>
                  </div>
                </template>
              </el-image>
              <el-image-viewer
                v-if="row.imageUrl && row.showViewer"
                :src="row.imageUrl"
                :url-list="[row.imageUrl]"
                @close="
                  () => {
                    row.showViewer = false;
                  }
                "
              />
              <el-image
                v-if="row.imgUrl"
                class="curson"
                :class="{ redBorder: row.isErrorUpload }"
                style="display: flex; width: 40px; height: 40px"
                :src="row.imgUrl"
                fit="cover"
                @click="row.showViewer = true"
              />
              <el-image-viewer
                v-if="row.imgUrl && row.showViewer"
                :src="row.imgUrl"
                :url-list="[row.imgUrl]"
                @close="
                  () => {
                    row.showViewer = false;
                  }
                "
              />
            </template>
          </el-table-column>
          <el-table-column prop="address" label="操作" width="90" fixed="right">
            <template #default="{ row, $index }">
              <!-- 未入库数据的删除 -->
              <span v-if="row.isDraft" class="blue-color" @click="deletePicture(row, $index)">删除</span>
              <!-- 入库数据的删除 -->
              <!-- v-if="row.imageUrl" -->
              <span v-if="!row.isDraft" class="blue-color" @click="deletePicture(row, $index, true)">删除</span>
              <el-upload
                v-show="false"
                :ref="'uploadImg' + $index"
                class="upload-demo"
                :action="imgAction2"
                :accept="'.jpg, .png'"
                multiple
                :headers="headerconfig"
                :auto-upload="false"
                :file-list="row.file"
                :show-file-list="false"
                :before-upload="beforeUpload"
                :on-success="
                  (res, file, fileList) => {
                    uploadSuccess(res, file, fileList, $index);
                  }
                "
                :on-error="
                  (res, file, fileList) => {
                    uploadError(res, file, fileList, $index);
                  }
                "
                :on-change="handleChange"
                :data="
                  pageName === 'addRecord' || pageName === 'tabAddRecord'
                    ? {
                        experimentId: experimentId,
                        samplesId: samplesId,
                        description: row.description || '',
                        isReport: row.isReport,
                        name: row.name,
                        labelList: row.labelList || []
                      }
                    : {}
                "
              >
                <span v-show="false" class="red-color">重新上传</span>
              </el-upload>
              <span v-show="row.isErrorUpload" class="red-color" @click="onSubmit">重新上传</span>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeDialog">取 消</el-button>
          <el-button v-if="isShowBtn" type="primary" @click="onSubmit" @keyup.prevent @keydown.enter.prevent
            >确 认</el-button
          >
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { reactive, toRefs, getCurrentInstance, ref, watch, nextTick } from 'vue';
import { useRoute } from 'vue-router';
import { getToken } from '@/utils/auth';
import { drageHeader } from '@/utils/formatTable';
import { imgrecordList, imgrecordDelete, capabilityImageLabel } from '@/api/sampleItemTest';
import { experimentAttachmentUploadUrl, experimentImgRecordUploadUrl } from '@/api/uploadAction';
// import store from '@/store'
// import { imgrecordSave } from '@/api/sampleItemTest'
// import router from '@/router/index.js'
export default {
  name: 'AddTestPicture',
  props: {
    showPicture: {
      type: Boolean,
      default: false
    },
    pageName: {
      type: String,
      default: ''
    },
    vertical: {
      type: Boolean,
      default: false
    },
    queryInfo: {
      type: Object,
      default: function () {
        return {
          experimentId: '',
          samplesId: '',
          new: false,
          capabilityId: '',
          type: 'check'
        };
      }
    }
  },
  emits: ['isCloseImg'],
  setup(props, context) {
    // const { appContext } = getCurrentInstance()
    watch(props, newValue => {
      state.dialogVisible = newValue.showPicture;
      if (props.showPicture) {
        state.isShowBtn = false;
        state.pageName = props.pageName;
        if (props.pageName === 'addRecord' || props.pageName === 'tabAddRecord') {
          state.imgAction = experimentAttachmentUploadUrl();
          state.imgAction2 = experimentImgRecordUploadUrl();
        }
        if (props.pageName === 'tabAddRecord') {
          state.experimentId = props.queryInfo.experimentId;
          state.samplesId = props.queryInfo.samplesId;
          state.capabilityId = props.queryInfo.capabilityId;
        }
        getImgList();
        getKeyOptions();
      }
    });
    const { proxy } = getCurrentInstance();
    const route = useRoute();
    const state = reactive({
      experimentId: props.pageName !== 'tabAddRecord' ? route.query.experimentId : props.queryInfo.experimentId,
      samplesId: props.pageName !== 'tabAddRecord' ? route.query.samplesId : props.queryInfo.samplesId,
      capabilityId: props.pageName !== 'tabAddRecord' ? route.query.capabilityId : props.queryInfo.capabilityId,
      loadingText: '加载中',
      pageName: '',
      successNumber: 0, // 多附件上传时计算成功的个数
      addNum: 0, // 统计新添加的个数，用于判断最后一个是否上传成功然后关闭弹窗
      formDataPicture: {
        tablePicture: []
      },
      labelOptions: [], // 图片标签key
      imgLoading: false,
      headerconfig: {
        Authorization: getToken()
      },
      isShowBtn: true, // 是否显示确认按钮
      imgAction: experimentAttachmentUploadUrl(),
      imgAction2: experimentImgRecordUploadUrl(),
      fileList: [],
      dialogVisible: false,
      dialogUpload: false,
      jsonData: {},
      ruleForm: ref(),
      uploadImg: ref(),
      options: {
        1: '是',
        0: '否'
      },
      formPicture: ref(null)
    });
    // 获取图片列表
    const getImgList = () => {
      state.imgLoading = true;
      imgrecordList(state.experimentId).then(res => {
        state.imgLoading = false;
        if (res) {
          const data = res.data.data;
          data.forEach(item => {
            item.labelKeyList = item.labelList.map(item => item.labelKey);
          });
          state.formDataPicture.tablePicture = data;
        }
      });
    };
    const getKeyOptions = () => {
      capabilityImageLabel(state.capabilityId).then(res => {
        if (res) {
          state.labelOptions = res.data.data;
        }
      });
    };
    // 附件上传成功的钩子
    const uploadSuccess = (res, file, fileList, index) => {
      state.imgLoading = false;
      if (res.code === 200) {
        proxy.$refs['uploadImg' + index].clearFiles();
        state.successNumber++;
        state.formDataPicture.tablePicture[index].isDraft = false;
        if (state.successNumber === state.addNum) {
          proxy.$message.success('试验图片上传成功');
          closeDialog();
        }
      } else {
        proxy.$message.error(res.message);
        state.formDataPicture.tablePicture[index].isErrorUpload = true;
        clearFiles(file, index);
      }
    };
    // 附件上传失败的钩子
    const uploadError = (res, file, fileList, index) => {
      state.imgLoading = false;
      state.formDataPicture.tablePicture[index].isErrorUpload = true;
      clearFiles(file, index);
    };
    // 添加文件、解析图片实现在线预览
    const handleChange = (file, fileList) => {
      if (file.status === 'ready') {
        const isCanUpload = beforeUpload(file);
        if (isCanUpload) {
          state.imgLoading = true;
          const reader = new FileReader();
          reader.readAsDataURL(file.raw);
          reader.onload = function (e) {
            // 将bade64位图片保存至数组里供上面图片显示
            state.formDataPicture.tablePicture.unshift({
              isReport: 0,
              name: file.name.split('.')[0],
              imgUrl: e.target.result,
              file: [file],
              labelList: [],
              isDraft: true, // 是否是草稿状态，true是，false不是
              isErrorUpload: false // 是否上传失败
            });
            setTimeout(() => {
              state.imgLoading = false;
              state.isShowBtn = true;
            }, 10);
          };
        }
      }
    };
    // 图片上传之前的钩子 判断图片格式和大小
    const beforeUpload = file => {
      const fileSize = file.size / 1024 / 1024 < 20;
      var fileName = '';
      if (file.name) {
        fileName = file.name.substring(file.name.lastIndexOf('.') + 1);
      }
      if (!fileSize) {
        proxy.$message.error('上传附件大小不能超过20M');
        return false;
      } else if (file.size === 0) {
        proxy.$message.error('上传图片大小不能为空');
        return false;
      } else if (fileName !== 'jpg' && fileName !== 'png') {
        proxy.$message.error('只能上传jpg/png格式图片');
        return false;
      } else {
        return true;
      }
    };
    // 删除 isTakeEffect是否已经生效，true生效数据需要调用接口
    const deletePicture = (row, index, isTakeEffect) => {
      if (isTakeEffect) {
        proxy
          .$confirm('是否删除？删除后将不能还原。', '删除确认', {
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            type: 'warning'
          })
          .then(() => {
            imgrecordDelete(row.id).then(res => {
              if (res.data.code === 200) {
                proxy.$message.success('删除成功');
                state.formDataPicture.tablePicture.splice(index, 1);
              } else {
                proxy.$message.error(res.data.message);
              }
            });
          })
          .catch(() => {});
      } else {
        state.formDataPicture.tablePicture.splice(index, 1);
      }
    };
    const onSubmit = () => {
      state.formPicture
        .validate()
        .then(valid => {
          if (valid) {
            state.addNum = 0;
            state.formDataPicture.tablePicture.forEach((val, index) => {
              if (val.isDraft) {
                state.addNum++;
              }
            });
            nextTick(() => {
              if (state.addNum > 0) {
                state.formDataPicture.tablePicture.forEach((val, index) => {
                  if (val.isDraft) {
                    state.imgLoading = true;
                    proxy.$refs['uploadImg' + index].submit();
                    state.isShowBtn = false;
                  }
                });
                state.successNumber = 0;
              } else {
                closeDialog();
              }
            });
          }
        })
        .catch(res => {
          proxy.$message.error('请输入图片名称');
        });
    };
    const closeDialog = () => {
      state.dialogVisible = false;
      state.formDataPicture.tablePicture = [];
      context.emit('isCloseImg', false);
    };
    // 清除缓存的附件，防止上传失败，二次上传不生效
    const clearFiles = (file, index) => {
      const files = JSON.parse(JSON.stringify(state.formDataPicture.tablePicture[index].file));
      state.formDataPicture.tablePicture[index].file = files;
      proxy.$refs['uploadImg' + index].clearFiles();
    };
    // 选择图片key
    const handleSelectKey = (val, index) => {
      state.formDataPicture.tablePicture[index].labelKeyList = val;
      const labelList = state.labelOptions.filter(item => {
        return val.some(value => {
          return value === item.labelKey;
        });
      });
      state.formDataPicture.tablePicture[index].labelList = JSON.stringify(labelList);
    };
    return {
      ...toRefs(state),
      getImgList,
      handleSelectKey,
      drageHeader,
      uploadSuccess,
      uploadError,
      handleChange,
      beforeUpload,
      deletePicture,
      closeDialog,
      clearFiles,
      onSubmit
    };
  },
  computed: {},
  created() {}
};
</script>

<style lang="scss" scoped>
.imgTable {
  margin-top: 12px;
}
.redBorder {
  border: 1px solid red;
}
.el-form .pictureName {
  margin-bottom: 0 !important;
}
.red-color {
  margin-right: 0;
}
.curson {
  cursor: pointer;
}
:deep(.el-upload__tip) {
  color: #909399;
  display: inline-block;
  font-size: 14px;
  margin-left: 12px;
}
.addPicture {
  :deep(.imgDialog2 .el-upload__tip) {
    width: 250px;
    color: #909399;
    display: inline-block;
    margin-left: 12px;
    font-size: 14px;
  }
  .uploadBtn2 {
    color: $tes-primary;
    i {
      margin-right: 9.6px;
    }
  }
  .uploadBtn2.el-button--small {
    padding: 5px 16px;
    font-size: 14px;
  }
  :deep(.el-input--medium .el-input__inner) {
    height: 28px;
    line-height: 28px;
  }
  :deep(.el-input--medium) {
    line-height: 28px;
  }
  :deep(.el-input--medium .el-input__icon) {
    line-height: 28px;
  }
}
</style>
