<template>
  <!-- 新增培训、编辑培训、查看培训 -->
  <el-dialog
    v-model="dialogVisiable"
    :title="dialogTitle"
    :close-on-click-modal="false"
    :width="1020"
    top="6vh"
    @close="handleClose"
  >
    <el-form v-if="dialogVisiable" ref="formRef" :model="formData" label-width="90px" size="small">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item
            label="培训名称："
            prop="name"
            :rules="{ required: true, message: '请输入培训名称', trigger: 'change' }"
          >
            <el-input v-if="isEdit" v-model="formData.name" maxlength="100" clearable placeholder="请输入培训名称" />
            <span v-else>{{ formData.name || '--' }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="培训地点：" prop="address">
            <el-input v-if="isEdit" v-model="formData.address" maxlength="200" clearable placeholder="请输入培训地点" />
            <span v-else>{{ formData.address || '--' }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="开始日期：" prop="startDate">
            <el-date-picker
              v-if="isEdit"
              v-model="formData.startDate"
              :disabled-date="
                time => {
                  return disabledDateStart(time, formData.endDate);
                }
              "
              type="date"
              placeholder="请选择开始日期"
              size="small"
              style="width: 100%"
            />
            <span v-else>{{ formData.startDate || '--' }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="结束日期：" prop="endDate">
            <el-date-picker
              v-if="isEdit"
              v-model="formData.endDate"
              :disabled-date="
                time => {
                  return disabledDateEnd(time, formData.startDate);
                }
              "
              type="date"
              placeholder="请选择结束日期"
              size="small"
              style="width: 100%"
            />
            <span v-else>{{ formData.endDate || '--' }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="培训附件：" prop="attachmentList">
            <el-upload
              v-if="isEdit"
              v-model:file-list="formData.attachmentList"
              :action="uploadAction"
              :headers="headerconfig"
              :before-upload="beforeUpload"
              multiple
              :on-success="
                res => {
                  return uploadSuccess(res);
                }
              "
              :on-preview="handlePreview"
              :on-remove="
                (file, fileList) => {
                  return handleRemove(file, fileList);
                }
              "
            >
              <el-button type="primary" icon="el-icon-upload">点击上传</el-button>
              <template #tip>
                <span class="el-upload__tip"> 单个文件大小不能超过20M </span>
              </template>
            </el-upload>
            <div v-else-if="formData.attachmentList?.length">
              <span v-for="item in formData.attachmentList" :key="item.id" class="link" @click="handlePreview(item)"
                >{{ item.fileName }}；</span
              >
            </div>
            <span v-else>--</span>
          </el-form-item>
        </el-col>
      </el-row>
      <div class="title">人员成绩</div>
      <div v-if="isEdit" class="text-left mb-3">
        <el-button type="primary" icon="el-icon-plus" @click="handleAdd"> 新增</el-button>
      </div>
      <el-table
        ref="tableRef"
        :key="tableKey"
        v-loading="dialogLoading"
        :data="formData.detailEntityList"
        fit
        border
        size="medium"
        class="dark-table base-table format-height-table3 no-quick-query table-content"
        @header-dragend="drageHeader"
      >
        <el-table-column label="培训人员" prop="userId" :min-width="colWidth.orderNo" show-overflow-tooltip>
          <template #default="{ row, $index }">
            <el-form-item
              v-if="isEdit"
              :prop="`detailEntityList.${$index}.userId`"
              label-width="0"
              :rules="{ required: true, message: '请选择培训人员', trigger: 'change' }"
            >
              <el-select
                v-model="row.userId"
                placeholder="请选择培训人员"
                filterable
                clearable
                style="width: 100%"
                size="small"
              >
                <el-option
                  v-for="item in userList"
                  :key="item.userId"
                  :label="getNameByid(item.userId) || item.userId"
                  :value="item.userId"
                />
              </el-select>
            </el-form-item>
            <UserTag v-else :name="getNameByid(row.userId) || '--'" />
          </template>
        </el-table-column>
        <el-table-column label="理论成绩" prop="theoryScore" :min-width="100" show-overflow-tooltip>
          <template #default="{ row, $index }">
            <el-form-item
              v-if="isEdit"
              label-width="0"
              :prop="`detailEntityList.${$index}.theoryScore`"
              :rules="{ validator: greaterThanZero, trigger: 'change' }"
            >
              <el-input
                v-model="row.theoryScore"
                v-trim
                maxlength="10"
                autocomplete="off"
                clearable
                placeholder="请输入理论成绩"
              />
            </el-form-item>
            <span v-else>{{ row.theoryScore || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="实操成绩" prop="practicalScore" :min-width="100" show-overflow-tooltip>
          <template #default="{ row, $index }">
            <el-form-item
              v-if="isEdit"
              label-width="0"
              :prop="`detailEntityList.${$index}.practicalScore`"
              :rules="{ validator: greaterThanZero, trigger: 'change' }"
            >
              <el-input
                v-model="row.practicalScore"
                v-trim
                maxlength="10"
                autocomplete="off"
                clearable
                placeholder="请输入实操成绩"
              />
            </el-form-item>
            <span v-else>{{ row.practicalScore || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="培训附件" prop="attachmentList" align="center" :min-width="160">
          <template #default="{ row, $index }">
            <el-form-item v-if="isEdit" label-width="0" :prop="`detailEntityList.${$index}.attachmentList`">
              <el-upload
                v-model:file-list="row.attachmentList"
                :action="uploadAction"
                :headers="headerconfig"
                :before-upload="beforeUpload"
                multiple
                :on-success="
                  res => {
                    return uploadSuccess(res, $index);
                  }
                "
                :on-preview="handlePreview"
                :on-remove="
                  (file, fileList) => {
                    return handleRemove(file, fileList, $index);
                  }
                "
              >
                <el-button type="text">点击上传</el-button>
              </el-upload>
            </el-form-item>
            <div v-else-if="row.attachmentList?.length">
              <span v-for="item in row.attachmentList" :key="item.id" class="link" @click="handlePreview(item)"
                >{{ item.fileName }}；</span
              >
            </div>
            <span v-else>--</span>
          </template>
        </el-table-column>
        <el-table-column v-if="isEdit" label="操作" :width="colWidth.operationSingle">
          <template #default="{ row, $index }">
            <span class="blue-color" @click="handleDelete(row, $index)">删除</span>
          </template>
        </el-table-column>
      </el-table>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button :loading="dialogLoading" @click="handleClose">取消</el-button>
        <el-button
          v-if="isEdit"
          type="primary"
          :loading="dialogLoading"
          @click="onSubmit"
          @keyup.prevent
          @keydown.enter.prevent
          >确定</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>
<script>
import { ref, watch, reactive, getCurrentInstance, toRefs } from 'vue';
import { getNameByid } from '@/utils/common';
import { drageHeader } from '@/utils/formatTable';
import { formatDate } from '@/utils/formatTime';
import UserTag from '@/components/UserTag';
import { colWidth } from '@/data/tableStyle';
import { uploadAttachmentUrl } from '@/api/uploadAction';
// utils
import { greaterThanZero } from '@/utils/validate';
import { getToken } from '@/utils/auth';
// Api
import { saveOrUpdate, employeetrainInfo } from '@/api/training-manage';
import { deleteFile, downloadById } from '@/api/departManagement';

export default {
  name: 'DialogTrainingDetail',
  components: { UserTag },
  props: {
    dialogShow: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: ''
    },
    nameList: {
      type: Array,
      default: function () {
        return [];
      }
    },
    rowDetail: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['closeDialog'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const state = reactive({
      dialogVisiable: false,
      dialogTitle: '',
      disabledDateStart: (time, endTime) => {
        if (endTime) {
          return time.getTime() > new Date(endTime).getTime();
        } else {
          return;
        }
      },
      disabledDateEnd: (time, startTime) => {
        if (startTime) {
          return time.getTime() < new Date(startTime).getTime();
        } else {
          return;
        }
      },
      isEdit: false,
      tableKey: 0,
      headerconfig: {
        Authorization: getToken()
      },
      uploadAction: uploadAttachmentUrl(),
      userList: [],
      formRef: ref(),
      deleteTable: [],
      formData: {
        attachmentList: [],
        detailEntityList: []
      },
      dialogLoading: false // 详情页的loading
    });

    watch(props, newValue => {
      state.dialogVisiable = props.dialogShow;
      if (props.dialogShow) {
        state.userList = props.nameList;
        if (props.type == 'add') {
          state.dialogTitle = '新增培训';
          state.isEdit = true;
          state.formData = {
            attachmentList: [],
            detailEntityList: []
          };
        } else {
          initDetail();
          if (props.type == 'edit') {
            state.isEdit = true;
            state.dialogTitle = '编辑培训';
          } else {
            state.isEdit = false;
            state.dialogTitle = '查看培训';
          }
        }
      }
    });
    // 查询详情
    const initDetail = async () => {
      state.dialogLoading = true;
      const response = await employeetrainInfo(props.rowDetail.id).finally((state.dialogLoading = false));
      if (response) {
        state.formData = response.data.data;
      }
    };
    // 新增
    const handleAdd = () => {
      state.formData.detailEntityList.push({
        attachmentList: []
      });
    };
    const handleDelete = (row, index) => {
      if (row.id) {
        state.deleteTable.push({
          ...row,
          isDeleted: 1
        });
      }
      state.formData.detailEntityList.splice(index, 1);
    };
    const onSubmit = () => {
      state.formRef.validate(async valid => {
        if (valid) {
          state.dialogLoading = true;
          const params = {
            ...state.formData,
            detailEntityList: [...state.formData.detailEntityList, ...state.deleteTable]
          };
          const response = await saveOrUpdate(params).finally((state.dialogLoading = false));
          if (response) {
            proxy.$message.success('保存成功！');
            context.emit('closeDialog', true);
            state.dialogVisiable = false;
          }
        } else {
          return false;
        }
      });
    };
    const handleClose = () => {
      context.emit('closeDialog', false);
      state.dialogVisiable = false;
    };
    const uploadSuccess = (res, index) => {
      if (res.code === 200) {
        if (index || index == 0) {
          state.formData.detailEntityList[index].attachmentList.push(res.data);
        } else {
          state.formData.attachmentList.push(res.data);
        }
      } else {
        proxy.$message.error(res.message);
      }
    };
    const beforeUpload = file => {
      const fileSize = file.size / 1024 / 1024 < 20;
      if (!fileSize) {
        proxy.$message.error('上传附件大小不能超过20M');
        return false;
      } else if (file.size === 0) {
        proxy.$message.error('上传附件大小不能为空');
        return false;
      } else {
        return true;
      }
    };
    const handlePreview = async file => {
      state.dialogLoading = true;
      const response = await downloadById(file.id).finally((state.dialogLoading = false));
      if (response) {
        const blob = new Blob([response.data], { type: '' });
        const blobUrl = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.download = `${file.name}`;
        a.href = blobUrl;
        a.click();
        proxy.$message.success('下载附件成功');
      }
    };
    const handleRemove = async (file, fileList, index) => {
      if (file?.id) {
        const response = await deleteFile(file.id);
        if (response) {
          if (index || index == 0) {
            state.formData.detailEntityList[index].attachmentList = fileList;
          } else {
            state.formData.attachmentList = fileList;
          }
        }
      }
    };
    return {
      ...toRefs(state),
      initDetail,
      greaterThanZero,
      uploadSuccess,
      handleDelete,
      beforeUpload,
      handlePreview,
      handleRemove,
      handleAdd,
      onSubmit,
      drageHeader,
      colWidth,
      handleClose,
      getNameByid,
      formatDate
    };
  }
};
</script>

<style lang="scss" scoped>
.link {
  color: $tes-primary;
  cursor: pointer;
}
.title {
  font-size: 18px;
  margin: 10px 0 10px 0;
}
:deep(.format-height-table3) {
  .el-table__body-wrapper {
    max-height: calc(100vh - 550px);
    overflow-y: auto;
  }
  .el-table__fixed-body-wrapper {
    max-height: calc(100vh - 550px);
    overflow-y: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }
  .el-table__fixed-body-wrapper::-webkit-scrollbar {
    display: none;
  }
}
:deep(.el-icon-close-tip, .el-icon-close) {
  display: none !important;
}
.table-content {
  :deep(.el-form-item--small.el-form-item) {
    margin-bottom: 0;
  }
}
</style>
