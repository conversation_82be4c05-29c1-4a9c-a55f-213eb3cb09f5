<template>
  <el-dialog
    :model-value="showDialog"
    custom-class="add-report"
    title="选择样品"
    width="1000px"
    top="50px"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    @close="close"
  >
    <el-row class="add-report-search">
      <el-col :span="12">
        <el-radio-group v-model="listQuery.dataType" size="small" style="margin-bottom: 15px" @change="searchItem">
          <el-radio-button label="">全部</el-radio-button>
          <el-radio-button :label="0">未完成样品</el-radio-button>
          <el-radio-button :label="1">已完成样品</el-radio-button>
        </el-radio-group>
      </el-col>
      <el-col :span="12" style="text-align: right">
        <el-input
          ref="inputRef"
          v-model="filterText"
          v-trim
          class="search"
          size="small"
          placeholder="请输入编号/样品名称/批号/盘号/合同号"
          prefix-icon="el-icon-search"
          clearable
          @keyup.enter="searchItem()"
        />
        <el-button type="primary" size="small" style="margin-left: 15px" @click="searchItem()">查询</el-button>
      </el-col>
    </el-row>
    <el-table
      ref="tableRef"
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      fit
      border
      height="calc(100vh - 400px)"
      size="medium"
      class="dark-table base-table add-report-table"
      :row-style="
        () => {
          return 'cursor: pointer';
        }
      "
      @header-dragend="drageHeader"
      @selection-change="handleSelectionChange"
      @row-click="handleRowClick"
    >
      <el-table-column type="selection" :width="70" :selectable="selectable" />
      <el-table-column
        label="样品编号"
        prop="secSampleNum"
        :min-width="colWidth.orderNo"
        sortable
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <span>{{ row.secSampleNum || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="委托编号"
        prop="presentationCode"
        :min-width="colWidth.orderNo"
        sortable
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <span>{{ row.presentationCode || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="样品名称" prop="mateName" :min-width="colWidth.name" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.mateName || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="试验负责人" prop="user" :width="colWidth.person">
        <template #default="{ row }">
          <UserTag :name="getNameByid(row.ownerId) || '--'" />
        </template>
      </el-table-column>
      <el-table-column label="样品状态" prop="status" :width="colWidth.status" show-overflow-tooltip>
        <template #default="{ row }">
          <el-tag size="small" effect="dark" :type="filterStatus(row.status)[0]">
            {{ filterStatus(row.status)[1] }}</el-tag
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      small
      :page="listQuery.page"
      :limit="listQuery.limit"
      :total="total"
      @pagination="getSampleOrderList"
    />
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="close">取 消</el-button>
        <el-button
          type="primary"
          :loading="addReportLoading"
          @click="dialogSuccess"
          @keyup.prevent
          @keydown.enter.prevent
          >确定</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { ref, reactive, toRefs, watch, getCurrentInstance, nextTick } from 'vue';
import { getNameByid } from '@/utils/common';
import { sampleReportList } from '@/api/order';
// import { GetInventorySampleBySampleId } from '@/api/login'
import { ElMessage, ElLoading } from 'element-plus';
import Pagination from '@/components/Pagination';
import UserTag from '@/components/UserTag';
import { drageHeader } from '@/utils/formatTable';
import { addReportList } from '@/api/testReport';
import { formatDate } from '@/utils/formatTime';
import { getWarehousinginfo } from '@/api/samplestorage';
import { colWidth } from '@/data/tableStyle';
// import _ from 'lodash'
// import { ElLoading } from 'element-plus'
// import { useRoute } from 'vue-router'
// import { formatPaginationList } from '@/utils/formatJson'

export default {
  name: 'AddReport',
  components: { Pagination, UserTag },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['close', 'selectData'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const datas = reactive({
      showDialog: props.show,
      filterText: '',
      inputRef: ref(),
      listLoading: false,
      tableKey: 0,
      list: [],
      total: 0,
      listQuery: {
        page: 1,
        limit: 20,
        param: '',
        dataType: 0
      },
      selectList: [],
      newList: [],
      addReportLoading: false,
      tableRef: ref(null)
    });

    watch(
      () => props.show,
      async newValue => {
        if (newValue) {
          await proxy.getSampleOrderList();
          datas.showDialog = newValue;
          nextTick(() => {
            datas.inputRef.focus();
          });
        }
      },
      { deep: true }
    );

    // 过滤样品状态颜色
    const filterStatus = status => {
      status = status.toString();
      const classMap = {
        0: ['info', '待下达'],
        1: ['warning', '检测中'],
        2: ['warning', '检测中'],
        3: ['warning', '检测中'],
        4: ['success', '检测完成']
      };
      return classMap[status];
    };

    // 查询
    const searchItem = () => {
      datas.listQuery.param = datas.filterText;
      proxy.getSampleOrderList();
    };

    // 确定选择
    const dialogSuccess = async () => {
      if (datas.selectList.length === 0) {
        ElMessage.warning('请勾选样品');
        return false;
      }
      datas.addReportLoading = true;
      await proxy.getInventorySampleBySampleId();
      const param = { reportRquestList: datas.selectList };
      // console.log(param)
      const loading = ElLoading.service({
        lock: true,
        text: '请稍后...',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      addReportList(param).then(res => {
        if (res !== false) {
          ElMessage.success('新增成功');
          datas.showDialog = false;
          datas.addReportLoading = false;
          datas.filterText = '';
          datas.dataType = 1;
          datas.listQuery = {
            page: 1,
            limit: 20,
            param: ''
          };
          loading.close();
          context.emit('selectData', datas.selectList);
          context.emit('close', false);
        } else {
          loading.close();
          datas.addReportLoading = false;
          ElMessage.error('新增失败!');
        }
      });
    };
    // 取消选择
    const close = () => {
      datas.showDialog = false;
      datas.listQuery = {
        page: 1,
        limit: 20,
        param: '',
        dataType: 0
      };
      datas.filterText = '';
      datas.dataType = 1;
      context.emit('close', false);
    };
    // 列表选择事件
    const handleSelectionChange = val => {
      datas.selectList = val;
    };
    // 是否禁用checkbox
    const selectable = (row, index) => {
      if (row.disable) {
        return false;
      } else {
        return true;
      }
    };

    function handleRowClick(row) {
      datas.tableRef.toggleRowSelection(
        row,
        !datas.selectList.some(item => {
          return row.id === item.id;
        })
      );
    }

    return {
      ...toRefs(datas),
      close,
      getNameByid,
      dialogSuccess,
      drageHeader,
      handleSelectionChange,
      selectable,
      filterStatus,
      searchItem,
      colWidth,
      handleRowClick
    };
  },
  created() {
    // this.getSampleOrderList()
  },
  methods: {
    getSampleOrderList(value) {
      const that = this;
      that.listLoading = true;
      if (value && value !== undefined) {
        that.listQuery.page = value.page;
        that.listQuery.limit = value.limit;
      }
      const param = JSON.parse(JSON.stringify(that.listQuery));
      param.page = param.page + '';
      param.limit = param.limit + '';
      param.status = '1';
      param.isInvalidated = '0';
      sampleReportList(param).then(res => {
        if (res !== false) {
          that.list = res.data.data.list;
          that.total = res.data.data.totalCount;
          if (that.list.length > 0) {
            that.list.forEach((order, index) => {
              order.selected = false;
              order.sampleName = order.mateName;
            });
          }
        }
        that.listLoading = false;
      });
    },
    getInventorySampleBySampleId() {
      var that = this;
      const num = this.selectList.length - 1;
      // var sampleInformation = ''
      return new Promise(resolve => {
        this.selectList.forEach((sl, index) => {
          // 入库信息，调用lims接口
          // GetInventorySampleBySampleId({ sampleId: sl.sampleId }).then(res => {
          //   // console.log(res.data)
          //   if (undefined !== res.data && res.data.inventorySamples.length > 0) {
          //     const rukuInfo = res.data.inventorySamples[0].inventorySample
          //     const status = rukuInfo.sampleWarehousingStatusTypeKey === 'perfect' ? '完好' : '其他'
          //     const info = formatDate(sl.createTime) + '/' + status
          //     sl.sampleInformation = info === '/' ? '' : info
          //     console.log(sl.sampleInformation)
          //   } else {
          //     sl.sampleInformation = formatDate(sl.createTime) + '/--'
          //   }
          //   if (num === index) {
          //     // console.log(that.selectList)
          //     resolve(that.selectList)
          //   }
          // })

          getWarehousinginfo(sl.id, sl.sampleId).then(res => {
            if (res.data.code === 200 && res.data.data.list.length > 0) {
              console.log(res.data.data.list);
              const rukuInfo = res.data.data.list[0];
              const info = formatDate(sl.createTime) + '/' + rukuInfo.sampleWarehousingStatus;
              sl.sampleInformation = info === '/' ? '' : info;
              console.log(sl.sampleInformation);
            } else {
              sl.sampleInformation = formatDate(sl.createTime) + '/--';
            }
            if (num === index) {
              // console.log(that.selectList)
              resolve(that.selectList);
            }
          });
        });
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.add-report {
  .search {
    width: 360px;
  }

  .add-report-search {
    margin-bottom: 15px;
  }
}
</style>
<style lang="scss">
.add-report {
  .el-table thead th {
    background: #f6f6f6;
  }
  .el-dialog__body {
    padding-bottom: 0;
  }
}
</style>
