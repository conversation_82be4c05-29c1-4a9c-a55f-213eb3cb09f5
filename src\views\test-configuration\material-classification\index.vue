<template>
  <!-- 试验配置 - 物资分类列表 -->
  <ListLayout
    :has-page-header="false"
    :has-quick-query="false"
    :has-left-panel="true"
    :aside-panel-width="asidePanelWidth"
  >
    <template #page-left-side>
      <div class="tree-container">
        <div class="tree-header">
          <el-input
            ref="searchTreeInput"
            v-model="filterText"
            size="small"
            placeholder="请输入类目名称"
            prefix-icon="el-icon-search"
          />
          <el-button
            v-if="getPermissionBtn('materialTreeAdd')"
            class="addTreeBtn"
            size="small"
            icon="el-icon-plus"
            @click="addTreeItem"
            @keyup.prevent
            @keydown.enter.prevent
          />
        </div>
        <div class="tree-content">
          <el-tree
            ref="tree"
            :data="treeData"
            node-key="id"
            :props="defaultProps"
            default-expand-all
            :expand-on-click-node="false"
            :highlight-current="true"
            draggable
            :filter-node-method="filterNode"
            :current-node-key="currentNodeKey"
            class="leftTree"
            @node-click="clickNode"
          >
            <template #default="{ node }">
              <span>{{ node.label }}</span>
              <el-dropdown
                class="tree-dropdown el-icon"
                trigger="hover"
                :class="node.showIcon ? 'icon-show' : ''"
                @visible-change="changeIcon(node.showIcon, node)"
              >
                <i class="el-icon-more" />
                <template v-if="getPermissionBtn('editMCTreeBtn') || getPermissionBtn('delMCTreeBtn')" #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item v-if="getPermissionBtn('editMCTreeBtn')" @click="editTree(node.data, node)"
                      ><i class="iconfont tes-edit" />编辑</el-dropdown-item
                    >
                    <el-dropdown-item
                      v-if="getPermissionBtn('delMCTreeBtn')"
                      class="color-red"
                      @click="delTree(node.data)"
                      ><i class="iconfont tes-delete" />删除</el-dropdown-item
                    >
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-tree>
        </div>
      </div>
    </template>
    <el-tabs v-model="activeName" class="tab-box" @tab-click="tabChange">
      <el-tab-pane v-if="getPermissionBtn('BasicConfigurationTab')" label="基础配置" name="first">
        <div class="header-btn-group">
          <el-button
            v-if="showEdit && getPermissionBtn('settingEdit')"
            icon="el-icon-edit"
            type="primary"
            size="small"
            @click="edit"
            >编辑</el-button
          >
          <el-button v-if="!showEdit" type="primary" size="small" @click="saveEdit">保存</el-button>
          <el-button v-if="!showEdit" size="small" @click="cancelEdit">取消</el-button>
        </div>
        <el-form ref="form" :model="formData" label-position="right" label-width="110px">
          <el-form-item label="检测周期：">
            <el-input-number
              v-model="formData.detectionCycle"
              size="small"
              :disabled="isDisable"
              controls-position="right"
              :min="0"
              :max="10000"
              :precision="0"
            />
            天
          </el-form-item>
          <el-form-item label="是否留样：">
            <el-radio v-model="formData.isRetention" :disabled="isDisable" :label="1">是</el-radio>
            <el-radio v-model="formData.isRetention" :disabled="isDisable" :label="0">否</el-radio>
          </el-form-item>
          <el-form-item label="是否制样：">
            <el-radio v-model="formData.isSamplePreparation" :disabled="isDisable" :label="1">是</el-radio>
            <el-radio v-model="formData.isSamplePreparation" :disabled="isDisable" :label="0">否</el-radio>
          </el-form-item>
          <el-form-item v-if="getPermissionBtn('samplestorageBtn')" label="入库验证：">
            <el-radio v-model="formData.isWarehousingVerification" :disabled="isDisable" :label="1">是</el-radio>
            <el-radio v-model="formData.isWarehousingVerification" :disabled="isDisable" :label="0">否</el-radio>
          </el-form-item>
          <el-form-item label="领用验证：">
            <el-radio-group v-model="formData.isReceiveVerification" @change="handleChangeLy">
              <el-radio :disabled="isDisable" :label="1">是</el-radio>
              <el-radio :disabled="isDisable" :label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item v-if="formData.isReceiveVerification === 1" label="流转方式：">
            <el-radio v-model="formData.circulationMode" :disabled="isDisable" label="0">
              单向流转
              <el-tooltip
                content="设备类样品，一台设备只能同时一人使用，回库后其他人才能领用做其它试验"
                placement="bottom"
                effect="light"
              >
                <i class="iconfont tes-title" />
              </el-tooltip>
            </el-radio>
            <el-radio v-model="formData.circulationMode" :disabled="isDisable" label="1">
              材料领用
              <el-tooltip
                content="材料消耗类样品，可以多人同时领用，同时开展不同试验项目"
                placement="bottom"
                effect="light"
              >
                <i class="iconfont tes-title" />
              </el-tooltip>
            </el-radio>
          </el-form-item>
          <el-form-item label="报告骑缝章：">
            <el-radio-group v-model="formData.isReportSeal">
              <el-radio :disabled="isDisable" :label="1">开启</el-radio>
              <el-radio :disabled="isDisable" :label="0">关闭</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </el-tab-pane>
      <el-tab-pane v-if="getPermissionBtn('ProductModel')" label="试样分组" name="second">
        <div class="header-search-group">
          <div class="search-bar">
            <el-input
              ref="xhSearchRef"
              v-model="filterPMText"
              v-trim
              size="small"
              placeholder="请输入分组名称"
              prefix-icon="el-icon-search"
              clearable
              @keyup.enter="searchPM"
            />
            <el-button type="primary" size="small" style="margin-left: 10px" @click="searchPM">查询</el-button>
          </div>
          <el-button
            v-if="getPermissionBtn('productAdd')"
            type="primary"
            icon="el-icon-plus"
            size="small"
            @click="handleAddEditGroup()"
            @keyup.prevent
            @keydown.enter.prevent
            >新增试样分组</el-button
          >
        </div>
        <el-table
          ref="tableRef"
          :key="tableKey"
          v-loading="listLoading"
          :data="tableGroup"
          fit
          border
          size="medium"
          class="dark-table base-table format-height-table"
          @header-dragend="drageHeader"
        >
          <el-table-column label="序号" prop="index" align="center" :width="colWidth.serialNo">
            <template #default="{ $index }">
              <div>{{ $index + 1 }}</div>
            </template>
          </el-table-column>
          <el-table-column label="分组key" prop="coreKey" :min-width="colWidth.model" show-overflow-tooltip>
            <template #default="{ row }">
              <span>{{ row.coreKey }}</span>
            </template>
          </el-table-column>
          <el-table-column label="分组名称" prop="name" :min-width="colWidth.name" show-overflow-tooltip>
            <template #default="{ row }">
              <span>{{ row.name }}</span>
            </template>
          </el-table-column>
          <el-table-column label="状态" prop="name" :min-width="colWidth.status" show-overflow-tooltip>
            <template #default="{ row }">
              <el-tag :type="row.status === 0 ? 'primary' : 'info'" effect="dark" size="small">{{
                row.status === 0 ? '启用' : '停用'
              }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" :width="colWidth.operation">
            <template #default="{ row }">
              <span v-if="getPermissionBtn('productEdit')" class="blue-color" @click="handleAddEditGroup(row)"
                >编辑</span
              >
              <span
                v-if="getPermissionBtn('groupOutOfService') && row.status === 0"
                class="blue-color"
                @click="handleDisuse(row)"
                >停用</span
              >
              <span
                v-if="getPermissionBtn('groupEnable') && row.status === 1"
                class="blue-color"
                @click="handleEnable(row)"
                >启用</span
              >
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total > 0"
          :page="listQuery.page"
          :limit="listQuery.limit"
          :total="total"
          @pagination="getList"
        />
      </el-tab-pane>
      <el-tab-pane v-if="getPermissionBtn('ProductSpecification')" label="产品规格" name="fourth">
        <Specifications
          :category-id="currentData.id"
          :category-code="currentData.code"
          :active-name="activeName"
          :current-data="currentData"
        />
      </el-tab-pane>
      <el-tab-pane v-if="getPermissionBtn('ReportManagement')" label="报告管理" name="third">
        <ReportManage :category-id="currentData.id" :category-code="currentData.code" :current-data="currentData" />
      </el-tab-pane>
      <el-tab-pane v-if="getPermissionBtn('PictureLabel')" label="图片标签" name="fifth">
        <TabImageTag :current-data="currentData" :parent-type="1" />
      </el-tab-pane>
      <el-tab-pane v-if="getPermissionBtn('CertificateTemplate')" label="模板管理" name="sixth">
        <CertificateTemplate
          :category-id="currentData.id"
          :category-code="currentData.code"
          :current-data="currentData"
        />
      </el-tab-pane>
      <el-tab-pane label="小样配置" name="1">
        <SmallSampleSupplement type="1" :active-name="activeName" :current-data="currentData" />
      </el-tab-pane>
      <el-tab-pane label="样品补充" name="2">
        <SmallSampleSupplement type="2" :active-name="activeName" :current-data="currentData" />
      </el-tab-pane>
    </el-tabs>
  </ListLayout>
  <!-- 添加树节点弹出框 -->
  <el-dialog
    v-model="showEditDialog"
    :title="isAddTree === true ? '添加类目' : '编辑类目'"
    width="480px"
    :close-on-click-modal="false"
  >
    <el-form v-if="showEditDialog" ref="formTree" :model="dialogFrom" :rules="dialogRules" label-position="right">
      <el-form-item label="类目名称：" prop="name" :label-width="formLabelWidth" style="margin-bottom: 20px">
        <el-input
          ref="inputRefDialog"
          v-model="dialogFrom.name"
          autocomplete="off"
          :input="(dialogFrom.name = dialogFrom.name.replace(/\s+/g, ''))"
          placeholder="请输入类目名称"
          @keyup.enter="editDialogSuccess"
          @keyup.prevent
          @keydown.enter.prevent
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="editDialogSuccess" @keyup.prevent @keydown.enter.prevent>确 定</el-button>
      </span>
    </template>
  </el-dialog>
  <!-- 新增/编辑 试样分组 -->
  <el-dialog
    v-model="dialogGroup"
    :title="isAddGroup ? '新增分组' : '编辑分组'"
    width="480px"
    :close-on-click-modal="false"
    @close="onDialogGroupClose"
  >
    <el-form ref="groupRef" :model="formdataGroup" label-position="right">
      <el-form-item
        label="试样分组名称："
        prop="name"
        :label-width="formLabelWidth"
        style="margin-bottom: 20px"
        :rules="{ required: true, message: '请输入试样分组名称', trigger: 'change' }"
      >
        <el-input
          ref="inputRef"
          v-model="formdataGroup.name"
          v-trim
          autocomplete="off"
          placeholder="请输入试样分组名称"
        />
      </el-form-item>
      <el-form-item label="试样分组key：" prop="coreKey" :label-width="formLabelWidth" style="margin-bottom: 20px">
        <el-input v-model="formdataGroup.coreKey" disabled autocomplete="off" placeholder="请输入试样分组key" />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="cancelGroup">取 消</el-button>
        <el-button type="primary" @click="onSubmitGroup">确 定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { reactive, toRefs, ref, watch, getCurrentInstance, nextTick } from 'vue';
import ReportManage from './components/reportManage.vue';
import Specifications from './components/specification.vue';
import CertificateTemplate from './components/certificateTemplate.vue';
import SmallSampleSupplement from './components/smallSampleSupplement.vue';
import TabImageTag from '@/components/BusinessComponents/TabImageTag.vue';
import {
  getMaterialcategoryTree,
  materialCategorydetailInfo,
  saveMaterialcategory,
  updateMaterialcategory,
  deleteMaterialcategory,
  updateMaterialcategoryDetailInfo,
  getProductModelList,
  saveProductModel,
  updateProductModel
} from '@/api/material';
import { ElMessage, ElMessageBox } from 'element-plus';
import { formatAllTree, formatTree } from '@/utils/formatJson';
import ListLayout from '@/components/ListLayout';
import Pagination from '@/components/Pagination';
import { drageHeader } from '@/utils/formatTable';
import { getPermissionBtn } from '@/utils/common';
import { colWidth } from '@/data/tableStyle';

export default {
  name: 'MaterialClassification',
  components: {
    ListLayout,
    ReportManage,
    Specifications,
    TabImageTag,
    Pagination,
    CertificateTemplate,
    SmallSampleSupplement
  },
  setup(props, context) {
    const { proxy, appContext } = getCurrentInstance();
    const bus = appContext.config.globalProperties.bus;
    const datas = reactive({
      asidePanelWidth: 300,
      dialogTreeData: {},
      filterText: '',
      filterPMText: '',
      inputRef: ref(),
      inputRefDialog: ref(),
      searchTreeInput: ref(),
      xhSearchRef: ref(),
      treeData: [],
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      dialogFrom: {
        name: '',
        parentId: ''
      },
      formdataGroup: {
        name: '',
        coreKey: '',
        categoryId: '',
        categoryCode: ''
      },
      activeName: 'first',
      formData: {
        categoryId: 0,
        detectionCycle: 0,
        circulationMode: 1,
        isRetention: '',
        isReportSeal: 0,
        isSamplePreparation: '',
        isReceiveVerification: 0,
        isWarehousingVerification: 0
      },
      currentParentForm: {},
      currentFormData: {},
      currentData: {},
      categoryProps: {
        expandTrigger: 'hover',
        checkStrictly: true,
        children: 'children',
        label: 'name',
        value: 'id'
      },
      showEdit: true,
      isAddGroup: false,
      showEditDialog: false,
      dialogGroup: false,
      isAddTree: true,
      dialogRules: {
        name: [{ required: true, message: '请输入类目名称' }],
        parentId: [{ required: true, message: '请选择父级目录' }]
      },
      formLabelWidth: '120px',
      isDisable: true,
      showIcon: false,
      tableGroup: [],
      listQuery: {
        page: 1,
        limit: 20,
        categoryId: '',
        productModel: ''
      },
      tableKey: 0,
      listLoading: false,
      total: 0,
      currentNodeKey: '',
      groupRef: ref()
    });
    const initActiveName = () => {
      if (getPermissionBtn('BasicConfigurationTab')) {
        datas.activeName = 'first';
      } else if (getPermissionBtn('ProductModel')) {
        datas.activeName = 'second';
      } else if (getPermissionBtn('ProductSpecification')) {
        datas.activeName = 'fourth';
      } else if (getPermissionBtn('ReportManagement')) {
        datas.activeName = 'third';
      } else if (getPermissionBtn('PictureLabel')) {
        datas.activeName = 'fifth';
      } else if (getPermissionBtn('CertificateTemplate')) {
        datas.activeName = 'sixth';
      }
    };
    initActiveName();
    // 过滤树节点
    const tree = ref(null);
    nextTick(() => {
      datas.searchTreeInput.focus();
    });
    watch(
      () => datas.filterText,
      newValue => {
        // console.log(newValue)
        tree.value.filter(newValue);
      }
    );
    const filterNode = (value, data) => {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    };
    const loadNode = (node, resolve) => {
      // console.log(node)
      if (!node.data.children || node.data.children.length === 0) {
        delete node.data['children'];
        node.data.leaf = true;
        // node.isLeaf = true
      }
      // console.log(node.data)
      return resolve([]);
    };
    // 鼠标hover到树节点
    const mouseover = () => {
      datas.showIcon = true;
    };
    const mouseleave = () => {
      datas.showIcon = false;
    };
    const handleChangeLy = val => {
      if (val) {
        datas.formData.circulationMode = '1';
      } else {
        datas.formData.circulationMode = '';
      }
    };
    // 新增树节点
    const addTreeItem = () => {
      datas.showEditDialog = true;
      datas.dialogFrom = {
        name: '',
        parentId: ''
      };
      datas.isAddTree = true;
      nextTick(() => {
        datas.inputRefDialog.focus();
      });
    };
    // 树节点编辑
    const editTree = (data, node) => {
      datas.dialogTreeData = formatAllTree(data.id, datas.dialogTreeData);
      datas.showEditDialog = true;
      nextTick(() => {
        datas.inputRefDialog.focus();
      });
      datas.isAddTree = false;
      datas.dialogFrom = JSON.parse(JSON.stringify(data));
    };
    // 保存树节点
    const formTree = ref(null);
    const editDialogSuccess = () => {
      formTree.value.validate(valid => {
        if (valid) {
          if (datas.isAddTree !== true) {
            // TODO: 更新状态管理的materiallist
            updateMaterialcategory(datas.dialogFrom).then(function (res) {
              if (res !== false && res.data.code === 200) {
                bus.$emit('reloadMaterialTree');
                ElMessage.success('编辑成功!');
                datas.showEditDialog = false;
              }
            });
          } else {
            // TODO: 更新状态管理的materiallist
            saveMaterialcategory(datas.dialogFrom).then(function (res) {
              if (res !== false && res.data.code === 200) {
                bus.$emit('reloadMaterialTree');
                ElMessage.success('新增成功!');
                datas.showEditDialog = false;
              }
            });
          }
        } else {
          return false;
        }
      });
    };
    // 树节点删除
    const delTree = node => {
      var ids = [];
      ids.push(node.id);
      ElMessageBox({
        title: '提示',
        message: '是否删除该类目?',
        confirmButtonText: '确认删除',
        cancelButtonText: '取消',
        showCancelButton: true,
        closeOnClickModal: false,
        type: 'warning'
      })
        .then(() => {
          deleteMaterialcategory(ids).then(function (res) {
            if (res !== false && res.data.code === 200) {
              if (res.data.data.isSuccess) {
                bus.$emit('reloadMaterialTree');
                ElMessage.success('删除成功!');
              } else {
                ElMessage.error(res.data.data.message);
              }
            }
          });
        })
        .catch(() => {});
    };
    // 停止使用
    const handleDisuse = row => {
      ElMessageBox({
        title: '提示',
        message: '是否确认停止使用?',
        confirmButtonText: '确认停用',
        cancelButtonText: '取消',
        showCancelButton: true,
        closeOnClickModal: false,
        type: 'warning'
      })
        .then(() => {
          // 停止使用
          updateProductModel({ ...row, status: 1 }).then(res => {
            if (res !== false && res.data.code === 200) {
              proxy.getList();
              ElMessage.success('停用成功');
            }
          });
        })
        .catch(() => {});
    };
    // 启用试样分组
    const handleEnable = row => {
      ElMessageBox({
        title: '提示',
        message: '是否确认启用?',
        confirmButtonText: '确认启用',
        cancelButtonText: '取消',
        showCancelButton: true,
        closeOnClickModal: false,
        type: 'warning'
      })
        .then(() => {
          // 启用
          // 停止使用
          updateProductModel({ ...row, status: 0 }).then(res => {
            if (res !== false && res.data.code === 200) {
              proxy.getList();
              ElMessage.success('启用成功');
            }
          });
        })
        .catch(() => {});
    };
    // 编辑
    const edit = () => {
      datas.showEdit = false;
      datas.isDisable = false;
    };
    // 保存编辑
    const saveEdit = () => {
      datas.formData.categoryId = datas.currentData.id;
      updateMaterialcategoryDetailInfo(datas.formData).then(function (res) {
        if (res !== false && res.data.code === 200) {
          ElMessage.success(res.data.message);
          datas.showEdit = true;
          datas.isDisable = true;
        }
      });
    };
    // 取消编辑
    const cancelEdit = () => {
      datas.showEdit = true;
      datas.isDisable = true;
    };
    // 试样分组新增/编辑
    const handleAddEditGroup = row => {
      datas.dialogGroup = true;
      nextTick(() => {
        datas.inputRef.focus();
      });
      if (row?.id) {
        // 编辑
        datas.formdataGroup = JSON.parse(JSON.stringify(row));
        datas.isAddGroup = false;
      } else {
        // 新增
        datas.formdataGroup = {
          name: '',
          coreKey: `group${Number(datas.tableGroup.length + 1)}`,
          categoryId: '',
          categoryCode: '',
          status: 0
        };
        datas.isAddGroup = true;
      }
    };
    const onDialogGroupClose = () => {
      datas.groupRef.resetFields();
    };
    // 试样分组-新增框-取消
    const cancelGroup = () => {
      datas.dialogGroup = false;
    };
    // 试样分组-新增/编辑框-确定
    const onSubmitGroup = flag => {
      datas.groupRef.validate(valid => {
        if (valid) {
          datas.dialogGroup = false;
          datas.formdataGroup.categoryId = datas.currentData.id;
          datas.formdataGroup.categoryCode = datas.currentData.code;
          if (datas.isAddGroup === true) {
            saveProductModel(datas.formdataGroup).then(res => {
              if (res !== false && res.data.code === 200) {
                proxy.getList();
                ElMessage.success('新增成功');
              }
            });
          } else {
            updateProductModel(datas.formdataGroup).then(res => {
              if (res !== false && res.data.code === 200) {
                proxy.getList();
                ElMessage.success('编辑成功');
              }
            });
          }
        }
      });
    };
    // 查询产品型号
    const searchPM = () => {
      datas.listQuery.productModel = datas.filterPMText;
      proxy.getList();
    };

    // 取消树节点编辑
    const cancel = () => {
      datas.showEditDialog = false;
    };

    const changeIcon = (command, node) => {
      if (!command) {
        node.showIcon = true;
      } else {
        node.showIcon = !node.showIcon;
      }
    };
    const tabChange = tabsPaneContext => {
      if (tabsPaneContext.props.name === 'second') {
        datas.xhSearchRef.focus();
      }
    };

    return {
      ...toRefs(datas),
      initActiveName,
      tabChange,
      handleChangeLy,
      getPermissionBtn,
      searchPM,
      filterNode,
      addTreeItem,
      edit,
      saveEdit,
      handleAddEditGroup,
      handleDisuse,
      handleEnable,
      editDialogSuccess,
      editTree,
      delTree,
      tree,
      formTree,
      cancelEdit,
      cancel,
      mouseover,
      mouseleave,
      loadNode,
      changeIcon,
      drageHeader,
      onDialogGroupClose,
      cancelGroup,
      onSubmitGroup,
      colWidth
    };
  },
  created() {
    this.getMaterialTree(0);
    this.bus.$on('reloadMaterialTree', msg => {
      this.getMaterialTree(0);
    });
  },
  methods: {
    getMaterialTree(typeId) {
      const vm = this;
      getMaterialcategoryTree(typeId).then(function (res) {
        vm.treeData = formatTree(res.data.data);
        vm.dialogTreeData = formatTree(res.data.data);
        if (vm.treeData.length > 0) {
          vm.currentData = vm.treeData[0];
          // 选中默认树节点
          vm.currentNodeKey = vm.treeData[0].id;
          vm.$nextTick(function () {
            vm.$refs.tree.setCurrentKey(vm.currentNodeKey);
          });
          vm.clickNode(vm.currentData);
        }
      });
    },
    clickNode(data, node) {
      const vm = this;
      vm.currentData = data;
      vm.showEdit = true;
      vm.isDisable = true;
      if (data.parentId !== '0') {
        materialCategorydetailInfo(data.parentId).then(function (res) {
          vm.currentParentForm = res.data.data;
          if (vm.currentParentForm === null) {
            vm.currentParentForm = {
              categoryId: 0,
              detectionCycle: 0,
              circulationMode: '',
              isRetention: '',
              isReportSeal: 0,
              isSamplePreparation: ''
            };
          }
        });
      }
      materialCategorydetailInfo(data.id).then(function (res) {
        // console.log(res.data.data)
        if (res !== false && res.data.code === 200) {
          if (res.data.data) {
            vm.formData = res.data.data;
            vm.currentFormData = res.data.data;
          } else {
            vm.formData = {
              categoryId: 0,
              detectionCycle: 0,
              circulationMode: '',
              isRetention: '',
              isReportSeal: 0,
              isSamplePreparation: '',
              isReceiveVerification: 0,
              isWarehousingVerification: 0
            };
          }
        }
      });
      vm.getList();
    },
    getList(value) {
      var vm = this;
      vm.listQuery.categoryId = vm.currentData.id;
      if (value) {
        vm.listQuery.page = value.page;
        vm.listQuery.limit = value.limit;
      }
      const param = JSON.parse(JSON.stringify(vm.listQuery));
      param.page = param.page + '';
      param.limit = param.limit + '';
      getProductModelList(param).then(res => {
        if (res !== false && res.data.code === 200) {
          vm.tableGroup = res.data.data.list;
          vm.total = res.data.data.totalCount;
        } else {
          ElMessage.error(res.data.message);
        }
      });
    }
  }
};
</script>
<style lang="scss" scoped>
@import '@/styles/tree.scss';

// 左侧区域高度
.tree-container .tree-content {
  height: calc(100vh - 175px);
}

.tab-box {
  .header-btn-group {
    display: flex;
    margin-bottom: 20px;
  }
  .header-search-group {
    display: flex;
    flex-wrap: nowrap;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    .search-bar {
      display: flex;
      justify-content: flex-start;
      align-items: center;
    }
  }
  :deep(.el-tabs__header) {
    margin-bottom: 20px;
  }
}
</style>
