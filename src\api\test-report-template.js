import request from '@/utils/request';

/**
 * 获取检测项目检测报告模板信息
 * @param {string} capabilityId
 */
export function capabilityreporttemplate(capabilityId) {
  return request({
    url: `/api-capabilitystd/capability/capabilityreporttemplate/findByCapabilityId/${capabilityId}`,
    method: 'get'
  });
}
/**
 * 保存检测项目检测报告模板信息
 */
export function reporttemplateSave(data) {
  return request({
    url: `/api-capabilitystd/capability/capabilityreporttemplate/saveOrUpdate`,
    method: 'post',
    data
  });
}
