<template>
  <!-- 设备使用记录 -->
  <ListLayout :has-quick-query="false">
    <template #search-bar>
      <el-form ref="editFrom" :inline="true" :model="formInline" class="page-searchbar" @submit.prevent>
        <el-form-item prop="code">
          <el-input
            v-model="formInline.code"
            v-trim
            v-focus
            placeholder="请输入编号"
            class="ipt-360"
            prefix-icon="el-icon-search"
            size="large"
            clearable
            @keyup.enter="onSubmit"
          />
        </el-form-item>
        <el-form-item style="margin-left: 0">
          <el-button type="primary" size="large" @click="onSubmit">查询</el-button>
          <el-button size="large" @click="reset">重置</el-button>
          <el-button class="searchBtn" size="large" type="text" @click="search" @keyup.prevent @keydown.enter.prevent
            >高级搜索<i class="el-icon--right" :class="[showS ? 'el-icon-arrow-up' : 'el-icon-arrow-down']" />
          </el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #button-group>
      <el-button class="fr" type="primary" size="large" :loading="expLoading" @click="exportExcel()"
        ><span class="iconfont tes-task-issued" @keyup.prevent @keydown.enter.prevent /> 导出</el-button
      >
    </template>
    <template #search-panel>
      <el-collapse v-model="activeName" class="search-collapse">
        <el-collapse-item name="1">
          <el-form ref="form" size="small" :model="searchForm" label-width="110px" label-position="right">
            <el-form-item label="使用日期：">
              <el-date-picker
                v-model="searchForm.rukuDateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                size="small"
                :shortcuts="shortcuts"
                @change="changeRuku"
              />
            </el-form-item>
          </el-form>
        </el-collapse-item>
      </el-collapse>
    </template>
    <el-table
      ref="tableRef"
      :key="tableKey"
      v-loading="listLoading"
      :data="list"
      fit
      border
      height="auto"
      size="medium"
      class="dark-table base-table format-height-table no-quick-query"
      @header-dragend="drageHeader"
      @sort-change="sortChange"
    >
      <el-table-column label="仪器设备编号" prop="deviceNumber" :width="colWidth.orderNo" show-overflow-tooltip>
        <template #default="{ row }">
          <div v-copy="row.deviceNumber" class="blue-color" @click="handleDeviceDetail(row)">
            {{ row.deviceNumber || '--' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="样品编号" prop="secSampleNum" :width="colWidth.orderNo" sortable show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.secSampleNum || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="委托编号"
        prop="presentationCode"
        :width="colWidth.orderNo"
        sortable
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <span>{{ row.presentationCode || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="使用日期" prop="deviceUseTime" :width="colWidth.date">
        <template #default="{ row }">
          <span>{{ formatDate(row.deviceUseTime) || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="检测项目" prop="capabilityName" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.capabilityName || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="使用前状态" prop="deviceStatusBefore" :width="colWidth.status">
        <template #default="{ row }">
          <el-tag size="small" effect="dark" :type="row.deviceStatusBefore === '1' ? 'success' : 'info'">{{
            row.deviceStatusBefore === '0' ? '异常' : row.deviceStatusBefore === '1' ? '正常' : '停用'
          }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="使用后状态" prop="deviceStatusAfter" :width="colWidth.status">
        <template #default="{ row }">
          <el-tag size="small" effect="dark" :type="row.deviceStatusAfter === '1' ? 'success' : 'info'">{{
            row.deviceStatusAfter === '0' ? '异常' : row.deviceStatusAfter === '1' ? '正常' : '停用'
          }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="使用人" prop="user" :width="colWidth.person">
        <template #default="{ row }">
          <UserTag :name="getNameByid(row.ownerId) || row.ownerId || '--'" />
        </template>
      </el-table-column>
    </el-table>
    <Detail
      :drawer="detailDrawer"
      title="仪器设备详情"
      :dictionary-all="dictionaryJSON"
      :tree-data="treeData"
      :device-id="deviceId"
      @close="closeDeatilDrawer"
    />
    <pagination
      v-show="total > 0"
      :page="listQuery.page"
      :limit="listQuery.limit"
      :total="total"
      @pagination="getList"
    />
  </ListLayout>
</template>

<script>
import { reactive, ref, toRefs, getCurrentInstance } from 'vue';
// import router from '@/router/index.js'
import Pagination from '@/components/Pagination';
import { getDeviceRecordList, getDeviceRecordPage, getDetail } from '@/api/equipment';
import Detail from '../equipment-management/instruments-equipment/detail.vue';
import { ElMessage } from 'element-plus';
import { getNameByid, getPermissionBtn } from '@/utils/common';
import { getTree } from '@/api/equipment';
import { formatDate } from '@/utils/formatTime';
import ListLayout from '@/components/ListLayout';
import { drageHeader } from '@/utils/formatTable';
import { getLoginInfo } from '@/utils/auth';
import UserTag from '@/components/UserTag';
import { checkPermissionList } from '@/api/permission';
import { getDictionary } from '@/api/user';
// import { permissionTypeList } from '@/utils/permissionList'
import { parseTime } from '@/utils';
import { colWidth } from '@/data/tableStyle';

export default {
  name: 'DeviceRecord',
  components: { Pagination, ListLayout, UserTag, Detail },
  setup() {
    const { proxy } = getCurrentInstance();
    // const store = useStore().state
    const editFrom = ref(null);
    const otherForm = reactive({
      accountId: getLoginInfo().accountId,
      showS: false,
      activeName: '0',
      detailDrawer: false, // 仪器设备编号抽屉
      treeData: [], // 仪器设备所属分类树
      deviceId: '', // 选中的设备id
      statusJson: {}, // 仪器设备维修记录状态
      mangeList: [],
      formInline: {
        code: '',
        startTime: '',
        endTime: ''
      },
      searchForm: {
        rukuDateRange: ''
      },
      list: [],
      allList: [],
      content: '',
      listQuery: {
        page: 1,
        limit: 20,
        orderBy: '',
        isAsc: null
      },
      tableKey: 0,
      listLoading: false,
      total: 0,
      dictionaryJSON: {
        cfdd: {
          enable: {},
          all: {}
        },
        SBFJLX: {
          enable: {},
          all: {}
        },
        24: {
          enable: {},
          all: {}
        }
      },
      shortcuts: [
        {
          text: '近三天',
          value: (() => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 3);
            return [start, end];
          })()
        },
        {
          text: '最近一周',
          value: (() => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            return [start, end];
          })()
        },
        {
          text: '最近一个月',
          value: (() => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            return [start, end];
          })()
        }
      ],
      expLoading: false
    });
    const getDictionaryAll = async () => {
      Object.keys(otherForm.dictionaryJSON).forEach(async item => {
        const response = await getDictionary(item);
        if (response) {
          otherForm.dictionaryJSON[item] = {
            enable: {},
            all: {}
          };
          response.data.data.dictionaryoption.forEach(valOption => {
            if (valOption.status === 1) {
              otherForm.dictionaryJSON[item].enable[valOption.code] = valOption.name;
            }
            otherForm.dictionaryJSON[item].all[valOption.code] = valOption.name;
          });
        }
      });
    };
    getDictionaryAll();
    function onSubmit() {
      proxy.getList();
    }

    function reset() {
      editFrom.value.resetFields();
      otherForm.formInline = {
        code: '',
        startTime: '',
        endTime: ''
      };
      otherForm.searchForm = {
        prodType: '',
        rukuDateRange: ''
      };
      otherForm.listQuery = {
        page: 1,
        limit: 20,
        orderBy: '',
        isAsc: null
      };
      proxy.getList();
    }

    const search = () => {
      otherForm.showS = !otherForm.showS;
      if (otherForm.activeName === '0') {
        otherForm.activeName = '1';
      } else {
        otherForm.activeName = '0';
      }
    };
    // // 从字典获取仪器设备状态
    // const getDictionaryStatus = () => {
    //   getDictionary(24).then(res => {
    //     if (res) {
    //       const data = res.data.data.dictionaryoption;
    //       otherForm.equipList[0].group = [];
    //       otherForm.equipList[1].group = [];
    //       data.forEach(item => {
    //         if (item.status === 1) {
    //           otherForm.equipList[0].group.push(item);
    //         } else {
    //           otherForm.equipList[1].group.push(item);
    //         }
    //         // 仪器设备所有状态
    //         otherForm.equipUnitJson[item.code] = item.name;
    //         if (item.code !== 'Standby' && item.code !== 'Maintenance') {
    //           // 用在维修模块的状态
    //           otherForm.statusJson[item.code] = item.name;
    //         }
    //       });
    //     }
    //   });
    // };
    // getDictionaryStatus();
    // 获取仪器设备所有的所属分类
    const getAllTree = () => {
      getTree().then(res => {
        otherForm.treeData = res.data.data;
      });
    };
    getAllTree();
    const sortChange = data => {
      const { prop, order } = data;
      otherForm.listQuery.orderBy = prop;
      if (order === 'ascending') {
        otherForm.listQuery.isAsc = true;
      } else if (order === 'descending') {
        otherForm.listQuery.isAsc = false;
      } else {
        otherForm.listQuery.isAsc = null;
      }
    };

    // 点击仪器编号跳转到仪器详情页面 deviceId
    const handleDeviceDetail = row => {
      // 查询详情
      getDetail(row.deviceId).then(res => {
        if (res) {
          if (res.data.data.device.id) {
            otherForm.detailDrawer = true;
            otherForm.deviceId = row.deviceId;
          } else {
            ElMessage.warning('当前设备已被删除！');
          }
        }
      });
    };

    // 高级搜索-入库日期-change
    const changeRuku = date => {
      otherForm.formInline.startTime = date ? formatDate(date[0]) : '';
      otherForm.formInline.endTime = date ? formatDate(date[1]) : '';
    };
    // 导出
    const exportExcel = () => {
      proxy.export2Excel();
      ElMessage.success('导出成功！');
    };
    // 关闭仪器设备弹出窗
    const closeDeatilDrawer = val => {
      otherForm.detailDrawer = false;
      proxy.getList();
    };

    return {
      getPermissionBtn,
      getAllTree,
      closeDeatilDrawer,
      drageHeader,
      handleDeviceDetail,
      formatDate,
      changeRuku,
      getNameByid,
      sortChange,
      editFrom,
      ...toRefs(otherForm),
      search,
      onSubmit,
      reset,
      exportExcel,
      colWidth
    };
  },
  created() {
    this.getList();
    // 刷新列表
    this.bus.$on('reloadDeviceRecordList', msg => {
      this.getList();
    });
  },
  methods: {
    getList(data) {
      const _this = this;
      _this.listLoading = true;
      if (data && data !== undefined) {
        _this.listQuery.page = data.page;
        _this.listQuery.limit = data.limit;
      }
      const param = Object.assign(_this.formInline, _this.listQuery);
      param.page = param.page + '';
      param.limit = param.limit + '';
      getDeviceRecordPage(param).then(res => {
        if (res !== false) {
          const { data } = res.data;
          _this.list = data.list;
          _this.total = data.totalCount;

          this.getAllList();
        }
        setTimeout(() => {
          _this.listLoading = false;
        }, 100);
      });
    },
    getAllList() {
      const _this = this;
      const param = Object.assign(_this.formInline, _this.listQuery);
      getDeviceRecordList(param).then(res => {
        if (res !== false) {
          const { data } = res.data;
          _this.allList = data;
          if (_this.allList.length > 0) {
            _this.allList.forEach(list => {
              list.deviceStatusBeforeName =
                list.deviceStatusBefore === '0' ? '异常' : list.deviceStatusBefore === '1' ? '正常' : '停用';
              list.deviceStatusAfterName =
                list.deviceStatusAfter === '0' ? '异常' : list.deviceStatusAfter === '1' ? '正常' : '停用';
              list.ownerName = getNameByid(list.ownerId);
            });
          }
        }
      });
    },
    getnamelist() {
      checkPermissionList('allocation').then(res => {
        this.mangeList = res.data.data;
      });
    },
    export2Excel() {
      var type = 1;
      var tHeader = [];
      var filterVal = [];
      var fileName = '模板';
      var that = this;
      that.expLoading = true;
      if (type === 1) {
        fileName = '设备使用记录';
        tHeader = [
          '仪器设备编号',
          '样品编号',
          '委托编号',
          '使用日期',
          '检测项目',
          '使用前状态',
          '使用后状态',
          '使用人'
        ];
        filterVal = [
          'deviceNumber',
          'secSampleNum',
          'presentationCode',
          'deviceUseTime',
          'capabilityName',
          'deviceStatusBeforeName',
          'deviceStatusAfterName',
          'ownerName'
        ];
      }
      import('@/utils/Export2Excel').then(excel => {
        const data = this.formatJson(filterVal, that.allList);
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: fileName,
          autoWidth: true,
          bookType: 'xlsx'
        });
        that.expLoading = false;
      });
    },
    formatJson(filterVal, jsonData) {
      return jsonData.map(v =>
        filterVal.map(j => {
          if (j === 'timestamp') {
            return parseTime(v[j]);
          } else {
            return v[j];
          }
        })
      );
    }
  }
};
</script>
<style lang="scss" scoped>
.device-record {
  height: inherit;
  overflow: hidden auto;
  padding: 16px 24px;

  .sample-order-form {
    text-align: left;

    .el-form-item {
      margin-bottom: 0px;
    }

    .searchBtn {
      border: 0;
      background: none;
      color: $tes-primary;
      padding: 0;
    }
    .export-data {
      float: right;
      margin: 0;
    }
  }
}
</style>
