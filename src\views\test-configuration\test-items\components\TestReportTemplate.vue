<template>
  <!-- 检测项目-检测依据 -->
  <div class="Specification">
    <div class="header-search-group">
      <div v-loading="listLoading" class="btn-group">
        <el-button
          v-if="!isEdit"
          size="small"
          icon="el-icon-plus"
          type="default"
          @click="handleAdd()"
          @keyup.prevent
          @keydown.enter.prevent
          >新增</el-button
        >
        <el-button
          v-if="formData.tableList.length > 0 && !isEdit"
          size="small"
          type="default"
          icon="el-icon-edit-outline"
          @click="handleEditable()"
          @keyup.prevent
          @keydown.enter.prevent
          >编辑</el-button
        >
        <el-button
          v-if="isEdit"
          size="small"
          icon="el-icon-check"
          type="primary"
          @click="handleSaveEdit()"
          @keyup.prevent
          @keydown.enter.prevent
          >保存</el-button
        >
        <el-button
          v-if="isEdit"
          size="small"
          icon="el-icon-close"
          @click="handleCancelEdit()"
          @keyup.prevent
          @keydown.enter.prevent
          >取消</el-button
        >
      </div>
    </div>
    <el-form ref="ruleFormTable" :model="formData">
      <el-table
        ref="tableRef"
        v-loading="listLoading"
        :data="formData.tableList"
        size="medium"
        fit
        border
        height="auto"
        highlight-current-row
        class="dark-table base-table"
        @header-dragend="drageHeader"
      >
        <el-table-column prop="name" label="模板名称" :min-width="colWidth.name">
          <template #default="{ row, $index }">
            <el-form-item
              v-if="isEdit"
              :prop="`tableList.${$index}.name`"
              :rules="{ required: true, message: '请输入模板名称', trigger: 'change' }"
              style="margin: 0px"
            >
              <el-input
                v-model="row.name"
                placeholder="请输入模板名称"
                maxlength="100"
                @keyup.prevent
                @keydown.enter.prevent
              />
            </el-form-item>
            <span v-else> {{ row.name || '--' }} </span>
          </template>
        </el-table-column>
        <el-table-column
          label="应用范围"
          prop="types"
          show-overflow-tooltip
          align="left"
          :min-width="colWidth.typeGroup"
        >
          <template #default="{ row, $index }">
            <el-form-item v-if="isEdit" :prop="`tableList.${$index}.types`" style="margin: 0px">
              <el-select
                v-model="row.types"
                placeholder="请选择应用范围"
                multiple
                style="width: 100%"
                @change="
                  val => {
                    return handleRowType(val, $index);
                  }
                "
              >
                <el-option v-for="(val, key) in typeOptions" :key="key" :label="val" :value="key" />
              </el-select>
            </el-form-item>
            <span v-else>{{ filterTypes(row.type) || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="languageType" label="报告语言" :min-width="colWidth.typeGroup">
          <template #default="{ row, $index }">
            <el-form-item
              v-if="isEdit"
              :prop="`tableList.${$index}.languageType`"
              :rules="{ required: true, message: '请选择报告语言', trigger: 'change' }"
              style="margin: 0px"
            >
              <el-select v-model="row.languageType" placeholder="请选择报告语言" style="width: 100%">
                <el-option v-for="(val, key) in languageTypeJson" :key="key" :label="val" :value="Number(key)" />
              </el-select>
            </el-form-item>
            <span v-else> {{ languageTypeJson[row.languageType] || '--' }} </span>
          </template>
        </el-table-column>
        <el-table-column prop="length" label="描述" :min-width="colWidth.remark">
          <template #default="{ row, $index }">
            <el-form-item v-if="isEdit" :prop="`tableList.${$index}.description`" style="margin: 0px">
              <el-input
                v-model="row.description"
                placeholder="请输入描述"
                maxlength="300"
                @keyup.prevent
                @keydown.enter.prevent
              />
            </el-form-item>
            <span v-else> {{ row.description || '--' }} </span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" :width="colWidth.status">
          <template #default="{ row }">
            <el-tag :type="row.status === 1 ? 'success' : 'info'" effect="dark">{{
              row.status === 1 ? '已启用' : '已停用'
            }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="附件" :min-width="colWidth.fileName" show-overflow-tooltip>
          <template #default="{ row }">
            <span class="blue-color" @click="downLoadFile(row)">{{ row.fileRemoteName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" :width="colWidth.operation">
          <template #default="{ row, $index }">
            <span class="blue-color" @click="handleChangeStatus(row, $index)">{{
              row.status === 1 ? '停用' : '启用'
            }}</span>
          </template>
        </el-table-column>
      </el-table>
    </el-form>
    <!-- 上传检测项目报告模板 -->
    <el-dialog
      v-model="dialogUpload"
      title="报告模板上传"
      width="500px"
      :close-on-click-modal="false"
      custom-class="reportTemplate-Dialog"
    >
      <el-form v-if="dialogUpload" ref="ruleForm" :model="formDataUpdate" label-position="right" label-width="85px">
        <el-form-item
          label="模板名称："
          prop="name"
          :rules="{ required: true, message: '请输入模板名称', trigger: 'change' }"
        >
          <el-input ref="uploadInputRef" v-model="formDataUpdate.name" placeholder="请输入模板名称" />
        </el-form-item>
        <el-form-item label="报告语言：" prop="type">
          <el-select v-model="formDataUpdate.languageType" placeholder="请选择报告语言" style="width: 100%">
            <el-option v-for="(val, key) in languageTypeJson" :key="key" :label="val" :value="Number(key)" />
          </el-select>
        </el-form-item>
        <el-form-item label="应用范围：" prop="type">
          <el-select
            v-model="formDataUpdate.types"
            placeholder="请选择应用范围"
            multiple
            style="width: 100%"
            @change="changeType"
          >
            <el-option v-for="(val, key) in typeOptions" :key="key" :label="val" :value="key" />
          </el-select>
        </el-form-item>
        <el-form-item label="描述：" prop="description">
          <el-input v-model="formDataUpdate.description" type="textarea" :rows="3" placeholder="请输入模板描述" />
        </el-form-item>
        <el-form-item label="上传模板：" prop="fileId">
          <el-upload
            ref="uploadRef"
            class="upload-demo"
            :headers="headerconfig"
            :accept="'.docx'"
            :action="uploadAction"
            :on-success="handleFileSuccess"
            :before-upload="beforeUpload"
            :auto-upload="false"
            :limit="1"
            :on-exceed="handleExceed"
            :file-list="fileList"
          >
            <el-button size="mini" icon="el-icon-upload2" type="primary" @keyup.prevent @keydown.enter.prevent
              >选择文件</el-button
            >
            <template #tip>
              <div class="el-upload__tip">单文件大小不超过20M，仅支持.docx文件扩展名</div>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogUpload = false">取 消</el-button>
          <el-button type="primary" @click="onSubmit">确 认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { getCurrentInstance, nextTick, reactive, ref, toRefs, watch } from 'vue';
import { formatDate } from '@/utils/formatTime';
import { getNamesByid, getNameByid, getPermissionBtn } from '@/utils/common';
import { drageHeader } from '@/utils/formatTable';
import { colWidth } from '@/data/tableStyle';
import { getColWidth } from '@/utils/func/customTable';
import { getToken } from '@/utils/auth';
import { capabilityreporttemplate, reporttemplateSave } from '@/api/test-report-template';
import { getAttachmentById } from '@/api/testItem';
import { getDictionary } from '@/api/user';
import { capabilityAttachmentUploadUrl } from '@/api/uploadAction';

export default {
  name: 'DetectionBasis',
  components: {},
  props: {
    capabilityId: {
      type: String,
      default: ''
    },
    activeName: {
      type: String,
      default: ''
    }
  },
  setup(props) {
    const { proxy } = getCurrentInstance();
    const state = reactive({
      tableRef: ref(),
      dialogLoading: false,
      dialogUpload: false, // 数据导入弹出框
      detailItemId: '', // 项目id
      isEdit: false, // 是否是编辑状态 true 是
      languageTypeJson: {
        1: '中文报告',
        2: '英文报告',
        3: '中英文报告'
      },
      ruleForm: ref(),
      ruleFormTable: ref(),
      uploadInputRef: ref(),
      typeOptions: {},
      listLoading: false,
      formData: {
        tableList: []
      },
      fileList: [],
      formDataUpdate: {
        types: []
      },
      uploadAction: capabilityAttachmentUploadUrl(), // 附件上传地址
      headerconfig: {
        Authorization: getToken()
      },
      uploadRef: ref(),
      capabilityId: ''
    });

    watch(props, newValue => {
      if (newValue.activeName === '10') {
        state.detailItemId = props.capabilityId;
        state.dialogLoading = false;
        state.isEdit = false;
        getTypeDictionary();
        getLanguageType();
        getList();
      }
    });
    // 修改停用启用
    const handleChangeStatus = (row, index) => {
      const params = JSON.parse(JSON.stringify(state.formData.tableList));
      params[index].status = params[index].status === 1 ? 2 : 1;
      onSubmitApi(params);
    };
    // 字典---报告语言
    const getLanguageType = () => {
      getDictionary('BGYY').then(response => {
        if (response !== false) {
          const { data } = response;
          state.languageTypeJson = {};
          data.data.dictionaryoption.forEach(val => {
            if (val.status === 1) {
              state.languageTypeJson[val.code] = val.name;
            }
          });
        }
      });
    };
    // 字典---应用范围列表
    const getTypeDictionary = () => {
      getDictionary('JYLX').then(response => {
        if (response !== false) {
          const { data } = response;
          state.typeOptions = {};
          data.data.dictionaryoption.forEach(val => {
            if (val.status === 1) {
              state.typeOptions[val.code] = val.name;
            }
          });
        }
      });
    };
    // 下载附件
    const downLoadFile = row => {
      getAttachmentById(row.id).then(res => {
        if (res.data.code === 200) {
          window.open(res.data.data[0].url);
        }
      });
    };
    // 获取检测报告模板列表
    const getList = () => {
      state.listLoading = true;
      capabilityreporttemplate(state.detailItemId).then(res => {
        state.listLoading = false;
        if (res) {
          state.formData.tableList = res.data.data;
        }
      });
    };

    const handleExceed = files => {
      state.uploadRef.clearFiles();
      state.uploadRef.handleStart(files[0]);
    };
    // 保存编辑
    const handleSaveEdit = () => {
      state.ruleFormTable
        .validate()
        .then(valid => {
          if (valid) {
            onSubmitApi(state.formData.tableList);
          } else {
            return false;
          }
        })
        .catch(error => {
          const fieldName = Object.keys(error)[0];
          proxy.$message.warning(error[fieldName][0].message);
          return false;
        });
    };

    // 上传文件的限制
    const beforeUpload = file => {
      var fileName = '';
      if (file.name) {
        fileName = file.name.substring(file.name.lastIndexOf('.') + 1);
      }
      const fileSize = file.size / 1024 / 1024 < 10;
      if (!fileSize) {
        proxy.$message.error('上传附件大小不能超过10M');
        return false;
      } else if (fileName !== 'docx') {
        proxy.$message.error('仅支持.docx文件扩展名');
        return false;
      } else if (file.size === 0) {
        proxy.$message.error('上传附件大小不能为空');
        return false;
      } else {
        return true;
      }
    };

    // 上传成功的钩子
    const handleFileSuccess = (res, file) => {
      if (res.code === 200) {
        const { fileId, fileRemoteName } = res.data;
        state.formDataUpdate = { ...state.formDataUpdate, fileId, fileRemoteName, capabilityId: state.detailItemId };
        onSubmitApi(state.formDataUpdate, 'add');
      } else {
        proxy.$message.error(res.message);
      }
    };
    const onSubmitApi = (params, type) => {
      const paramsApi = type === 'add' ? [params] : params;
      reporttemplateSave({ entityList: paramsApi }).then(res => {
        if (res) {
          proxy.$message.success(res.data.message);
          state.listLoading = false;
          state.dialogUpload = false;
          state.isEdit = false;
          getList();
        }
      });
    };

    // 新增
    const handleAdd = () => {
      state.dialogUpload = true;
      state.formDataUpdate = {
        status: 1,
        languageType: 1,
        types: []
      };
      nextTick(() => {
        state.uploadInputRef.focus();
      });
    };

    // 编辑表格
    const handleEditable = () => {
      state.isEdit = true;
      state.formData.tableList.forEach((item, index) => {
        if (item.type) {
          item.types = item.type.split(',');
        } else {
          item.types = [];
        }
      });
    };

    // 保存修改
    const onSubmit = () => {
      state.ruleForm
        .validate()
        .then(valid => {
          if (valid) {
            if (state.uploadRef.uploadFiles.length) {
              state.uploadRef.submit();
            } else {
              proxy.$message.warning('请上传附件');
            }
          } else {
            return false;
          }
        })
        .catch(error => {
          const fieldName = Object.keys(error)[0];
          proxy.$message.warning(error[fieldName][0].message);
          return false;
        });
    };

    // 取消修改
    const handleCancelEdit = () => {
      state.isEdit = false;
      getList();
    };

    // 删除新增的数据
    const handleDeleteRow = index => {
      state.formData.tableList.splice(index, 1);
    };

    // 设置编辑和新增的颜色
    const tableRowClassName = ({ row }) => {
      if (row.updateDetermine || row.updateKeyValue) {
        return 'updated-row';
      } else {
        return 'new-row';
      }
    };

    // 应用范围选择
    const changeType = types => {
      console.log(types);
      state.formDataUpdate.type = types.join(',');
    };
    // 列表过滤应用范围
    const filterTypes = type => {
      var rowType = '';
      type.split(',').forEach((item, index) => {
        if (item) {
          if (state.typeOptions[item]) {
            rowType += `${state.typeOptions[item]}，`;
          } else {
            rowType += `${item}，`;
          }
        }
      });
      return rowType;
    };
    // 切换应用范围
    const handleRowType = (val, index) => {
      state.formData.tableList[index].type = val.toString();
    };

    return {
      ...toRefs(state),
      handleAdd,
      handleRowType,
      filterTypes,
      changeType,
      getLanguageType,
      getTypeDictionary,
      handleSaveEdit,
      downLoadFile,
      handleChangeStatus,
      handleDeleteRow,
      handleExceed,
      handleFileSuccess,
      beforeUpload,
      drageHeader,
      getNameByid,
      getNamesByid,
      getPermissionBtn,
      formatDate,
      colWidth,
      handleEditable,
      handleCancelEdit,
      onSubmit,
      getColWidth,
      tableRowClassName
    };
  }
};
</script>
<style lang="scss" scoped>
:deep(.el-upload__tip) {
  display: inline;
  margin-left: 5px;
}
.header-search-group {
  display: flex;
  flex-wrap: nowrap;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  .btn-group {
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
}

.base-table.dark-table :deep(.el-table__body-wrapper) {
  max-height: 100% !important;
}
</style>
