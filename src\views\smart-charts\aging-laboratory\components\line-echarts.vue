<template>
  <!-- 老化箱状态 -->
  <div class="sample-list flex flex-col overflow-auto">
    <div class="item-title">老化箱状态</div>
    <div class="auto-scroll-table-container flex-1 overflow-y-auto flex flex-col">
      <el-row class="table-th">
        <el-col :span="3"> 设备编号 </el-col>
        <el-col :span="3"> 设备名称 </el-col>
        <el-col :span="3"> 状态 </el-col>
        <el-col :span="3"> 样品信息 </el-col>
        <el-col :span="3"> 实时温度 </el-col>
        <el-col :span="3"> 开始时间 </el-col>
        <el-col :span="3"> 结束时间 </el-col>
        <el-col :span="3"> 放样人 </el-col>
      </el-row>
      <div class="flex-1 overflow-y-auto table-content flex flex-col gap-2">
        <el-row v-for="item in tableData" :key="item.id">
          <el-col :span="3"> {{ item.deviceNumber }} </el-col>
          <el-col :span="3"> {{ item.deivceName }} </el-col>
          <el-col :span="3"> {{ item.status }} </el-col>
          <el-col :span="3"> 样品信息 </el-col>
          <el-col :span="3"> 实时温度 </el-col>
          <el-col :span="3"> 开始时间 </el-col>
          <el-col :span="3"> 结束时间 </el-col>
          <el-col :span="3"> 放样人 </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onBeforeUnmount } from 'vue';
import { samplesList } from '@/api/order';

export default {
  setup() {
    const tableRef = ref(null);
    const tableData = ref([]);
    const loading = ref(false);
    const currentPage = ref(1);
    const total = ref(0);
    const pageSize = 20;
    const autoScrollInterval = ref(null);

    // 获取数据
    const fetchData = async () => {
      if (loading.value) return;

      loading.value = true;
      try {
        // 替换为你的实际API调用
        const response = await samplesList({
          status: '0',
          page: currentPage.value,
          limit: pageSize
        });

        if (currentPage.value === 1) {
          tableData.value = response.data.data.list;
        } else {
          tableData.value = [...tableData.value, ...response.data.data.list];
        }

        total.value = response.total;
        currentPage.value++;
      } finally {
        loading.value = false;
      }
    };

    // 滚动事件处理
    const handleScroll = ({ scrollTop, scrollLeft, scrollHeight, clientHeight }) => {
      // 检查是否滚动到底部
      const isBottom = scrollTop + clientHeight >= scrollHeight - 10;

      if (isBottom && !loading.value && tableData.value.length < total.value) {
        fetchData();
      }
    };

    // 自动滚动功能
    const startAutoScroll = () => {
      if (autoScrollInterval.value) clearInterval(autoScrollInterval.value);

      autoScrollInterval.value = setInterval(() => {
        if (tableRef.value && tableRef.value.scroll) {
          const scrollWrapper = tableRef.value.scroll;
          const currentScrollTop = scrollWrapper.scrollTop;
          const maxScrollTop = scrollWrapper.scrollHeight - scrollWrapper.clientHeight;

          if (currentScrollTop >= maxScrollTop) {
            // 如果已经滚动到底部，尝试加载更多数据
            if (!loading.value && tableData.value.length < total.value) {
              fetchData();
            }
            // 滚动回顶部
            scrollWrapper.scrollTop = 0;
          } else {
            // 向下滚动
            scrollWrapper.scrollTop = currentScrollTop + 1;
          }
        }
      }, 50); // 调整这个值可以改变滚动速度
    };

    onMounted(() => {
      fetchData().then(() => {
        // 数据加载完成后开始自动滚动
        startAutoScroll();
      });
    });

    onBeforeUnmount(() => {
      if (autoScrollInterval.value) {
        clearInterval(autoScrollInterval.value);
        autoScrollInterval.value = null;
      }
    });

    return {
      tableRef,
      tableData,
      handleScroll
    };
  }
};
</script>

<style lang="scss" scoped>
@import '@/styles/intelligentChart.scss';
.table-content {
  color: #fff;
  text-align: center;
  .el-col {
    line-height: 28px;
  }
  .el-col:nth-child(2n) {
    background-color: #4163bc;
  }
  .el-col:nth-child(2n + 1) {
    background-color: #4a6fd0;
  }
  // box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
}
.table-th {
  color: #fff;
  line-height: 30px;
  font-size: 14px;
}
.item-title {
  background: #4280d7;
  border-radius: 8px 8px 0px 0px;
  font-size: 18px;
  color: $scrollListColor;
  height: 44px;
  line-height: 30px;
  color: $titleColor;
  font-weight: 700;
  padding: 7px 0 7px 29px;
  text-align: left;
}
.auto-scroll-table-container {
  width: 100%;
  height: 100%;
  border: 2px solid #4280d7;
  padding: 3px 10px 10px 10px;
  border-radius: 0 0 4px 4px;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
}
</style>
