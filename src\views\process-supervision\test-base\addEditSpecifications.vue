<template>
  <div class="AddEditSpecifications">
    <!-- 添加规格 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTypeTitle[dialogType]"
      :close-on-click-modal="false"
      width="550px"
      custom-class="submit_dialog"
      @close="cacle()"
    >
      <el-form
        v-if="dialogVisible"
        ref="ruleForm"
        :model="formData"
        label-position="top"
        label-width="120px"
        size="small"
      >
        <el-row :gutter="40">
          <el-col :span="24">
            <el-form-item
              label="标准分类："
              prop="standardCategoryId"
              :rules="{ required: true, message: '请选择标准分类', trigger: 'change' }"
            >
              <el-cascader
                v-model="formData.standardCategoryId"
                :options="standardTree"
                :props="categoryProps"
                placeholder="请选择标准分类"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-row class="productStyle">
              <el-col v-for="(item, index) in specifyList" :key="item.id" :span="11" :offset="index % 2 === 1 ? 2 : 0">
                <el-form-item
                  :label="item.specificationName + '：'"
                  :prop="'productMap.' + item.id"
                  :rules="{
                    required: item.isKeySpecification,
                    message: '请选择' + item.specificationName,
                    trigger: 'change'
                  }"
                >
                  <el-select
                    v-if="item.dictionaryCode"
                    v-model="formData.productMap[item.id]"
                    :placeholder="'请选择' + item.specificationName"
                    clearable
                    filterable
                    size="small"
                    @change="
                      val => {
                        return handleChangeSelect(val, item.id, index);
                      }
                    "
                  >
                    <el-option
                      v-for="(val, key) in dictionaryOptions[item.dictionaryCode]"
                      :key="key"
                      :label="val"
                      :value="val"
                    />
                  </el-select>
                  <el-input
                    v-else
                    v-model.trim="formData.productMap[item.id]"
                    :placeholder="'请输入' + item.specificationName"
                    @input="
                      val => {
                        return handleChangeSelect(val, item.id, index, 'input');
                      }
                    "
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-col>
          <el-col :span="24">
            <el-form-item
              label="产品规格："
              prop="productName"
              :rules="{ required: true, message: '请填写产品规格', trigger: 'change' }"
            >
              <el-input v-model="formData.productName" type="textarea" placeholder="请填写产品规格" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cacle()">取 消</el-button>
          <el-button type="primary" @click="onSubmit">确 认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { reactive, toRefs, ref, watch, getCurrentInstance } from 'vue';
import { formatGgTree } from '@/utils/formatJson';
import { saveProduct, copyProduct, updateProduct } from '@/api/testBase';
import { getDictionary } from '@/api/user';
export default {
  name: 'AddEditSpecifications',
  props: {
    isShow: {
      type: Boolean,
      default: false
    },
    specifyList: {
      type: Array,
      default: function () {
        return [];
      }
    },
    dialogType: {
      type: String,
      default: ''
    },
    detailData: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['closeDialog'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const state = reactive({
      dialogVisible: false,
      dialogTitle: '',
      ruleForm: ref(),
      specifyList: [], // 规格列表
      dictionaryCode: [], // 字典code集合
      dictionaryAll: [], // 字典选项集合
      dictionaryOptions: {}, // 获取字典选项
      productJson: {},
      standardTree: [],
      detailData: {}, // 详情
      dialogType: '', // 弹出框类型
      dialogTypeTitle: {
        edit: '编辑规格',
        add: '新增规格',
        copy: '复制规格'
      },
      formData: {
        productMap: {}
      },
      specifyTitle: {}, // 产品规格
      joinCarAll: {}, // 连接符集合
      categoryProps: {
        expandTrigger: 'hover',
        checkStrictly: true,
        children: 'children',
        label: 'code',
        value: 'id'
      }
    });
    watch(props, newValue => {
      state.dialogVisible = newValue.isShow;
      state.dialogTitle = newValue.dialogTitle;
      state.detailData = props.detailData;
      state.productJson = state.detailData.productJson;
      if (state.dialogVisible) {
        state.dialogType = props.dialogType;
        state.specifyTitle = {};
        // 标准分类树
        state.standardTree = formatGgTree(props.detailData.standardTree);
        // 规格列表
        state.specifyList = props.specifyList;
        state.specifyList.forEach(item => {
          state.joinCarAll[item.id] = item.joinChar;
          state.specifyTitle[item.id] = '';
        });
        if (state.dialogType === 'add') {
          state.formData = {
            standardCategoryId: state.detailData.checkTreeNode.id === 'all' ? '' : state.detailData.checkTreeNode.id,
            materialCategoryCode: state.detailData.materialCode,
            materialCategoryId: state.detailData.materialCategoryId,
            // name: props.detailData.checkTreeNode.name,
            // materialModelId: props.detailData.checkTreeNode.id,
            productMap: {}
          };
        } else {
          state.formData = JSON.parse(JSON.stringify(props.detailData.productDetail));
          for (var key in state.formData.productMap) {
            state.specifyTitle[key] = state.formData.productMap[key];
          }
        }
        state.formData.materialCategoryCode = props.detailData.materialCategoryCode;
        getDictionaryData(state.specifyList);
      }
    });
    // 获取规格列表中所有的字典code
    const getDictionaryData = dataList => {
      state.dictionaryCode = dataList.filter(item => {
        if (item.dictionaryCode) {
          return item.dictionaryCode;
        }
      });
      state.dictionaryCode = [
        ...new Set(
          state.dictionaryCode.map(item => {
            return item.dictionaryCode;
          })
        )
      ];
      getDictionaryOptions(state.dictionaryCode);
    };
    // 获取字典选项
    const getDictionaryOptions = codeList => {
      codeList.forEach(val => {
        // const options = []
        getDictionary(val).then(res => {
          if (res) {
            const dictionary = {};
            res.data.data.dictionaryoption.forEach(item => {
              if (item.status === 1) {
                // 没有被停用的字典选项
                dictionary[item.code] = item.name;
              }
            });
            state.dictionaryOptions[val] = dictionary;
          }
        });
        // state.dictionaryAll.push({ code: val, option: options })
      });
    };
    // 修改参数
    const handleChangeSelect = (val, itemId) => {
      getSpecifications(val, itemId);
    };
    // 获取产品规格
    const getSpecifications = (name, itemId) => {
      state.specifyTitle[itemId] = name;
      const allKeys = [];
      for (var key in state.specifyTitle) {
        if (state.specifyTitle[key]) {
          allKeys.push(key);
        }
      }
      const newObject = {};
      allKeys.forEach((item, index) => {
        if (index !== Number(allKeys.length - 1) && state.joinCarAll[item] && state.specifyTitle[item]) {
          newObject[item] = state.specifyTitle[item] + state.joinCarAll[item];
        } else {
          newObject[item] = state.specifyTitle[item];
        }
      });
      if (allKeys.length > 0) {
        state.formData.productName = Object.values(newObject).join(' ');
      }
    };
    const onSubmit = () => {
      state.ruleForm.validate().then(valid => {
        if (valid) {
          var standardCategoryId = state.formData.standardCategoryId;
          if (typeof state.formData.standardCategoryId !== 'string') {
            if (state.formData.standardCategoryId !== undefined && state.formData.standardCategoryId.length > 0) {
              standardCategoryId =
                state.formData.standardCategoryId[state.formData.standardCategoryId.length - 1].toString();
            } else {
              standardCategoryId = '';
            }
          }
          var params = {
            ...state.formData,
            standardCategoryId: standardCategoryId
          };
          if (state.dialogType === 'add') {
            saveProduct(params).then(res => {
              if (res) {
                proxy.$message.success(res.data.message);
                cacle(true);
              }
            });
          } else if (state.dialogType === 'copy') {
            params.id = state.formData.id;
            copyProduct(params).then(res => {
              if (res) {
                proxy.$message.success(res.data.message);
                cacle(true);
              }
            });
          } else {
            updateProduct(params).then(res => {
              if (res) {
                cacle(true, params);
                proxy.$message.success('编辑成功');
              }
            });
          }
        }
      });
    };
    const cacle = (isRefresh, params) => {
      state.dialogVisible = false;
      context.emit('closeDialog', { dialogVisible: state.dialogVisible, isRefresh: isRefresh, productDetail: params });
    };
    return {
      ...toRefs(state),
      onSubmit,
      handleChangeSelect,
      getSpecifications,
      getDictionaryData,
      getDictionaryOptions,
      cacle
    };
  },
  computed: {},
  created() {}
};
</script>

<style lang="scss" scoped>
.AddEditSpecifications {
  :deep(.el-cascader) {
    width: 100%;
  }
  .productStyle {
    background-color: #f5f7fa;
    border-radius: 3px;
    padding: 0 10px;
  }
  .el-select {
    width: 100%;
  }
  :deep(.el-dialog .el-dialog__body .el-form-item__label) {
    overflow: hidden;
    white-space: nowrap;
    width: 100%;
    text-overflow: ellipsis;
  }
}
</style>
