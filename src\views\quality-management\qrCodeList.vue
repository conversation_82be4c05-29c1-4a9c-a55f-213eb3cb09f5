<template>
  <!-- 合格证打印 成品二维码打印 -->
  <ListLayout v-loading="expLoading">
    <template #search-bar>
      <el-form ref="editFrom" :inline="true" :model="formInline" class="page-searchbar" @submit.prevent>
        <el-form-item prop="queryStr">
          <el-input
            v-model="formInline.queryStr"
            v-trim
            v-focus
            placeholder="请输入关键字搜索"
            class="ipt-360"
            prefix-icon="el-icon-search"
            size="large"
            clearable
            @keyup.enter="getTableList()"
          />
        </el-form-item>
        <el-form-item style="margin-left: 0">
          <el-button type="primary" size="large" @click="getTableList()">查询</el-button>
          <el-button size="large" @click="reset">重置</el-button>
          <el-button
            class="searchBtn"
            size="large"
            type="text"
            @click="advancedSearch"
            @keyup.prevent
            @keydown.enter.prevent
            >高级搜索<i class="el-icon--right" :class="[showS ? 'el-icon-arrow-up' : 'el-icon-arrow-down']" />
          </el-button>
        </el-form-item>
      </el-form>
    </template>
    <template #button-group>
      <el-button
        v-if="getPermissionBtn('qrCodePrint')"
        type="primary"
        size="large"
        icon="el-icon-printer"
        @click="exportExcel"
        @keyup.prevent
        @keydown.enter.prevent
        >导出</el-button
      >
      <el-button
        v-if="getPermissionBtn('synchronousConclusion')"
        type="primary"
        size="large"
        :disabled="selected.length === 0"
        icon="el-icon-refresh"
        @click="handleSynchronization"
        @keyup.prevent
        @keydown.enter.prevent
      >
        同步结论</el-button
      >
      <el-button
        v-if="getPermissionBtn('qrCodePrint')"
        type="primary"
        :disabled="selected.length === 0"
        size="large"
        icon="el-icon-printer"
        @click="handlePrint"
        @keyup.prevent
        @keydown.enter.prevent
        >二维码打印</el-button
      >
    </template>
    <template #search-panel>
      <el-collapse v-model="activeName" class="search-collapse">
        <el-collapse-item name="1">
          <el-form ref="searchFromRef" :model="formInline" label-width="110px" label-position="right">
            <el-form-item label="申请日期：" prop="mateCode">
              <el-date-picker
                v-model="applicationDate"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                size="small"
                :shortcuts="shortcuts"
                @change="
                  val => {
                    return handleReview(val, 'beginApplicationTime', 'endApplicationTime');
                  }
                "
              />
            </el-form-item>
            <el-form-item label="检验日期：" prop="mateCode">
              <el-date-picker
                v-model="dateOfSurvey"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                size="small"
                :shortcuts="shortcuts"
                @change="
                  val => {
                    return handleReview(val, 'beginTestTime', 'endTestTime');
                  }
                "
              />
            </el-form-item>
          </el-form>
        </el-collapse-item>
      </el-collapse>
    </template>
    <template #radio-content>
      <el-radio-group v-model="printedFlag" size="small" style="float: left" @change="changeStatus">
        <el-radio-button label="">全部</el-radio-button>
        <el-radio-button v-for="(value, key, index) in statusList" :key="index" :label="key"
          >{{ value }}
        </el-radio-button>
      </el-radio-group>
    </template>
    <el-table
      ref="tableRef"
      :key="tableKey"
      v-loading="tableLoading"
      :data="tableData"
      fit
      border
      height="auto"
      size="medium"
      highlight-current-row
      class="dark-table format-height-table base-table"
      :row-style="
        () => {
          return 'cursor: pointer';
        }
      "
      @header-dragend="drageHeader"
      @selection-change="handleSelectionChange"
      @row-click="handleRowClick"
    >
      <el-table-column
        v-if="getPermissionBtn('addMeasurePlan')"
        type="selection"
        fixed="left"
        prop="checkbox"
        :width="colWidth.checkbox"
        align="center"
      />
      <el-table-column label="成品批次号" prop="batchNo" :min-width="colWidth.orderNo" show-overflow-tooltip>
        <template #default="{ row }">
          <div>{{ row.batchNo || '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column label="唯一编码" prop="uniqueNumber" :min-width="colWidth.orderNo" show-overflow-tooltip>
        <template #default="{ row }">
          <div>{{ row.uniqueNumber || '--' }}</div>
        </template>
      </el-table-column>
      <el-table-column
        label="生产订单号"
        prop="productionOrderNumber"
        :min-width="colWidth.orderNo"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <span>{{ row.productionOrderNumber || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="成品物料编号" prop="materialNumber" :width="colWidth.orderNo" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.materialNumber || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="成品物料名称" prop="materialName" :min-width="colWidth.orderNo" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.materialName || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="型号规格" prop="typeSpecification" :min-width="colWidth.model" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.typeSpecification || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="包装数量" prop="packageQuantity" :min-width="colWidth.amount" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.packageQuantity || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="包装规格" prop="packingSpecification" :min-width="colWidth.model" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.packingSpecification || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="销售订单号" prop="salesOrderNumber" :min-width="colWidth.orderNo" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.salesOrderNumber || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="检测结果" prop="testConclusion" :min-width="colWidth.model" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.testConclusion || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="检验日期" prop="testTime" :min-width="colWidth.date" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.testTime || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="检验人" prop="tester" :min-width="colWidth.person" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.tester || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="盘号" prop="reelNumber" :min-width="colWidth.plate" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.reelNumber || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="仓库" prop="warehouse" :min-width="colWidth.plate" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.warehouse || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="仓位" prop="warehouseLocation" :min-width="colWidth.plate" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.warehouseLocation || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="申请单号" prop="applicationNumber" :min-width="colWidth.orderNo" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.applicationNumber || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="申请日期" prop="applicationTime" :min-width="colWidth.date" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.applicationTime || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="申请人" prop="applicant" :width="colWidth.person" show-overflow-tooltip>
        <template #default="{ row }">
          <UserTag :name="getNameByid(row.applicant) || row.applicant || '--'" />
        </template>
      </el-table-column>
      <el-table-column label="打印次数" prop="printTimes" :width="colWidth.amount" show-overflow-tooltip>
        <template #default="{ row }">
          <span>{{ row.printTimes || '--' }}</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination :page="listQuery.page" :limit="listQuery.limit" :total="total" @pagination="getTableList" />
    <template #other>
      <!-- 打印弹出框 -->
      <el-dialog v-model="dialogCode" title="打印设置" :close-on-click-modal="false" width="400px" top="50px">
        <el-row>
          <el-col :span="24">
            <el-select v-model="qrSprint" placeholder="请选择打印机" size="small" clearable style="width: 100%">
              <el-option v-for="item in qrSprintList" :key="item.name" :label="item.name" :value="item.name">
                <span style="float: left">{{ item.name }}</span>
                <span style="float: right; color: #909399; font-size: 13px">{{ item.description }}</span>
              </el-option>
            </el-select>
            <div v-for="item in qrCodeList" :key="item.certificationVo.uniqueNumber" class="qrItem">
              <img :src="item.qrCodeString" alt="" />
              <div class="qrRight">
                <div class="qrRightItem">
                  <span class="label">物资编号：</span
                  ><span class="label">{{ item.certificationVo.materialNumber }}</span>
                </div>
                <div class="qrRightItem">
                  <span class="label">批次：</span><span class="label">{{ item.certificationVo.batchNo }}</span>
                </div>
                <div class="qrRightItem">
                  <span class="label">数量：</span
                  ><span class="label">{{ item.certificationVo.packingSpecification }}</span>
                </div>
                <!-- <div class="qrRightItem"><span class="label">物料描述：</span><span class="label">{{ item.batchNo }}</span></div> -->
              </div>
            </div>
          </el-col>
        </el-row>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="dialogCode = false">取 消</el-button>
            <el-button type="primary" @click="onSubmit">打 印</el-button>
          </span>
        </template>
      </el-dialog>
    </template>
  </ListLayout>
</template>

<script>
// getCurrentInstance
import { reactive, ref, onMounted, toRefs, getCurrentInstance } from 'vue';
import Pagination from '@/components/Pagination';
import { getList, synchronizatioApi, printersView, getPringList, printCode } from '@/api/qrCodeList';
import { getNameByid, getNamesByid, getPermissionBtn } from '@/utils/common';
import { useStore } from 'vuex';
import { formatDateTime } from '@/utils/formatTime';
import { drageHeader } from '@/utils/formatTable';
import ListLayout from '@/components/ListLayout';
import UserTag from '@/components/UserTag';
import { colWidth } from '@/data/tableStyle';

export default {
  name: 'QrCodeList',
  components: { Pagination, ListLayout, UserTag },
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const store = useStore();
    const state = reactive({
      tableData: [],
      printedFlag: '',
      qrSprintList: [], // 打印机列表
      qrSprint: '', // 选择的打印机
      tableRef: ref(),
      dialogCode: false,
      codeLoading: false,
      selected: [],
      applicationDate: [],
      dateOfSurvey: [],
      shortcuts: [
        {
          text: '近三天',
          value: (() => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 3);
            return [start, end];
          })()
        },
        {
          text: '最近一周',
          value: (() => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            return [start, end];
          })()
        },
        {
          text: '最近一个月',
          value: (() => {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            return [start, end];
          })()
        }
      ],
      formInline: {
        queryStr: ''
      },
      editFrom: ref(0),
      total: 0,
      processList: [], // 流程轨迹
      activeCollapse: '',
      searchFromRef: ref(),
      showS: false,
      activeName: '0',
      expLoading: false, // 导出的loading
      drawerLoading: false, // 流程轨迹loading
      tableLoading: false, // 表格加载的loading
      dialogFrom: {}, // 操作树节点的弹窗表格
      listQuery: {
        page: 1,
        limit: 20
      },
      activeIndex: '0',
      tabsData: store.state.user.materialList,
      tabsMoreData: [],
      qrCodeList: [],
      statusList: {
        false: '未打印',
        true: '已打印'
      }, // 类型
      moreIndex: 0
    });
    const tableKey = ref(0);
    const changeStatus = val => {
      getTableList();
    };
    const reset = () => {
      state.formInline = {};
      state.listQuery.page = 1;
      state.listQuery.limit = 20;
      state.applicationDate = [];
      state.dateOfSurvey = [];
      getTableList();
    };

    const handleSelectionChange = val => {
      state.selected = val;
    };

    // 树节点编辑
    const showEditDialog = ref(false);
    const clickArrow = () => {
      state.activeIndex = parseInt(state.activeIndex) + 1 + '';
    };
    const handleRowClick = row => {
      state.tableRef.toggleRowSelection(
        row,
        !state.selected.some(item => {
          return row.sampleId === item.sampleId;
        })
      );
    };
    const getTableList = query => {
      var params = {
        printedFlag: state.printedFlag,
        ...state.formInline
      };
      if (query && query.page) {
        params.page = query.page.toString();
        params.limit = query.limit.toString();
        state.listQuery.page = query.page;
        state.listQuery.limit = query.limit;
      } else {
        params.page = state.listQuery.page.toString();
        params.limit = state.listQuery.limit.toString();
      }
      if (params.printedFlag) {
        params.printedFlag = params.printedFlag === 'true';
      }
      state.tableLoading = true;
      getList(params).then(res => {
        state.tableLoading = false;
        if (res) {
          state.tableData = res.data.data.list;
          state.total = res.data.data.totalCount;
          state.listQuery.page = Number(params.page);
        }
      });
    };
    getTableList();
    // 同步结论
    const handleSynchronization = () => {
      const ids = state.selected.map(item => {
        return item.id;
      });
      synchronizatioApi({ ids: ids.toString() }).then(res => {
        if (res) {
          state.selected = [];
          proxy.$message.success('同步结论成功');
          getTableList();
        }
      });
    };
    const handlePrint = () => {
      state.codeLoading = true;
      const params = state.selected.map(item => {
        return item.uniqueNumber;
      });
      printersView({ uniqueNumbers: params.toString() }).then(res => {
        if (res) {
          state.dialogCode = true;
          state.codeLoading = false;
          state.qrCodeList = res.data.data;
        }
      });
      getPringList().then(res => {
        if (res) {
          state.qrSprintList = res.data.data;
          state.qrSprint = state.qrSprintList[0].name;
        }
      });
    };
    // 提交合格证打印
    const submitCertificate = () => {};
    onMounted(() => {});
    // 高级搜索
    const advancedSearch = () => {
      state.showS = !state.showS;
      if (state.activeName === '0') {
        state.activeName = '1';
      } else {
        state.activeName = '0';
      }
    };
    const formatJson = (filterVal, jsonData) => {
      return jsonData.map(v =>
        filterVal.map(j => {
          if (j === 'applicant') {
            return getNameByid(v[j]);
          } else if (j === 'applicationTime') {
            return formatDateTime(v[j]);
          } else {
            return v[j];
          }
        })
      );
    };
    const handleReview = (val, startTime, endTime) => {
      if (val) {
        state.formInline[startTime] = formatDateTime(val[0]);
        state.formInline[endTime] = formatDateTime(val[1]);
      } else {
        state.formInline[startTime] = '';
        state.formInline[endTime] = '';
      }
    };
    const onSubmit = () => {
      if (!state.qrSprint) {
        proxy.$message.error('请先选择打印机');
        return false;
      }
      const params = {
        printServiceName: state.qrSprint,
        uniqueNumbers: state.selected
          .map(item => {
            return item.uniqueNumber;
          })
          .toString()
      };
      printCode(params).then(res => {
        if (res) {
          if (res.data.data) {
            state.selected = [];
            proxy.$message.success('打印成功');
            state.dialogCode = false;
            getTableList();
          }
        }
      });
    };
    const export2Excel = allData => {
      var tHeader = [];
      var filterVal = [];
      var fileName = '模板';
      fileName = '合格证打印';
      tHeader = [
        '成品批次号',
        '唯一编码',
        '生产订单号',
        '成品物料编号',
        '成品物料名称',
        '型号规格',
        '包装数量',
        '包装规格',
        '销售订单号',
        '检测结果',
        '检验日期',
        '检验人',
        '盘号',
        '仓库',
        '仓位',
        '申请单号',
        '申请日期',
        '申请人',
        '打印次数'
      ];
      filterVal = [
        'batchNo',
        'uniqueNumber',
        'productionOrderNumber',
        'materialNumber',
        'materialName',
        'typeSpecification',
        'packageQuantity',
        'packingSpecification',
        'salesOrderNumber',
        'testConclusion',
        'testTime',
        'tester',
        'reelNumber',
        'warehouse',
        'warehouseLocation',
        'applicationNumber',
        'applicationTime',
        'applicant',
        'printTimes'
      ];
      import('@/utils/Export2Excel').then(excel => {
        const data = formatJson(filterVal, allData);
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: fileName,
          autoWidth: true,
          bookType: 'xlsx'
        });
      });
    };
    // 导出
    const exportExcel = () => {
      var params = {
        printedFlag: state.printedFlag,
        ...state.formInline,
        limit: '-1'
      };
      getList(params).then(res => {
        state.tableLoading = false;
        if (res) {
          export2Excel(res.data.data.list);
        }
      });
    };
    return {
      ...toRefs(state),
      exportExcel,
      export2Excel,
      onSubmit,
      handleReview,
      advancedSearch,
      handleRowClick,
      handleSynchronization,
      handlePrint,
      submitCertificate,
      formatJson,
      colWidth,
      getPermissionBtn,
      changeStatus,
      getTableList,
      formatDateTime,
      getNameByid,
      getNamesByid,
      drageHeader,
      showEditDialog,
      clickArrow,
      handleSelectionChange,
      tableKey,
      reset
    };
  }
};
</script>
<style lang="scss" scoped>
.certificateTitle {
  padding: 10px 10px;
  margin: 10px 0 30px 0;
  line-height: 25px;
  background: #eee;
  border-radius: 5px;
  li {
    list-style: none;
  }
}
.timeLineContent {
  margin: 0 auto;
  max-height: calc(100vh - 90px);
  overflow-y: auto;
  padding: 40px 20px 20px 0;
}
.itemImage {
  vertical-align: text-bottom;
  margin-right: 5px;
}
:deep(.el-timeline) {
  padding-left: 20px;
}

.hadEndTime {
  :deep(.el-timeline-item__node) {
    background: $tes-primary;
  }
}
.qrItem {
  background-color: #f0f2f5;
  margin-top: 10px;
  padding: 10px;
  display: flex;
  align-items: flex-start;
  img {
    height: 90px;
  }
  .qrRight {
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    height: 90px;
    padding-left: 10px;
  }
  .qrRightItem {
    line-height: 20px;
  }
}
.timeLineTop {
  line-height: 20px;
  text-align: left;
  .nodeName {
    font-size: 16px;
  }
  .endTime {
    font-size: 12px;
    color: $tes-font2;
    float: right;
  }
}

:deep(.el-collapse-item__header) {
  height: 20px;
  background-color: transparent;
  font-size: 16px;
  margin-bottom: 5px;
  border: 0;
}
:deep(.el-collapse) {
  border: 0;
}
:deep(.el-collapse-item__content) {
  padding-bottom: 0;
}
:deep(.el-collapse-item__wrap) {
  border-bottom: 0;
}
.inlineBlock {
  display: inline-block;
}

.el-button--primary {
  border: 0;
  border-radius: 4px;
}
.searchLeft {
  width: 70%;
  display: inline-block;
  .el-input {
    margin-right: 8px;
    width: 38%;
  }
}
.el-menu--horizontal > .el-menu-item.is-active {
  color: $tes-primary;
  border-bottom: 2.25px solid $tes-primary;
}
:deep(.el-drawer__close-btn) {
  margin-right: 9px;
}
.searchRight {
  margin-right: 0;
}
</style>
<style lang="scss"></style>
