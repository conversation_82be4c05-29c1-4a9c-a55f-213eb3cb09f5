<template>
  <v-chart
    class="chart"
    :option="chartOption"
    :autoresize="true"
    :style="{ width: chartWidth, height: chartHeight }"
    @click="handleClick"
  />
</template>

<script>
import { defaultOption } from './demo';
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, PieChart } from 'echarts/charts';
import {
  DatasetComponent,
  GridComponent,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  ToolboxComponent
} from 'echarts/components';
import { UniversalTransition, LabelLayout } from 'echarts/features';
import VChart, { THEME_KEY } from 'vue-echarts';
import { computed } from 'vue';

use([
  CanvasRenderer,
  DatasetComponent,
  GridComponent,
  Bar<PERSON><PERSON>,
  LabelLayout,
  LineChart,
  PieChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  UniversalTransition,
  ToolboxComponent
]);
export default {
  name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
  components: {
    VChart
  },
  provide: {
    [THEME_KEY]: 'light'
  },
  props: {
    option: {
      type: Object,
      default: function () {
        return defaultOption;
      }
    },
    width: {
      type: String,
      default: '600px'
    },
    height: {
      type: String,
      default: '400px'
    }
  },
  emits: ['clickEcharts'],
  setup(props, context) {
    const handleClick = args => {
      context.emit('clickEcharts', args);
    };
    const chartOption = computed({
      get: () => props.option
    });
    const chartWidth = computed({
      get: () => props.width
    });
    const chartHeight = computed({
      get: () => props.height
    });
    return {
      handleClick,
      chartOption,
      chartWidth,
      chartHeight
    };
  }
};
</script>

<style scoped>
.chart {
  height: 200px;
  width: 300px;
}
</style>
