<template>
  <el-dialog
    v-model="dialogSubmit"
    title="提交审核"
    :close-on-click-modal="false"
    width="480px"
    custom-class="submit_dialog"
    :before-close="closedialog"
  >
    <el-form v-if="dialogSubmit" ref="ruleForm" :model="formData" label-position="right" label-width="110px">
      <el-form-item
        label="试验日期："
        prop="date"
        :rules="{ required: true, message: '请选择试验日期', trigger: 'change' }"
      >
        <el-date-picker
          v-model="formData.date"
          :clearable="false"
          type="daterange"
          class="submit-form-item"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        />
      </el-form-item>
      <el-form-item
        label="试验员："
        prop="realOwnerIds"
        :rules="{ required: true, message: '请选择试验员', trigger: 'change' }"
      >
        <el-select
          v-model="formData.realOwnerIds"
          clearable
          multiple
          placeholder="请选择"
          class="submit-form-item"
          @change="handleDisable()"
        >
          <el-option
            v-for="item in getNameOptionaByid(jsonData.ownerIds)"
            :key="item.value"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        label="校核人员："
        prop="reviewerId"
        :rules="{ required: true, message: '请选择校核人员', trigger: 'change' }"
      >
        <el-select v-model="formData.reviewerId" clearable filterable class="submit-form-item">
          <!-- 默认选择不是登录人的第一个人 -->
          <!--<el-option v-for="item in userList" :key="item.value" :label="item.label" :value="item.value" :disabled="item.label=='邓洪冈'" />-->
          <el-option
            v-for="item in userList"
            :key="item.value"
            :label="item.nickname"
            :value="item.id"
            :disabled="item.id === getLoginInfo().accountId || item.status === -2"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="closedialog">取 消</el-button>
        <el-button type="primary" @click="handleSubmit(1)" @keyup.prevent @keydown.enter.prevent>确 认</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script>
import { reactive, toRefs, ref, watch, getCurrentInstance } from 'vue';
import { formatDate } from '@/utils/formatTime';
import router from '@/router/index.js';
import { useRoute } from 'vue-router';
import { experimentmongodatainfo, saveAndSubmit } from '@/api/execution';
import { ElMessage } from 'element-plus';
import { getLoginInfo } from '@/utils/auth';
import { getNameOptionaByid, getNameByid, getNamesByid } from '@/utils/common';
// import { reportAudit } from '@/api/permission'
import { addByTemp } from '@/api/messageAgent';
import { getRecordUserList } from '@/api/user';
import uniWebview from '@dcloudio/uni-webview-js';
export default {
  name: 'ModuleAudit',
  props: {
    dataValue: {
      type: Object,
      default: function () {
        return {};
      }
    },
    isStandardCustom: {
      type: Number,
      default: 0
    },
    jsonData: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  emits: ['closedialog', 'sumbitData', 'sumbitSuccess'],
  setup(props, ctx) {
    const route = useRoute();
    watch(props, newValue => {
      state.dialogSubmit = newValue.dataValue.dialogSubmit;
      if (newValue.dataValue.dialogSubmit) {
        state.dataValue = newValue.dataValue;
        state.jsonData = newValue.jsonData;
        getexperimentData();
        initializeData();
        getUserList();
      }
    });
    const ruleForm = ref('');
    const state = reactive({
      dialogSubmit: props.dataValue.dialogSubmit,
      dataValue: props.dataValue,
      jsonData: props.jsonData,
      defaultDate: [],
      excelInfo: {},
      experimentReviewOwnerList: [],
      currentAccountId: getLoginInfo().accountId,
      formData: {
        realOwnerIds: [getLoginInfo().accountId],
        reviewerId: '',
        date: []
      },
      userList: []
    });
    // // 获取资源权限
    // reportAudit('recordAudit').then(res => {
    //   state.experimentReviewOwnerList = res.data
    // })
    // 获取 获取具有原始记录审核/原始记录审核详情权限的 人员列表
    const getUserList = () => {
      getRecordUserList({}).then(res => {
        if (res) {
          state.userList = res.data.data;
        }
      });
    };

    // 提交审核事件
    const { appContext } = getCurrentInstance();
    const bus = appContext.config.globalProperties.bus;
    bus.$on('excelData', allDate => {
      state.excelInfo.experimentData = allDate;
    });
    const handleSubmit = () => {
      ctx.emit('sumbitData');
      const postdata = {};
      // const experimentDataNew = {}
      postdata.capabilityId = state.jsonData.capabilityId;
      postdata.templateId = state.jsonData.templateId;
      postdata.samplesId = state.jsonData.samplesId;
      postdata.experimentId = state.jsonData.experimentId;
      postdata.showType = state.jsonData.showType;
      postdata.source = state.jsonData.source;
      postdata.experimentData = state.excelInfo.experimentData;
      postdata.isUseStandard = state.jsonData.isUseStandard; // 是否是需要判定标准的模板
      postdata.isStandardCustom = state.isStandardCustom; // 判断是否自定义标准

      ruleForm.value.validate().then(valid => {
        if (valid) {
          if (state.formData.realOwnerIds.indexOf(state.formData.reviewerId) !== -1) {
            ElMessage.warning({
              message: '试验员和校核员不能为相同的人',
              type: 'warning'
            });
            return false;
          }
          postdata.realOwnerIds = state.formData.realOwnerIds.toString();
          postdata.reviewerId = state.formData.reviewerId;
          postdata.completeStartDate = formatDate(state.formData.date[0]);
          postdata.completeDatetime = formatDate(state.formData.date[1]);
          postdata.saveColorNumber = state.jsonData.experimentData.saveColorNumber;
          if (!postdata.saveColorNumber) {
            postdata.saveColorNumber = state.jsonData.experimentData.coreRecord.coreColourList.length;
          }
          saveAndSubmit(postdata).then(res => {
            if (res) {
              ctx.emit('sumbitSuccess');
              if (route.name === 'recordReviewAdd') {
                router.push({
                  path: '/recordReviewDetail',
                  query: {
                    samplesId: postdata.samplesId
                    // capabilityId: postdata.capabilityId
                  }
                });
              }
              if (route.name === 'AddRecord') {
                router.push({
                  path: '/execution/detail',
                  query: {
                    samplesId: postdata.samplesId,
                    avtivestatus: 0
                  }
                });
              }
              if (route.name === 'UniappTemplate') {
                uniWebview.postMessage({
                  data: { action: 'submitSuccess' }
                });
              }
              if (route.name !== 'UniappTemplate') {
                ElMessage.success({
                  message: '提交成功',
                  type: 'success'
                });
              }
              // 添加消息待办
              const params = {
                eventCode: 'M007',
                receiverType: '1',
                senderName: getNameByid(state.currentAccountId),
                receiverIds: postdata.reviewerId,
                receiverNames: getNameByid(postdata.reviewerId),
                todoStartTime: postdata.completeStartDate,
                todoEndTime: postdata.completeDatetime,
                c_ids: postdata.capabilityId,
                c_b_samplesIdArray: postdata.samplesId,
                c_b_sampleNoArray: state.jsonData.secSampleNum,
                c_b_projectNameArray: state.jsonData.capabilityName
              };
              addByTemp(params).then(res => {
                // if (res !== false) {
                // }
              });
              params.eventCode = 'M008';
              params.receiverIds = postdata.realOwnerIds;
              params.receiverNames = getNamesByid(postdata.realOwnerIds).join(',');
              addByTemp(params).then(res => {
                // if (res !== false) {
                // }
              });
              ctx.emit('closedialog', true);
            }
          });
        }
      });
    };
    const getexperimentData = () => {
      state.excelInfo = {};
      if (state.jsonData.experimentId) {
        experimentmongodatainfo(state.jsonData.experimentId).then(res => {
          if (res) {
            state.excelInfo.experimentData = res.data.data;
            // state.excelInfo.capabilityId = res.data.data.capabilityId
            // state.excelInfo.experimentId = res.data.data.experimentId
            // state.excelInfo.remark = res.data.data.remark
            state.excelInfo.templateId = res.data.data.templateId;
          }
        });
      }
    };
    getexperimentData();
    // 判断校核人员disbale status的判断
    const handleDisable = val => {
      state.formData.realOwnerIds.forEach(item => {});
    };
    // 关闭弹屏
    const closedialog = () => {
      state.dialogSubmit = false;
      ruleForm.value.resetFields();
      ctx.emit('closedialog', false);
    };
    const initializeData = () => {
      state.formData.date[0] = formatDate(new Date());
      state.formData.date[1] = formatDate(new Date());
      state.formData.reviewerId = '';
    };
    return {
      ...toRefs(state),
      ruleForm,
      handleDisable,
      initializeData,
      getNameOptionaByid,
      getLoginInfo,
      handleSubmit,
      getexperimentData,
      closedialog
    };
  }
};
</script>
<style lang="scss" scoped>
.submit-form-item {
  width: 100%;
  // height: 36px;
}
:deep(.el-range-editor.el-input__inner) {
  width: 100%;
}
// .el-form-item.is-required.el-form-item--medium {
//   height: 49px;
//   :deep(.el-select__tags) {
//     height: 36px;
//   }
// }
</style>
