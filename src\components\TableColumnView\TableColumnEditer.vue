<template>
  <!-- 编辑视图 -->
  <el-drawer
    :model-value="modelValue"
    :title="
      isEditCustomView
        ? '编辑当前视图'
        : isNewCustomView
        ? '新增自定义视图'
        : isNewFixedView
        ? '新建默认视图'
        : isEditFixedView
        ? '编辑默认视图'
        : ''
    "
    :size="isNewFixedView ? '70%' : isEditFixedView ? '65%' : '55%'"
    direction="rtl"
    :before-close="handleBeforeClose"
    @open="handleOpen"
    @close="handleClose"
  >
    <DrawerLayout :has-button-group="false" :has-page-header="false">
      <el-form
        ref="formRef"
        :label-width="isFixedView ? '80px' : '110px'"
        label-position="left"
        size="small"
        :model="viewForm"
        :rules="viewFormRules"
      >
        <el-row :gutter="40">
          <template v-if="isFixedView">
            <el-col :span="24">
              <el-form-item label="所属资源" size="small" prop="bindingMenuName" required>
                <span class="table-column-editer__label-colon">：</span>
                <el-cascader
                  v-model="menuKeys"
                  :disabled="isEditFixedView"
                  :props="{ label: 'title', value: 'key' }"
                  :options="menuList"
                  @change="handleMenuKeysChange"
                />
              </el-form-item>
              <el-form-item label="资源KEY" size="small" prop="bindingMenu" required>
                <span class="table-column-editer__label-colon">：</span>
                <span>{{ viewForm.bindingMenu }}</span>
              </el-form-item>
              <el-form-item label="列表页" size="small" prop="isList" required>
                <el-switch
                  v-model="viewForm.isList"
                  :active-value="1"
                  :inactive-value="0"
                  :active-text="viewForm.isList == 1 ? '是' : '否'"
                />
              </el-form-item>
            </el-col>
          </template>
          <template v-else-if="isCustomView">
            <el-col :span="24">
              <el-form-item label="视图名称" size="small" prop="viewName">
                <span class="table-column-editer__label-colon">：</span>
                <template v-if="isNewCustomView || isEditViewName">
                  <el-input v-model="viewForm.viewName" size="small" style="width: 50%" maxlength="100" />
                  <el-button v-if="isEditViewName" size="small" @click="confirmViewName">确认</el-button>
                </template>
                <template v-else>
                  <span>{{ viewForm.viewName }}</span>
                  <el-button
                    circle
                    icon="el-icon-edit"
                    style="border-color: #ffffff"
                    size="small"
                    @click="isEditViewName = !isEditViewName"
                  />
                </template>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="默认展示视图" size="small" prop="isDefault" required>
                <span class="table-column-editer__label-colon">：</span>
                <el-radio-group v-model="viewForm.isDefault">
                  <el-radio label="0" size="small">否</el-radio>
                  <el-radio label="1" size="small">是</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </template>
        </el-row>
      </el-form>
      <el-form ref="formTableRef" label-position="left" :model="tableColumnData">
        <div class="flex justify-between">
          <el-radio-group
            v-if="dictionaryGroup.length"
            v-model="filterGroup"
            size="small"
            class="mb-2"
            clearable
            @change="handleChangeViewGroup"
          >
            <el-radio-button v-for="item in dictionaryGroup" :key="item.code" :label="item.code">{{
              item.name
            }}</el-radio-button>
          </el-radio-group>
          <div v-if="viewForm.isList == 0">
            <!-- <el-button size="small" @click="handleCopy()">复制</el-button> -->
            <el-select v-if="isCopyView" v-model="copyValue" size="small" placeholder="" @change="handleChangeCopy">
              <el-option v-for="item in dictionaryGroup" :key="item.code" :label="item.name" :value="item.code" />
            </el-select>
          </div>
        </div>
        <el-table
          ref="tableRef"
          :key="tableKey"
          v-loading="tableLoading"
          :data="tableColumnData"
          row-key="order"
          height="auto"
          class="table-column-editer__list"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="25" />
          <el-table-column type="index" label="序号" width="50" align="center" :index="index => index + 1" />
          <el-table-column label="顺序" width="70" align="center">
            <i class="tes-move iconfont" style="font-size: 12px; cursor: move" />
          </el-table-column>
          <el-table-column label="字段名" min-width="150">
            <template #default="{ row, $index }">
              <el-form-item :prop="$index + '.fieldName'" required>
                <el-input
                  v-model.trim="row.fieldName"
                  size="small"
                  maxlength="100"
                  placeholder="字段名"
                  @change="
                    val => {
                      return handleChangeField(val, $index, 'fieldName');
                    }
                  "
                />
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column label="字段编码" min-width="150" :show-overflow-tooltip="isNewFixedView ? false : true">
            <template #default="{ row, $index }">
              <el-form-item
                v-if="isNewFixedView || row.isNewRow || getPermissionBtn('editFieldCode')"
                :prop="$index + '.fieldKey'"
                required
              >
                <el-input
                  v-model.trim="row.fieldKey"
                  size="small"
                  maxlength="100"
                  placeholder="字段编码"
                  @change="
                    val => {
                      return handleChangeField(val, $index, 'fieldKey');
                    }
                  "
                />
              </el-form-item>
              <span v-else>{{ row.fieldKey }}</span>
            </template>
          </el-table-column>
          <el-table-column v-if="isFixedView" label="字段类型" width="110" :align="isFixedView ? 'left' : 'center'">
            <template #default="{ row, $index }">
              <el-form-item :prop="$index + '.fieldType'" required>
                <el-select
                  v-model="row.fieldType"
                  size="small"
                  placeholder=""
                  @change="
                    val => {
                      return handleChangeField(val, $index, 'fieldType');
                    }
                  "
                >
                  <el-option v-for="item in fieldTypes" :key="item.type" :label="item.name" :value="item.type" />
                </el-select>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column label="字段宽度" width="80">
            <template #default="{ row, $index }">
              <el-form-item :prop="$index + '.columnWidth'" required>
                <el-input
                  v-model.trim="row.columnWidth"
                  maxlength="30"
                  @change="
                    val => {
                      return handleChangeField(val, $index, 'columnWidth');
                    }
                  "
                />
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column label="固定列" width="110">
            <template #default="{ row, $index }">
              <el-select
                v-model="row.columnFixedType"
                size="small"
                placeholder=""
                @change="
                  val => {
                    return handleChangeField(val, $index, 'columnFixedType');
                  }
                "
              >
                <el-option v-for="item in columnFixedTypes" :key="item.code" :label="item.name" :value="item.code" />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="自适应宽度" width="90" align="center">
            <template #default="{ row, $index }">
              <el-switch
                v-model="row.isMinWidth"
                :active-value="1"
                :inactive-value="0"
                @change="
                  val => {
                    return handleChangeField(val, $index, 'isMinWidth');
                  }
                "
              />
            </template>
          </el-table-column>
          <el-table-column label="是否排序" width="90" align="center">
            <template #default="{ row, $index }">
              <el-switch
                v-model="row.isSortable"
                :active-value="1"
                :inactive-value="0"
                @change="
                  val => {
                    return handleChangeField(val, $index, 'isSortable');
                  }
                "
              />
            </template>
          </el-table-column>
          <el-table-column label="查询字段" width="90" align="center">
            <template #default="{ row, $index }">
              <el-switch
                v-if="isFixedView"
                v-model="row.isQuery"
                :active-value="1"
                :inactive-value="0"
                @change="
                  val => {
                    return handleChangeField(val, $index, 'isQuery');
                  }
                "
              />
              <span v-else>{{ row.isQuery ? '是' : '否' }}</span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="isFixedView"
            label="字段所属租户类型"
            width="150"
            :align="isFixedView ? 'left' : 'center'"
          >
            <template #default="{ row, $index }">
              <el-form-item :prop="$index + '.fieldTenantType'" required>
                <el-select
                  v-model="row.fieldTenantType"
                  size="small"
                  placeholder=""
                  @change="
                    val => {
                      return handleChangeField(val, $index, 'fieldTenantType');
                    }
                  "
                >
                  <el-option
                    v-for="(item, index) in fieldTenantTypes"
                    :key="index"
                    :label="item.name"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column v-if="isNewFixedView" label="操作" width="70px" prop="caozuo">
            <template #default="{ $index }">
              <span class="blue-color" @click="removeField($index)">删除</span>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <BottomPanel>
        <template #panel-content>
          <div class="flex justify-between items-center" style="overflow: hidden">
            <el-button v-if="isFixedView" type="primary" size="small" style="float: left" @click="addNewField">
              添加字段
            </el-button>
            <span class="">注：字段宽度在表格里代表宽度，在表单里代表占比，总数24</span>
            <div style="float: right">
              <el-button size="small" @click="resetView" @keyup.prevent @keydown.enter.prevent> 重置 </el-button>
              <el-button
                type="primary"
                size="small"
                :loading="saving"
                @click="saveView"
                @keyup.prevent
                @keydown.enter.prevent
              >
                保存
              </el-button>
            </div>
          </div>
        </template>
      </BottomPanel>
    </DrawerLayout>
  </el-drawer>
</template>

<script>
import { ref, reactive, toRefs, computed, watch, onMounted, nextTick, getCurrentInstance } from 'vue';
import { useStore } from 'vuex';
import { ElMessage } from 'element-plus';
import { getViewByViewId, saveOrUpdateView } from '@/api/tableView';
import { getMenuList } from '@/utils/auth';
import { viewFormRules } from '@/utils/func/customTable';
import { tenantTypeOptions } from '@/data/industryTerm';
import Sortable from 'sortablejs';
import { getPermissionBtn } from '@/utils/common';
import { getDictionary } from '@/api/user';
import { cloneDeep, isEqual } from 'lodash';
import { filterTableColumnDataByTenantType } from './tools';
import { fieldTypesEnum, columnFixedTypesEnum } from './enum';
import DrawerLayout from '@/components/DrawerLayout';
import BottomPanel from '@/components/PageComponents/BottomPanel';

export default {
  name: 'TableColumnEditer',
  components: { DrawerLayout, BottomPanel },
  props: {
    type: {
      type: String,
      default: 'add',
      validator(value) {
        return ['add', 'edit', 'addFixed'].includes(value);
      }
    },
    modelValue: {
      type: Boolean,
      default: false
    },
    userId: {
      type: String,
      default: ''
    },
    bindingMenu: {
      type: String,
      default: ''
    },
    view: {
      type: Object,
      default: () => {
        return {
          id: '',
          viewName: '',
          isDefault: 0,
          isList: 1,
          tableColumnData: []
        };
      }
    }
  },
  emits: ['update:model-value', 'update:view'],
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    const store = useStore();
    const state = reactive({
      isEditViewName: false,
      menuKeys: [],
      filterGroup: '',
      copyValue: '',
      tableViewData: {},
      isCopyView: false, // 是否开启复制模式
      dictionaryGroup: [],
      viewForm: {
        bindingMenuName: '',
        bindingMenu: '',
        viewName: '',
        isFixedView: '',
        isDefault: '',
        isList: 1
      },
      tableLoading: false,
      tableColumnData: [],
      dictionaryTable: {},
      tableSelectedData: [],
      tableKey: 0,
      isModified: false,
      originViewForm: {},
      originTableColumnData: [],
      originTableSelectedData: [],
      saving: false,
      isCheckDataChanged: true,
      menuList: []
    });

    const formRef = ref(null);
    const formTableRef = ref(null);
    const tableRef = ref(null);

    const isNewFixedView = computed(() => props.type === 'addFixed');
    // const isList = computed(() => props.type === 'addFixed');
    const isEditFixedView = computed(() => props.type === 'edit' && props.view?.isFixedView?.toString() === '1');
    const isFixedView = computed(() => isNewFixedView.value || isEditFixedView.value);
    const isNewCustomView = computed(() => props.type === 'add');
    const isEditCustomView = computed(() => props.type === 'edit' && props.view?.isFixedView?.toString() === '0');
    const isCustomView = computed(() => isNewCustomView.value || isEditCustomView.value);
    const tenantInfo = computed(() => store.getters.tenantInfo);

    watch(
      () => state.tableKey,
      (newValue, oldValue) => {
        nextTick(() => {
          rowDrop();
          toggleSelection(state.tableColumnData);
        });
      }
    );
    const fieldTypes = [
      { type: fieldTypesEnum.Text, name: '文本' },
      { type: fieldTypesEnum.Date, name: '日期' },
      { type: fieldTypesEnum.Link, name: '链接' },
      { type: fieldTypesEnum.Status, name: '状态' },
      { type: fieldTypesEnum.Person, name: '人员' },
      { type: fieldTypesEnum.Progress, name: '进度' },
      { type: fieldTypesEnum.Custom, name: '自定义' }
    ];

    const columnFixedTypes = [
      { code: columnFixedTypesEnum.None, name: '不启用' },
      { code: columnFixedTypesEnum.Left, name: '左固定' },
      { code: columnFixedTypesEnum.Right, name: '右固定' }
    ];

    const fieldTenantTypes = [{ id: -1, name: '通用' }, ...tenantTypeOptions];

    onMounted(() => {
      const menuList = getMenuList();
      const newMenuList = [];
      menuList.forEach(menuItem => {
        if (menuItem.children.length > 0 && !/^(BusinessManage|userManage|PlatformManage)$/.test(menuItem.key)) {
          newMenuList.push({
            title: menuItem.title,
            key: menuItem.key,
            children: menuItem.children.map(item => ({
              title: item.title,
              key: item.key
            }))
          });
        }
      });
      state.menuList = newMenuList;
    });
    // 切换视图分组
    const handleChangeViewGroup = val => {
      state.tableColumnData = state.dictionaryTable[val]?.length
        ? JSON.parse(JSON.stringify(state.dictionaryTable[val]))
        : [];
      state.tableColumnData.forEach((item, index) => {
        item.order = index;
      });
      state.tableSelectedData = state.tableColumnData.filter(row => Boolean(row.isShow));
      toggleSelection(state.tableColumnData);
    };
    // 查询视图详情
    const getViewDetail = async () => {
      state.tableLoading = true;
      const res = await getViewByViewId(props.view?.id);
      state.tableLoading = false;
      if (res && res.data.code === 200) {
        setTableColumnData(res.data.data.sysEmployeeListConfigList);
        state.tableKey += 1;
      }
    };
    // 查询视图详情分组
    const getDictionaryGroup = async () => {
      if (!state.viewForm.bindingMenu) {
        return;
      }
      state.tableLoading = true;
      const res = await getDictionary(state.viewForm.bindingMenu);
      state.tableLoading = false;
      state.dictionaryGroup = [];
      state.filterGroup = '';
      if (res) {
        res.data.data.dictionaryoption.forEach(item => {
          if (item.status == 1) {
            state.dictionaryGroup.push(item);
          }
        });
        state.filterGroup = state.dictionaryGroup[0]?.code || '';
        if (state.filterGroup) {
          state.tableColumnData = state.dictionaryTable[state.filterGroup];
        }
      }
    };
    // 初始化视图数据
    const initView = async () => {
      state.isEditViewName = false;
      state.menuKeys = [];
      state.tableColumnData = [];
      state.originTableColumnData = [];
      state.originTableSelectedData = [];
      state.dictionaryTable = {};
      state.viewForm.bindingMenu = props.bindingMenu || '';
      const bindingMenuItems = getBindingMenuItems([null, props.bindingMenu]);
      if (bindingMenuItems.length > 1) {
        state.menuKeys = [bindingMenuItems[0].key, bindingMenuItems[1].key];
        state.viewForm.bindingMenuName = bindingMenuItems[1].title;
      }
      state.viewForm.viewName = isFixedView.value
        ? '默认视图'
        : isNewCustomView.value
        ? ''
        : props.view?.viewName || '';
      state.viewForm.isFixedView = isFixedView.value ? '1' : props.view?.isFixedView?.toString();
      state.viewForm.isDefault = props.view?.isDefault?.toString() || '0';
      state.viewForm.isList = props.view?.isList;
      state.originViewForm = cloneDeep(state.viewForm);
      state.isCheckDataChanged = true;
      await getDictionaryGroup();
      if (!props.view?.tableColumnData?.length > 0 && props.view?.id) {
        getViewDetail();
      } else {
        setTableColumnData(props.view?.tableColumnData && cloneDeep(props.view.tableColumnData));
      }
      nextTick(() => {
        formRef.value?.clearValidate();
        formTableRef.value?.clearValidate();
      });
    };

    // 查找所属页面的路径数据
    const getBindingMenuItems = ([parentKey, childKey]) => {
      if (childKey) {
        for (let index = 0; index < state.menuList.length; index++) {
          const menuItem = state.menuList[index];
          if (parentKey && menuItem.key !== parentKey) {
            continue;
          }
          const childItem = menuItem.children.find(child => child.key === childKey);
          if (childItem) {
            return [menuItem, childItem];
          }
        }
      }
      return [];
    };

    // 设置 tableColumnData
    const setTableColumnData = tableColumnData => {
      let paramsTable = tableColumnData;
      if (tableColumnData?.length > 0) {
        if (state.filterGroup) {
          state.dictionaryGroup.forEach(item => {
            state.dictionaryTable[item.code] = tableColumnData.filter(val => {
              return val.groupCode == item.code;
            });
          });
          paramsTable = state.dictionaryTable[state.filterGroup];
        }
        if (isCustomView.value) {
          state.tableColumnData = filterTableColumnDataByTenantType(paramsTable, tenantInfo.value.type);
        } else {
          state.tableColumnData = paramsTable;
        }
        // console.log(state.tableColumnData);
        state.tableColumnData = state.tableColumnData.map(item => ({
          ...item
          // isMinWidth: Boolean(item.isMinWidth),
          // // fieldTenantType:
          // //   item.fieldTenantType == -1
          // //     ? fieldTenantTypes.map(item => {
          // //         return item.id;
          // //       })
          // //     : item.fieldTenantType.split(','),
          // isQuery: Boolean(item.isQuery),
          // isSortable: Boolean(item.isSortable)
        }));
        state.tableSelectedData = state.tableColumnData.filter(row => Boolean(row.isShow));
        state.originTableColumnData = cloneDeep(state.tableColumnData);
        state.originTableSelectedData = cloneDeep(state.tableSelectedData);
      } else {
        state.dictionaryGroup.forEach(item => {
          state.dictionaryTable[item.code] = [];
        });
        state.tableColumnData = [];
        state.tableSelectedData = [];
        state.originTableColumnData = [];
        state.originTableSelectedData = [];
      }
    };

    // 每当 Drawer 打开并重新赋值
    const handleOpen = () => {
      initView();
      state.tableKey += 1;
    };

    // Drawer 关闭之前
    const handleBeforeClose = done => {
      if (state.isCheckDataChanged && isModified()) {
        proxy
          .$confirm('当前页面数据已更新，是否确认离开？', {
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            type: 'warning',
            showClose: false,
            closeOnClickModal: false,
            closeOnPressEscape: false
          })
          .then(done.bind(null, false))
          .catch(done.bind(null, true));
      } else {
        done();
      }
    };

    // Drawer 关闭
    const handleClose = () => {
      if (props.modelValue) {
        state.dictionaryGroup = [];
        state.filterGroup = '';
        setTableColumnData([]);
        context.emit('update:model-value', false);
      }
    };

    // 处理 Cascader 事件
    const handleMenuKeysChange = values => {
      if (values instanceof Array && values.length > 1) {
        const bindingMenuItems = getBindingMenuItems(values);
        if (bindingMenuItems.length > 1) {
          state.viewForm.bindingMenu = bindingMenuItems[1].key;
          state.viewForm.bindingMenuName = bindingMenuItems[1].title;
        }
      }
    };

    // 是否修改了数据
    const isModified = () => {
      return (
        !isEqual(state.viewForm, state.originViewForm) ||
        !isEqual(state.tableColumnData, state.originTableColumnData) ||
        !isEqual(state.tableSelectedData, state.originTableSelectedData)
      );
    };

    // 确认视图名称
    const confirmViewName = () => {
      formRef.value.validateField(['viewName'], error => {
        if (!error) {
          state.isEditViewName = false;
        }
      });
    };

    // 添加新的字段
    const addNewField = () => {
      const addRow = {
        columnWidth: 200,
        fieldKey: '',
        fieldName: '',
        fieldType: 'text',
        fieldTenantType: -1,
        id: '',
        isMinWidth: false,
        columnFixedType: 0,
        isQuery: 1,
        isShow: 1,
        isSortable: 0,
        order: state.tableColumnData.length,
        viewId: '',
        isNewRow: true
      };
      if (state.filterGroup) {
        addRow.groupCode = state.filterGroup;
        state.dictionaryTable[state.filterGroup].push(addRow);
      }
      state.tableColumnData.push(addRow);
      toggleSelection(state.tableColumnData);
    };
    const handleCopy = () => {
      state.isCopyView = true;
    };
    const handleChangeCopy = val => {
      const newTable = [];
      state.dictionaryTable[val].forEach(item => {
        const paramsItem = JSON.parse(JSON.stringify(item));
        delete paramsItem.viewId;
        delete paramsItem.id;
        const addRow = {
          ...paramsItem,
          groupCode: state.filterGroup
          // isMinWidth: Boolean(paramsItem.isMinWidth),
          // isQuery: Boolean(paramsItem.isQuery),
          // isSortable: Boolean(paramsItem.isSortable)
        };
        newTable.push(addRow);
        state.dictionaryTable[state.filterGroup].push(addRow);
      });
      state.tableColumnData = newTable;
      // setTableColumnData(newTable);
    };
    // 删除字段
    const removeField = index => {
      state.tableColumnData.splice(index, 1);
      if (state.filterGroup) {
        state.dictionaryTable[state.filterGroup].splice(index, 1);
      }
    };

    // 设置 table checkbox 状态
    const toggleSelection = rows => {
      if (rows) {
        rows.forEach(row => {
          tableRef.value?.toggleRowSelection(row, Boolean(row.isShow));
        });
      } else {
        tableRef.value?.clearSelection();
      }
    };

    // table 选择事件
    const handleSelectionChange = selectedRows => {
      state.tableSelectedData = selectedRows;
    };

    // 重置视图值
    const resetView = () => {
      initView();
      state.filterGroup = state.dictionaryGroup[0]?.code || '';
      if (state.tableKey !== 1) {
        state.tableKey = 1;
        nextTick(() => {
          toggleSelection(state.tableColumnData);
        });
      }
    };

    // 保存视图
    const saveView = async () => {
      await formRef.value.validate();
      await formTableRef.value.validate();
      let paramsTable = [];
      if (state.filterGroup) {
        paramsTable = JSON.parse(JSON.stringify(Object.values(state.dictionaryTable).flat()));
        paramsTable.forEach((item, index) => {
          if (item.groupCode == state.filterGroup) {
            const isChecked = state.tableSelectedData.some(selectedItem => item.id === selectedItem.id);
            item.isShow = isChecked ? 1 : 0;
          }
          // item.isMinWidth = item.isMinWidth ? 1 : 0;
          // item.isQuery = item.isQuery ? 1 : 0;
          // item.isSortable = item.isSortable ? 1 : 0;
          if (isNewCustomView.value) {
            item.id = '';
            item.viewId = '';
          }
          if (item.isNewRow) {
            delete item.isNewRow;
          }
          item.order = index;
        });
      } else {
        paramsTable = state.tableColumnData.map(item => {
          const newItem = { ...item };
          const isChecked = state.tableSelectedData.some(selectedItem => newItem.id === selectedItem.id);
          newItem.isShow = isChecked ? 1 : 0;
          // newItem.isMinWidth = newItem.isMinWidth ? 1 : 0;
          // newItem.isQuery = newItem.isQuery ? 1 : 0;
          // newItem.isSortable = newItem.isSortable ? 1 : 0;
          if (isNewCustomView.value) {
            newItem.id = '';
            newItem.viewId = '';
          }
          if (newItem.isNewRow) {
            delete newItem.isNewRow;
          }
          return newItem;
        });
      }
      const saveParams = {
        userId: props.userId,
        bindingMenuName: state.viewForm.bindingMenuName,
        bindingMenu: state.viewForm.bindingMenu,
        isList: state.viewForm.isList,
        id: isNewFixedView.value || isNewCustomView.value ? '' : props.view.id,
        viewName: state.viewForm.viewName,
        isFixedView: isFixedView.value ? '1' : '0',
        isDefault: isFixedView.value ? '0' : state.viewForm.isDefault,
        sysEmployeeListConfigList: paramsTable
      };
      state.saving = true;
      const res = await saveOrUpdateView(saveParams);
      state.saving = false;
      if (res && res.data.code === 200) {
        ElMessage.success(`${saveParams.viewName}-保存成功!`);
        const updatedViewData = res.data.data;
        context.emit('update:view', {
          id: updatedViewData.id,
          viewName: updatedViewData.viewName,
          isFixedView: updatedViewData.isFixedView,
          isDefault: Number(updatedViewData.isDefault),
          isList: Number(updatedViewData.isList),
          sysEmployeeListConfigList: updatedViewData.sysEmployeeListConfigList
        });
        state.isCheckDataChanged = false;
        handleClose();
      }
      handleClose();
    };

    // 行拖拽
    const rowDrop = () => {
      // 获取当前表格
      const tbody = tableRef.value.$el.querySelector('.el-table__body-wrapper tbody');
      Sortable.create(tbody, {
        animation: 150,
        handle: '.tes-move',
        draggable: '.el-table__row',
        ghostClass: 'ghost',
        // 拖动对象移动样式
        dragClass: 'drag',
        forceFallback: true,
        onEnd(evt) {
          if (evt.oldIndex !== evt.newIndex) {
            // 移除原来的数据
            const currRow = state.tableColumnData.splice(evt.oldIndex, 1)[0];
            // 移除原来的数据并插入新的数据
            state.tableColumnData.splice(evt.newIndex, 0, currRow);
            state.tableColumnData.forEach((item, index) => {
              const isChecked = state.tableSelectedData.some(selectedItem => item.id === selectedItem.id);
              item.isShow = isChecked ? 1 : 0;
              item.order = index;
            });
            if (state.filterGroup) {
              state.dictionaryTable[state.filterGroup] = state.tableColumnData;
            }
            state.tableKey += 1;
          }
        }
      });
    };
    // 修改表单
    const handleChangeField = (val, index, fieldName) => {
      console.log(state.filterGroup);
      if (state.filterGroup) {
        state.dictionaryTable[state.filterGroup][index][fieldName] = val;
      }
      console.log(state.dictionaryTable);
    };
    return {
      ...toRefs(state),
      formRef,
      handleCopy,
      handleChangeCopy,
      getPermissionBtn,
      formTableRef,
      handleChangeField,
      tableRef,
      isNewFixedView,
      isEditFixedView,
      isFixedView,
      isNewCustomView,
      isEditCustomView,
      isCustomView,
      viewFormRules,
      fieldTypes,
      columnFixedTypes,
      fieldTenantTypes,
      handleOpen,
      handleBeforeClose,
      handleClose,
      handleMenuKeysChange,
      confirmViewName,
      addNewField,
      removeField,
      handleChangeViewGroup,
      handleSelectionChange,
      resetView,
      saveView,
      rowDrop
    };
  }
};
</script>

<style>
.table-column-editer__label-colon {
  position: absolute;
  top: 0;
  right: 100%;
}

.table-column-editer__list.el-table .el-table__body-wrapper {
  max-height: calc(100vh - 421px);
  overflow-y: auto;
}

.table-column-editer__list .el-form-item {
  margin-bottom: 0 !important;
}

.table-column-editer__list .el-table-column--selection > .cell {
  padding-left: 5px;
  padding-right: 5px;
}
</style>
