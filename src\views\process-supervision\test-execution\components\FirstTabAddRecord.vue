<template>
  <!-- 原始记录模板  -->
  <div class="firstTabRaw h-full flex flex-col">
    <div class="btnGroup">
      <div class="btnGroupLeft">
        <el-button size="small" icon="el-icon-back" @click="onBack(1)">返回</el-button>
        <el-button
          v-if="!isCheck && jsonData.isower"
          size="small"
          icon="el-icon-plus"
          @click="handleAddDevice"
          @keyup.prevent
          @keydown.enter.prevent
          >仪器设备</el-button
        >
        <el-button
          v-if="!isCheck && jsonData.isower"
          size="small"
          icon="el-icon-upload2"
          @click="isShowPicture = true"
          @keyup.prevent
          @keydown.enter.prevent
          >试验图片</el-button
        >
        <el-dropdown trigger="hover">
          <el-button size="small" icon="el-icon-more" style="margin-left: 10px">更多操作</el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item
                v-if="!isCheck && jsonData.isower && getPermissionBtn('moduleReset')"
                @click="getresetList()"
              >
                <span class="blue"
                  ><span
                    class="el-icon-refresh-left"
                    style="margin-right: 5px"
                    @click="getresetList()"
                    @keyup.prevent
                    @keydown.enter.prevent
                  />模板重置</span
                >
              </el-dropdown-item>
              <el-dropdown-item v-if="!isCheck && jsonData.isower" @click="handleSafeguard()">
                <span class="blue"
                  ><span
                    class="el-icon-refresh"
                    style="margin-right: 5px"
                    @click="handleSafeguard()"
                    @keyup.prevent
                    @keydown.enter.prevent
                  />调整分组</span
                >
              </el-dropdown-item>
              <el-dropdown-item v-if="!isCheck && jsonData.isower" @click="chooseTypeSpecification()">
                <span class="blue"
                  ><span
                    class="el-icon-collection-tag"
                    style="margin-right: 5px"
                    @click="chooseTypeSpecification()"
                    @keyup.prevent
                    @keydown.enter.prevent
                  />型号规格</span
                >
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <el-switch
          v-if="!isCheck && jsonData.isUseStandard"
          v-model="isStandardCustom"
          active-text="自定义标准"
          :active-value="1"
          :inactive-value="0"
          inactive-text="系统标准"
          style="margin-left: 20px"
          :before-change="beforeSwitchChange"
        />
      </div>

      <div class="btnGroupRight">
        <el-button
          v-if="isCheck && getPermissionBtn('printBtn')"
          size="small"
          icon="el-icon-printer"
          @click="toImg()"
          @keyup.prevent
          @keydown.enter.prevent
          >打印模板</el-button
        >
        <el-button
          v-if="!isCheck && jsonData.isower"
          size="small"
          type="primary"
          icon="el-icon-receiving"
          @click="handleSubmit(0)"
          @keyup.prevent
          @keydown.enter.prevent
          >保存草稿</el-button
        >
        <el-button
          v-if="!isCheck && jsonData.isower"
          size="small"
          type="primary"
          icon="el-icon-circle-check"
          @click="handleSaveAndSubmit()"
          @keyup.prevent
          @keydown.enter.prevent
          >保存并提交</el-button
        >
      </div>
    </div>
    <div class="scroll-wrapper flex-1 h-full overflow-auto py-5">
      <div id="printMe" class="moban">
        <base-excel
          ref="excel"
          :parent-type="parentType"
          :json-data="jsonData"
          :is-standard-custom="isStandardCustom"
          @handleData="handleData"
        />
      </div>
    </div>
    <!--  设备管理-->
    <el-dialog
      v-model="dialogVisible"
      title="选择仪器设备"
      :width="1000"
      :close-on-click-modal="false"
      custom-class="dialog-table"
    >
      <el-button size="small" type="primary" icon="el-icon-plus" @click="addRow()">添加仪器设备</el-button>
      <el-table
        ref="multipleTable"
        :data="tableList"
        size="medium"
        fit
        border
        class="dark-table base-table"
        @select-all="handleSelectAll"
        @selection-change="handleSelectionChange"
        @select="handleToggleRowSelection"
        @row-click="handleRowClick"
      >
        <el-table-column type="selection" :width="colWidth.checkbox" label="全选" :selectable="rowSelect" />
        <el-table-column label="仪器设备" prop="yqsb">
          <template #default="{ row, $index }">
            <ul>
              <li>
                <span>编号：</span>
                <el-select
                  v-model="row.deviceNumber"
                  filterable
                  placeholder="请选择"
                  class="selectBh"
                  @change="
                    val => {
                      handleSelect(val, $index);
                    }
                  "
                >
                  <el-option-group v-for="item in deviceList" :key="item.label" :label="item.label">
                    <el-option
                      v-for="val in item.group"
                      :key="val.id"
                      :label="val.deviceNumber + val.name"
                      :value="val.deviceNumber"
                      :disabled="
                        selected.some(value => {
                          return value == val.deviceId;
                        }) ||
                        val.isOverdue ||
                        val.statusNo
                      "
                    >
                      <span style="float: left">{{ val.deviceNumber }}({{ val.name }})</span>
                      <span v-if="val.isOverdue" class="fr" style="color: red">已过期</span>
                      <span v-if="val.statusNo" class="fr" style="color: red">不可用</span>
                    </el-option>
                  </el-option-group>
                </el-select>
              </li>
              <li>
                <span>名称：</span><span class="value">{{ row.deviceName || '--' }}</span>
              </li>
              <li>
                <span>型号规格：</span><span class="value">{{ row.model || '--' }}</span>
              </li>
            </ul>
          </template>
        </el-table-column>
        <el-table-column label="检定有效期" :width="colWidth.date" prop="hdrq">
          <template #default="{ row }">
            <div>{{ row.validBeginDate || '--' }} ~ <br />{{ row.validEndDate || '--' }}</div>
          </template>
        </el-table-column>
        <el-table-column label="不确定度" :width="120" prop="measurementDeviation" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="nowrap">
              {{ row.measurementDeviation || '--' }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="使用情况" :width="280" prop="syqk">
          <template #default="{ row, $index }">
            <ul>
              <li>
                <span>使用人：</span><span class="value">{{ getNameByid(accountId) }}</span>
              </li>
              <li>
                <span>时间：</span>
                <el-date-picker
                  v-if="row.isCheck"
                  v-model="row.deviceUseTime"
                  :editable="false"
                  type="date"
                  :clearable="false"
                  placeholder="选择日期"
                  @change="
                    val => {
                      handleSelectDate(val, $index);
                    }
                  "
                />
                <span v-else>--</span>
              </li>
              <li>
                <span>状态：</span>
                <el-select v-if="row.isCheck" v-model="row.deviceStatusBefore" class="status" filterable>
                  <el-option v-for="(val, key) in dictionary" :key="key" :label="val" :value="key" />
                </el-select>
                <span v-else>--</span>
                <span class="fgline">/</span>
                <el-select v-if="row.isCheck" v-model="row.deviceStatusAfter" class="status" filterable>
                  <el-option v-for="(val, key) in dictionary" :key="key" :label="val" :value="key" />
                </el-select>
                <span v-else>--</span>
              </li>
            </ul>
          </template>
        </el-table-column>
        <el-table-column label="备注" prop="remark" :width="120">
          <template #default="{ row }">
            <el-input v-if="row.isCheck" v-model="row.remark" type="textarea" :rows="3" placeholder="请输入" />
            <span v-else>--</span>
          </template>
        </el-table-column>
      </el-table>
      <template #footer>
        <span class="dialog-footer">
          <el-button size="medium" @click="dialogVisible = false">取 消</el-button>
          <el-button size="medium" type="primary" @click="onSubmit" @keyup.prevent @keydown.enter.prevent
            >确 认</el-button
          >
        </span>
      </template>
    </el-dialog>
    <!-- 维护线芯弹窗-->
    <module-safeguard
      :json-data="jsonData"
      :dialog-sheath="dialogSheath"
      :is-capability="true"
      @closexx="closexx"
      @save-color="saveSampleColor"
    />
    <!-- 审核弹屏-->
    <module-audit
      :data-value="dataValue"
      :json-data="jsonData"
      :is-standard-custom="isStandardCustom"
      @closedialog="closedialog"
      @sumbitData="sumbitData"
      @sumbit-success="sumbitSuccess"
    />
    <AddTestPicture
      :show-picture="isShowPicture"
      :page-name="'tabAddRecord'"
      :query-info="queryInfo"
      @isCloseImg="closeDialogImg"
    />
    <!--    切换模板弹屏-->
    <el-dialog v-model="templateVisible" title="模板重置" :before-close="templateVisibleClose">
      <el-alert
        title="初始化后将重新加载最新模板，可能会造成部分数据丢失，请慎重选择。"
        type="warning"
        show-icon
        :closable="false"
      />
      <el-table size="medium" class="dark-table base-table" :data="templateList" @current-change="changeRadio">
        <el-table-column width="50">
          <template #default="{ row }">
            <el-radio v-model="currentRowId" :label="row.id" @change="changeRadio(row)">&nbsp;</el-radio>
          </template>
        </el-table-column>
        <el-table-column property="version" :width="200" label="版本">
          <template #default="{ row }">
            <div>
              <span>{{ row.version }}</span>
              <el-tag v-if="row.isCurrent === 1" size="small" style="margin-left: 8px"> 当前使用</el-tag>
              <el-tag v-if="row.isDefault === 1" size="small" style="margin-left: 8px"> 默认 </el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column property="description" label="版本描述" />
      </el-table>
      <template #footer>
        <span class="dialog-footer">
          <el-button size="medium" @click="templateVisibleClose">取 消</el-button>
          <el-button size="medium" type="primary" @click="innerVisible = true" @keyup.prevent @keydown.enter.prevent
            >确 认</el-button
          >
        </span>
      </template>
    </el-dialog>
    <!-- 选择型号规格 -->
    <DialogTypeSpecification
      :dialog-show="dialogSpecification"
      :json-data="jsonData"
      @closeDialog="closeDialogSpecification"
    />
    <!--    二次确认弹屏-->
    <el-dialog v-model="innerVisible" width="30%" title="模板初始化提醒" append-to-body>
      <div>初始化后将重新加载最新模板，可能会造成部分数据丢失，是否确认执行？</div>
      <template #footer>
        <span class="dialog-footer">
          <el-button size="medium" @click="innerVisible = false">取 消</el-button>
          <el-button size="medium" type="primary" @click="submitChange" @keyup.prevent @keydown.enter.prevent
            >确 认</el-button
          >
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { reactive, toRefs, getCurrentInstance, ref, nextTick, onMounted } from 'vue';
// import { useRoute } from 'vue-router'
import { getNameByid, getPermissionBtn } from '@/utils/common';
// import router from '@/router/index.js'
import { getLoginInfo } from '@/utils/auth';
import ModuleAudit from '@/components/BusinessComponents/ModuleAudit';
import pdf from '@/utils/preview-or-download-pdf';
// import html2canvas from 'html2canvas'
// import { samplesDetails } from '@/api/order'
// import { GetInventorySampleBySampleId, getName } from '@/api/login'
// import { formatDate } from '@/utils/formatTime'
import { ElMessage } from 'element-plus';
import baseExcel from '@/views/excelComponents/baseExcel';
import AddTestPicture from './addTestPicture';
import { colWidth } from '@/data/tableStyle';
import {
  experiment,
  experimentmongodatainfo,
  saveExcelInfo,
  TemplateIdByexperimentId,
  addDevice,
  downloadByCapabilityId,
  resetList,
  resetSave
} from '@/api/execution';
import { getDeviceList } from '@/api/sampleItemTest';
import { getCapabilityDevice, getDeviceCapabilityList } from '@/api/equipment';
import { formatDate } from '@/utils/formatTime';
import store from '@/store';
import _ from 'lodash';
import ModuleSafeguard from '@/components/BusinessComponents/ModuleSafeguard';
import DialogTypeSpecification from './DialogTypeSpecification';
import { decryptCBC } from '@/utils/ASE';
export default {
  name: 'FirstTabAddRecord',
  components: { baseExcel, AddTestPicture, ModuleAudit, ModuleSafeguard, DialogTypeSpecification },
  props: {
    queryInfo: {
      type: Object,
      default: function () {
        return {
          experimentId: '',
          samplesId: '',
          new: false,
          capabilityId: '',
          type: 'check'
        };
      }
    }
  },
  emits: ['showItemInfo'],
  setup(props, ctx) {
    const { proxy } = getCurrentInstance();
    // const route = useRoute()
    const ruleForm = ref('');
    const state = reactive({
      experimentId: '',
      dialogSpecification: false,
      isStandardCustom: 0,
      samplesId: '',
      disable: 'edit',
      new: true,
      innerVisible: false,
      templateVisible: false,
      templateList: [],
      currentRow: null,
      currentRowId: null,
      capabilityId: '',
      accountId: getLoginInfo().accountId,
      dialogVisible: false,
      experimentData: {},
      isShowPicture: false,
      jsonData: {},
      dataValue: {
        dialogSubmit: false
      },
      url: '',
      dialogSubmit: false,
      isCheck: true,
      formData: {
        realOwnerIds: [],
        reviewerId: '',
        date: []
      },
      dictionary: {
        1: '正常',
        3: '停用',
        0: '异常'
      },
      selected: [], // 已经添加的仪器设备的id集合
      multipleTable: ref(null),
      tableList: [],
      selectTable: [], // 选中的仪器设备表格数据
      userList: store.state.common.nameList,
      deviceList: [], // 仪器设备
      allDeviceList: [
        {
          // 所有仪器设备
          label: '可选择',
          group: []
        },
        {
          label: '已过期',
          group: []
        }
      ],
      dialogSheath: false,
      parentType: 1
    });
    const getdata = () => {};
    getdata();

    // 获取仪器设备编号
    const getEquipmentList = () => {
      getDeviceCapabilityList({ limit: '10000', page: '1' }).then(res => {
        state.allDeviceList[0].group = [];
        state.allDeviceList[1].group = [];
        if (res.data.data.list.length > 0) {
          res.data.data.list.forEach(item => {
            item.deviceId = item.id;
            item.deviceName = item.name;
            // 判断仪器设备检定日期是否已过期
            if (item.status === 'Running') {
              if (
                new Date(item.validBeginDate).getTime() < new Date().getTime() &&
                new Date(item.validEndDate).getTime() > new Date().getTime()
              ) {
                item.isOverdue = false;
                state.allDeviceList[0].group.push(item);
              } else if (!item.isEquipmentMetering) {
                item.isOverdue = false;
                state.allDeviceList[0].group.push(item);
              } else {
                item.isOverdue = true;
                state.allDeviceList[1].group.push(item);
              }
            } else {
              item.statusNo = true;
              state.allDeviceList[1].group.push(item);
            }
          });
          state.deviceList = JSON.parse(JSON.stringify(state.allDeviceList));
        }
      });
    };

    // 获取仪器设备表格
    const getDeviceTable = () => {
      // 获取已选中的数据
      getDeviceList(state.experimentId).then(res => {
        if (res.data.code === 200) {
          state.tableList = [];
          if (res.data.data.length > 0) {
            res.data.data.forEach(row => {
              row.isCheck = true;
              state.tableList.push(row);
            });
            state.selected = state.tableList.map(item => {
              return item.deviceId;
            });
            state.selectTable = res.data.data;
            nextTick(() => {
              if (state.dialogVisible) {
                state.tableList.forEach(row => {
                  state.multipleTable.toggleRowSelection(row, true);
                });
              }
            });
          } else {
            getItemRelation();
          }
          getall();
        }
      });
    };

    // 获取仪器设备表格
    const getDeviceTableAndRefresh = () => {
      // 获取已选中的数据
      getDeviceList(state.experimentId).then(res => {
        if (res.data.code === 200) {
          state.tableList = [];
          if (res.data.data.length > 0) {
            res.data.data.forEach(row => {
              row.isCheck = true;
              state.tableList.push(row);
            });
            state.selected = state.tableList.map(item => {
              return item.deviceId;
            });
            state.selectTable = res.data.data;
            nextTick(() => {
              if (state.dialogVisible) {
                state.tableList.forEach(row => {
                  state.multipleTable.toggleRowSelection(row, true);
                });
              }
            });
          } else {
            getItemRelation();
          }
          getall();
        }
      });
    };
    // 获取项目库相关数据
    const getItemRelation = () => {
      getCapabilityDevice(state.capabilityId).then(res => {
        if (res) {
          // 过滤重复的设备编号
          var tableJson = {};
          res.data.data.forEach(item => {
            const isShow =
              item.isEquipmentMetering &&
              new Date(item.validBeginDate).getTime() < new Date().getTime() &&
              new Date(item.validEndDate).getTime() > new Date().getTime();
            if (isShow || !item.isEquipmentMetering) {
              item.ownerId = getLoginInfo().accountId;
              item.deviceUseTime = formatDate(new Date());
              item.experimentid = props.queryInfo.experimentId;
              item.deviceStatusBefore = '1';
              item.deviceStatusAfter = '1';
              tableJson[item.deviceId] = item;
            }
          });
          state.tableList = Object.values(tableJson);
          state.selected = state.tableList.map(item => {
            return item.deviceId;
          });
        }
      });
    };

    const init1 = () => {
      return new Promise((resolve, reject) => {
        // 获取单个模板信息
        experiment(state.experimentId).then(res => {
          if (res.data.code === 200 && res.data.data) {
            // state.jsonData = { ...res.data.data }
            res.data.data.isower = _.indexOf(res.data.data.ownerIds.split(','), state.accountId) !== -1;
            resolve(res.data.data);
          } else {
            resolve(res.data.data);
            // reject('接口错误')
          }
        });
      });
    };

    const init2 = () => {
      return new Promise((resolve, reject) => {
        if (state.new) {
          downloadByCapabilityId({ capabilityId: state.capabilityId, samplesId: state.samplesId }).then(res => {
            if (res.data.code === 200) {
              resolve(res.data.data);
            } else {
              reject('接口错误');
            }
          });
        } else {
          TemplateIdByexperimentId(state.experimentId).then(res => {
            if (res.data.code === 200) {
              resolve(res.data.data);
            } else {
              reject('接口错误');
            }
          });
        }
      });
    };

    const getall = () => {
      Promise.all([init1(), init2()])
        .then(async allres => {
          // 两个都调成功以后执行的操作
          // 模板实验值信息
          await experimentmongodatainfo(state.experimentId).then(async resdata => {
            if (resdata) {
              const experimentData = resdata.data.data;
              experimentData.coreNumber = allres[1].coreNumber;
              state.jsonData = {
                ...allres[0],
                excelHtml: decryptCBC(allres[1].html),
                templateValue: allres[1].templateValue,
                fileNo: allres[1].fileNo,
                coreNumber: allres[1].coreNumber,
                showType: allres[1].showType,
                source: allres[1].source,
                isUseStandard: allres[1].isUseStandard,
                disabled: state.disable !== 'check',
                templateId: state.new ? allres[1].templateId : allres[0].templateId,
                experimentData: experimentData,
                realReviewerIdimg: experimentData.reviewerSignUrl.split(','),
                realOwnerIdsimgs: experimentData.ownerSignUrls.split(',')
              };
            }
          });
        })
        .catch(err => {
          // 抛出错误信息
          ElMessage(err);
        });
    };

    // // 获取签名
    // const getrealOwnerIdsimgs = async(ids) => {
    //   if (ids && ids !== undefined) {
    //     const ul = ids.split(',')
    //     const newPeopleInfoList = await store.dispatch('common/getSignatureImg', ul)
    //     return new Promise((resolve) => {
    //       resolve(newPeopleInfoList)
    //     })
    //   }
    // }

    // 保存
    const excel = ref(null);

    const handleData = thisValue => {
      state.experimentData = thisValue;
    };

    const sumbitData = () => {
      excel.value.handleData();
      // ctx.emit('showItemInfo', 2)
    };

    const sumbitSuccess = () => {
      excel.value.handleData();
      ctx.emit('showItemInfo', 3);
    };

    const handleSubmit = type => {
      const postdata = getExcelPostData();
      if (type === 0) {
        // 保存
        saveExcelInfo(postdata).then(res => {
          if (res) {
            ElMessage.success({
              message: '保存成功',
              type: 'success'
            });
            onBack();
          }
        });
      }
    };

    const handleSaveAndSubmit = type => {
      state.dataValue.dialogSubmit = true;
    };

    function getExcelPostData() {
      excel.value.handleData();
      var postdata = {};
      postdata.capabilityId = state.jsonData.capabilityId;
      postdata.experimentId = state.jsonData.experimentId;
      postdata.templateId = state.jsonData.templateId;
      postdata.samplesId = state.jsonData.samplesId;
      postdata.showType = state.jsonData.showType;
      postdata.source = state.jsonData.source;
      postdata.remark = '';
      postdata.saveColorNumber = 0;
      postdata.isUseStandard = state.jsonData.isUseStandard; // 是否是需要判定标准的模板
      postdata.isStandardCustom = state.isStandardCustom; // 判断是否自定义标准
      if (!state.jsonData.saveColorNumber) {
        if (state.jsonData.experimentData.coreRecord.coreColourList?.length > 0) {
          postdata.saveColorNumber = state.jsonData.experimentData.coreRecord.coreColourList.length;
        }
      }
      postdata.experimentData = state.experimentData;
      return postdata;
    }

    const handleSelectDate = (val, index) => {
      state.tableList[index].deviceUseTime = formatDate(val);
    };

    // 仪器设备表单选中的数据
    const handleSelectionChange = value => {
      state.selectTable = value;
    };

    // 仪器设备单选切换
    const handleToggleRowSelection = (selection, row) => {
      row.isCheck = !row.isCheck;
    };

    // 仪器设备全选和全不选的判断
    const handleSelectAll = datas => {
      if (datas.length > 0) {
        state.tableList.forEach(item => {
          item.isCheck = true;
        });
      } else {
        state.tableList.forEach(item => {
          item.isCheck = false;
        });
      }
    };

    // 仪器设备判断选框是否可以选择
    const rowSelect = (row, index) => {
      if (row.deviceId || row.deviceid) {
        return true;
      } else {
        return false;
      }
    };

    const handleSelect = (val, index) => {
      const addJson = state.deviceList[0].group.find(item => item.deviceNumber === val);
      state.tableList[index] = JSON.parse(JSON.stringify(addJson));
      state.tableList[index].experimentId = state.experimentId;
      state.tableList[index].deviceUseTime = formatDate(new Date());
      state.tableList[index].validBeginDate = formatDate(addJson.validBeginDate);
      state.tableList[index].validEndDate = formatDate(addJson.validEndDate);
      state.tableList[index].ownerId = state.accountId;
      state.tableList[index].deviceStatusBefore = '1';
      state.tableList[index].deviceStatusAfter = '1';
      state.tableList[index].isCheck = true;
      state.multipleTable.toggleRowSelection(state.tableList[index], true);
      state.selected = state.tableList.map(item => {
        return item.deviceId;
      });
    };

    function handleRowClick(row) {
      if (row && row.deviceNumber) {
        const rowIndex = state.tableList.findIndex(item => item.id === row.id);
        if (rowIndex !== -1) {
          row.isCheck = !row.isCheck;
          state.multipleTable.toggleRowSelection(state.tableList[rowIndex], row.isCheck);
        }
      }
    }

    const onSubmit = () => {
      // 提交仪器设备
      if (state.selectTable.length > 0) {
        const params = {
          experimentId: props.queryInfo.experimentId,
          samplesId: props.queryInfo.samplesId,
          devices: state.selectTable
        };
        addDevice(params).then(res => {
          if (res) {
            proxy.$message.success('操作成功');
            state.dialogVisible = false;
            const postdata = getExcelPostData();
            saveExcelInfo(postdata).then(res => {
              if (res) {
                getDeviceTableAndRefresh();
              }
            });
          }
        });
      } else {
        // proxy.$message.closeAll()
        proxy.$message.error('请至少勾选一条数据');
      }
    };

    // 仪器设备选择框筛选
    const handleFilter = val => {
      if (val) {
        const newData1 = state.allDeviceList[0].group.filter(item => {
          return item.name.indexOf(val) > -1 || item.deviceNumber.indexOf(val) > -1;
        });
        const newData2 = state.allDeviceList[1].grouaddRowp.filter(item => {
          return item.name.indexOf(val) > -1 || item.deviceNumber.indexOf(val) > -1;
        });
        state.deviceList[0].group = JSON.parse(JSON.stringify(newData1));
        state.deviceList[1].group = JSON.parse(JSON.stringify(newData2));
      } else {
        state.deviceList = JSON.parse(JSON.stringify(state.allDeviceList));
      }
    };

    const addRow = () => {
      state.tableList.push({
        deviceStatusBefore: '',
        deviceStatusAfter: ''
      });
    };

    const deleteRow = index => {
      state.tableList.splice(index, 1);
      state.selected = state.tableList.map(item => {
        return item.deviceId;
      });
    };

    const onBack = val => {
      ctx.emit('showItemInfo', 1);
    };

    const closedialog = () => {
      state.dataValue.dialogSubmit = false;
      // ctx.emit('getdata')
    };

    const closeDialogImg = () => {
      state.isShowPicture = false;
    };

    const handleAddDevice = () => {
      state.dialogVisible = true;
      getDeviceTable();
    };

    // 转图片打印
    const toImg = () => {
      // 转图片打印
      pdf.PageImg(state.jsonData?.showType);
    };

    const getresetList = () => {
      const data = {
        experimentId: state.experimentId,
        capabilityId: state.jsonData.capabilityId
      };
      resetList(data).then(res => {
        if (res.data.data) {
          state.templateList = res.data.data;
          state.templateVisible = true;
        }
      });
    };

    const changeRadio = val => {
      if (val && val.id) {
        state.currentRow = val;
        state.currentRowId = val.id;
      }
    };

    const submitChange = () => {
      const data = {
        experimentId: state.experimentId,
        templateId: state.currentRow.id
      };
      resetSave(data).then(async res => {
        state.jsonData = {
          ...state.jsonData,
          templateId: state.currentRow.id,
          showType: state.currentRow.showType,
          experimentData: res.data.data,
          excelHtml: decryptCBC(res.data.data.html)
        };
        nextTick(() => {
          excel.value.setData();
        });
        templateVisibleClose();
        ElMessage.success({
          message: '提交成功',
          type: 'success'
        });
      });
    };

    const templateVisibleClose = () => {
      state.templateVisible = false;
      state.currentRowId = null;
      state.innerVisible = false;
    };

    // 线芯维护弹屏
    const handleSafeguard = () => {
      state.dialogSheath = true;
    };

    const closexx = () => {
      state.dialogSheath = false;
    };

    const saveSampleColor = colorList => {
      state.jsonData.experimentData.saveColorNumber = colorList.length;
      state.jsonData.experimentData.coreRecord.coreColourList = colorList;
    };

    // watch(props, (newValue) => {
    //   if (newValue.queryInfo?.experimentId) {
    //     state.experimentId = props.queryInfo.experimentId
    //     state.capabilityId = props.queryInfo.capabilityId
    //     state.samplesId = props.queryInfo.samplesId
    //     state.new = props.queryInfo.new
    //     state.disable = props.queryInfo.type
    //     state.isCheck = props.queryInfo.type === 'check'
    //   }
    // }, { deep: true })

    onMounted(() => {
      nextTick(() => {
        if (props.queryInfo?.experimentId) {
          state.experimentId = props.queryInfo.experimentId;
          state.capabilityId = props.queryInfo.capabilityId;
          state.samplesId = props.queryInfo.samplesId;
          state.new = props.queryInfo.new;
          state.disable = props.queryInfo.type;
          state.isCheck = props.queryInfo.type === 'check';
          state.parentType = props.queryInfo.type === 'check' ? 2 : 1;
          getDeviceTableAndRefresh();
          getEquipmentList();
          getall();
        }
      });
    });

    // 切换标准取值自定义或系统定义
    const beforeSwitchChange = val => {
      return new Promise((resolve, reject) => {
        return resolve(true);
        // ElMessageBox({
        //   title: '提示',
        //   message: '切换标准定义会导致模板刷新',
        //   confirmButtonText: '继续',
        //   cancelButtonText: '取消',
        //   showCancelButton: true,
        //   closeOnClickModal: false,
        //   type: 'info'
        // }).then(() => {
        //   return resolve(true)
        // }).catch(() => {
        //   return resolve(false)
        // })
      });
    };
    // 型号规格弹出框
    const chooseTypeSpecification = () => {
      state.dialogSpecification = true;
    };
    // 关闭型号规格
    const closeDialogSpecification = val => {
      if (val) {
        state.jsonData.experimentProdTypeList = val;
      }
      state.dialogSpecification = false;
    };

    return {
      ...toRefs(state),
      excel,
      beforeSwitchChange,
      chooseTypeSpecification,
      closeDialogSpecification,
      ruleForm,
      colWidth,
      handleFilter,
      templateVisibleClose,
      getresetList,
      changeRadio,
      submitChange,
      handleSelectionChange,
      handleToggleRowSelection,
      handleSelectAll,
      toImg,
      rowSelect,
      closedialog,
      getPermissionBtn,
      getdata,
      getEquipmentList,
      getItemRelation,
      handleData,
      sumbitData,
      sumbitSuccess,
      handleSubmit,
      handleSaveAndSubmit,
      handleAddDevice,
      getDeviceTableAndRefresh,
      onSubmit,
      addRow,
      deleteRow,
      handleSelect,
      handleSelectDate,
      onBack,
      getLoginInfo,
      closeDialogImg,
      getNameByid,
      handleSafeguard,
      closexx,
      saveSampleColor,
      handleRowClick
    };
  },
  computed: {},
  created() {}
};
</script>

<style lang="scss" scoped>
.firstTabRaw {
  padding-bottom: 10px;
  position: relative;
  :deep(.el-select--medium),
  :deep(.el-range-editor.el-input__inner) {
    width: 100%;
  }
  :deep(.el-form .el-form-item) {
    margin-bottom: 20px;
  }
  .btnGroup {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 20px;
  }
  .moban {
    min-height: 500px;
    margin: 0 auto;
  }
}

.dialog-table {
  .selectBh {
    width: 60%;
  }
}

.fgline {
  margin: 0 5px;
}
ul {
  padding: 0;
}
li {
  list-style: none;
  margin: 8px 0;
}
.base-table {
  margin-top: 12px;
  .status.el-select--medium {
    width: 30%;
  }
  :deep(.el-input--medium .el-input__icon) {
    line-height: 24px;
  }
  :deep(.el-date-editor.el-input, .el-date-editor.el-input__inner) {
    width: 64.5%;
  }
  :deep(.el-input--medium .el-input__inner) {
    height: 24px;
    line-height: 24px;
  }
  :deep(.el-select--medium),
  :deep(.el-input--medium) {
    line-height: 24px;
  }
}

.addRecord {
  margin: 0 1rem;
  padding: 10px 14px 10px 1rem;
  :deep(.el-form .el-form-item) {
    margin-bottom: 20px;
  }
  :deep(.dialogRecord .el-dialog__body) {
    padding-top: 35px;
    min-height: 500px;
  }
}
</style>
