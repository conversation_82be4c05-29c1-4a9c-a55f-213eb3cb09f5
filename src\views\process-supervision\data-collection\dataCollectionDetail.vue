<template>
  <!-- 数据采集详情页 -->
  <ListLayout
    :has-custom-header="true"
    :has-quick-query="false"
    :has-search-panel="false"
    :has-left-panel="true"
    :main-offset-top="124"
    :aside-panel-width="400"
    :aside-max-width="520"
    aside-height=""
  >
    <template #page-custom-header>
      <el-form :model="ruleForm" label-position="left" class="collection-form">
        <el-form-item label-width="0">
          <div class="full-item">
            <div class="title">
              <span class="iconfont tes-Vector1 icon-status" />
              {{ deviceInfo.deviceNumber }} <span class="space">-</span>
              {{ deviceInfo.deviceName }}
            </div>
            <div class="right-group">
              <div v-if="jsonData && jsonData.excelHtml && jsonData.isower && tableListNew.length > 0">
                <div v-show="!inProgress">
                  <el-button
                    v-if="getPermissionBtn('inforSupplementCollection')"
                    size="small"
                    @click="handleSupplement()"
                    @keyup.prevent
                    @keydown.enter.prevent
                    >样品信息补充</el-button
                  >
                  <el-button
                    v-if="getPermissionBtn('realTimeAcquisition')"
                    size="small"
                    @click="showRealTimeDataDialog"
                    @keyup.prevent
                    @keydown.enter.prevent
                    >实时采集</el-button
                  >
                  <el-button size="small" @click="handleHistoryData" @keyup.prevent @keydown.enter.prevent
                    >历史数据</el-button
                  >
                  <el-button
                    v-if="getPermissionBtn('manualEntry')"
                    size="small"
                    @click="goOut"
                    @keyup.prevent
                    @keydown.enter.prevent
                    >手动录入</el-button
                  >
                  <el-button
                    v-if="getPermissionBtn('submitCollectionBtn')"
                    size="small"
                    type="primary"
                    @click="handleSumbit"
                    @keyup.prevent
                    @keydown.enter.prevent
                    >提 交</el-button
                  >
                </div>
                <div v-show="inProgress">
                  <label class="item-label" for="start-time">开始时间：</label>
                  <el-date-picker
                    v-model="startDateTime"
                    name="start-time"
                    type="datetime"
                    size="small"
                    placeholder="实时数采开始时间"
                    :readonly="disabledTimePicker"
                  />
                  &nbsp;&nbsp;
                  <el-button size="small" type="danger" @click="stopRealTimeData" @keyup.prevent @keydown.enter.prevent
                    >停止采集</el-button
                  >
                </div>
              </div>
            </div>
          </div>
        </el-form-item>
        <el-form-item v-show="!inProgress" label="数采项目：" prop="activeItem">
          <el-radio-group v-model="activeItem" class="radio-item-content" @change="handleChangeTestItem">
            <el-radio v-for="(item, index) in itemList" :key="index" :label="item.id">
              {{ item.name }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-show="inProgress && !isCustomWrite" label="数采进度：" class="form-item-progress">
          <el-progress :text-inside="true" :stroke-width="16" :percentage="realTimeProgress" />
        </el-form-item>
        <el-form-item v-show="inProgress && isCustomWrite" label="自定义采集中：" class="form-item-progress">
          <span>当前已使用数据：{{ customConsumeIndex }}, 已接受数据：{{ selectiondata.length }}</span>
        </el-form-item>
      </el-form>
    </template>
    <template #page-left-side>
      <div class="left-list">
        <el-input
          ref="inputRef"
          v-model="search"
          prefix-icon="el-icon-search"
          size="small"
          class="ipt-search"
          clearable
          placeholder="请输入样品编号"
        />
        <el-table
          ref="tableRef"
          v-loading="loading"
          class="dark-table table-content"
          fit
          border
          height="auto"
          :data="tableListNew"
          highlight-current-row
          @current-change="handleCurrentChange"
        >
          <el-table-column type="index" label="序号" :width="colWidth.serialNo" align="center" />
          <el-table-column property="secsamplenum" show-overflow-tooltip label="样品编号" :width="colWidth.orderNo">
            <template #default="{ row }">
              <span v-if="row.isUrgent" class="urgent">急</span>
              <span>{{ row.secsamplenum }}</span>
            </template>
          </el-table-column>
          <el-table-column property="samplename" show-overflow-tooltip label="样品名称" :min-width="colWidth.name" />
        </el-table>
      </div>
    </template>
    <el-row>
      <el-col :span="inProgress ? 17 : 24">
        <div class="right-box">
          <div
            v-if="jsonData && jsonData.excelHtml && tableListNew.length > 0"
            v-loading="loading"
            class="template-box"
          >
            <base-excel ref="excel" :json-data="jsonData" @handleData="handleData" />
          </div>
          <div v-else class="empty-box">
            <el-empty :image="emptyImg" description="该项目没有绑定模板" />
            <el-button
              v-if="jsonData.isower && tableListNew.length > 0"
              type="primary"
              size="small"
              @click="handleAddRecord"
              @keyup.prevent
              @keydown.enter.prevent
              >添加原始记录模板</el-button
            >
          </div>
        </div>
      </el-col>
      <el-col v-show="inProgress" :span="1" />
      <el-col v-show="inProgress" :span="6">
        <div class="group-wrapper">
          <el-input
            ref="groupSearchRef"
            v-model="groupSearch"
            prefix-icon="el-icon-search"
            size="small"
            class="group-search"
            clearable
            placeholder="请输入试样分组"
            @change="groupChange"
          />
          <div class="color-table-wrapper">
            <el-table
              id="color-table"
              ref="colorTableRef"
              :data="colorList"
              fit
              :border="false"
              height="100%"
              class="dark-table"
              scrollbar-always-on
              :highlight-current-row="isCustomWrite"
              @selection-change="handleSelectionChange"
              @current-change="changeRadio"
            >
              <el-table-column v-if="false" type="selection" :width="55" />
              <el-table-column type="index" label="选择" width="50" align="center">
                <template #default="{ row }">
                  <el-radio v-model="row.radio" :disabled="!isCustomWrite" :label="row.id" @change="changeRadio(row)">{{
                    ''
                  }}</el-radio>
                </template>
              </el-table-column>
              <el-table-column label="序号" type="index" width="50" />
              <el-table-column label="试样分组" :min-width="75">
                <template #default="{ row }">
                  <span>{{ row.coreColour }}</span>
                </template>
              </el-table-column>
              <el-table-column label="当前状态" :min-width="65">
                <template #default="{ row }">
                  <el-tag size="small" :type="filterStatus(row.status)[0]">
                    {{ filterStatus(row.status)[1] }}
                  </el-tag>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <QrcodeVue
            v-if="writeMethod === 2"
            :value="`deviceNumber=${deviceInfo.deviceNumber}&sampleId=${jsonData.sampleId}&capabilityId=${jsonData.capabilityId}&name=${deviceInfo.deviceName}&startDateTime=${startDateTime}&wsUrl=${wsUrl}`"
            :size="150"
          />
        </div>
      </el-col>
    </el-row>

    <real-time-data-dialog
      ref="realTimeDataDialog"
      :dialog-visible="realTimeDialogVisible"
      :active-capability-item="activeItem"
      :wire-color-list="colorList"
      :wire-core-exist-info="coreExistInfo"
      :wire-color-radio="colorRadio"
      @close="closeRealTimeDialog"
      @change-color="changeRadioColor"
      @start-real-time="startRealTimeData"
    />
    <history-data-dialog
      ref="historyDataDialog"
      :dialog-visible="historyDialogVisible"
      :active-sample-row="activeRow"
      :active-capability-item="activeItem"
      :wire-color-list="colorList"
      :wire-core-exist-info="coreExistInfo"
      :wire-color-radio="colorRadio"
      @close="closeHistoryDialog"
      @confirm-selected="confirmSelectedData"
      @change-color="changeRadioColor"
    />
    <!--    提交-->
    <module-audit :data-value="dataValue" :json-data="jsonData" @closedialog="closedialog" @sumbitData="sumbitData" />
    <!-- 线芯标识-->
    <module-safeguard :json-data="jsonData" :dialog-sheath="dialogSheath" :show-tip="true" @closexx="closexx" />
    <!-- 样品信息补充 -->
    <DialogSupplement
      :show="dialogSupplement"
      :sample-id="jsonData.sampleId"
      :data="{ secSampleNum: jsonData.secSampleNum, prodType: jsonData.prodType, samplesName: jsonData.samplesName }"
      :material-category-code="jsonData.mateType"
      @closeDialog="handleCloseDialog"
    />
  </ListLayout>
</template>

<script>
import { computed, getCurrentInstance, reactive, ref, toRefs, watch, nextTick, onMounted, onUnmounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useRoute } from 'vue-router';
import _ from 'lodash';
import { getLoginInfo } from '@/utils/auth';
import { deviceByInfo, findCapabilityByDeviceId, startRdsData, stopRdsData } from '@/api/datacollection';
import { getDetail } from '@/api/equipment';
import {
  experiment,
  experimentmongodatainfo,
  saveExcelInfo,
  TemplateIdByexperimentId,
  addDevice,
  getInfoByExperimentId,
  downloadByCapabilityId,
  getInfoBySampleCapabilityId
} from '@/api/execution';
import router from '@/router';
import { formatDate, formatDateTime } from '@/utils/formatTime';
import baseExcel from '@/views/excelComponents/baseExcel';
import ModuleAudit from '@/components/BusinessComponents/ModuleAudit';
import ModuleSafeguard from '@/components/BusinessComponents/ModuleSafeguard';
import ListLayout from '@/components/ListLayout';
import HistoryDataDialog from './components/HistoryDataDialog.vue';
import RealTimeDataDialog from './components/RealTimeDataDialog.vue';
import { storeToDB, getListByDate, deleteListByDate } from '@/utils/helper/idbHelper';
// import { clearInterval, setInterval } from 'timers'
import bus from '@/bus';
import { colWidth } from '@/data/tableStyle';
import { addBrowserClosePrompt, removeBrowserClosePrompt } from '@/utils/browser';
import { mapGetters } from 'vuex';
import QrcodeVue from 'qrcode.vue';
import DialogSupplement from '@/components/DialogSupplement';
import { formatWebsocketTime } from '@/utils/formatTime';
import { getPermissionBtn } from '@/utils/common';
import { debounce } from 'lodash';
import emptyImg from '@/assets/img/empty-template.png';
import { decryptCBC } from '@/utils/ASE';
import { getWebSocketURL } from '@/utils/base-url';

export default {
  name: 'DataCollectionDetail',
  components: {
    baseExcel,
    ListLayout,
    ModuleAudit,
    ModuleSafeguard,
    HistoryDataDialog,
    RealTimeDataDialog,
    DialogSupplement,
    QrcodeVue
  },
  setup() {
    const { proxy } = getCurrentInstance();
    const route = useRoute();
    bus.startRds = false;
    // const lodash = inject('_')
    const state = reactive({
      dialogSheath: false,
      ruleForm: {
        Resource: ''
      },
      isnew: false,
      dialogSupplement: false,
      wsUrl: window.location.hostname === 'localhost' ? '*************' : window.location.host,
      isOpenWebSocket: false,
      inputRef: ref(),
      coreExistInfo: {},
      activeRow: {},
      colorRadio: 0,
      dataValue: {
        dialogSubmit: false
      },
      colorList: [],
      experimentData: {},
      tableRef: ref(),
      experimentId: '',
      jsonData: {},
      value2: '',
      accountId: getLoginInfo().accountId,
      leftTable: [],
      activeItem: '',
      deviceInfo: {
        deviceId: route.query.id,
        deviceNumber: route.query.deviceNumber,
        deviceName: route.query.name,
        id: route.query.id
      },
      search: '',
      tableList: [],
      selectiondata: [],
      loading: false,
      devices: [],
      detailData: [],
      itemList: [],
      historyDialogVisible: false,
      realTimeDialogVisible: false,
      startDateTime: formatDateTime(new Date()),
      inProgress: false,
      stopRealTimeKey: '',
      endIntervalId: null,
      realIntervalId: null,
      disabledTimePicker: true,
      realTimeProgress: 0,
      sampleGroupNumber: 0,
      currentColor: 0,
      groupSearch: '',
      searchGroupList: [],
      writeMethod: 1,
      selectedRow: null,
      customConsumeIndex: 0,
      isInConsume: false,
      colorTableRef: ref(),
      dataCollection: []
    });

    const dataAcquisitionWebSocket = ref();
    nextTick(() => {
      state.inputRef.focus();
    });

    onMounted(() => {
      websocketOpen();
    });

    const websocketOpen = () => {
      dataAcquisitionWebSocket.value = new WebSocket(
        `${getWebSocketURL()}/${getLoginInfo().accountId}/${formatWebsocketTime(60000)}`
      );
    };

    const websocketStart = data => {
      const newColorList = [];
      data.forEach(item => {
        newColorList.push({
          id: item.id,
          coreColour: item.coreColour,
          coreColourSuffix: item.coreColourSuffix,
          state: item.status
        });
      });
      const params = {
        header: {
          cmd: 2
        },
        body: {
          deviceNumber: state.deviceInfo.deviceNumber,
          sampleId: state.jsonData.sampleId,
          capabilityId: state.jsonData.capabilityId,
          optType: 1,
          list: newColorList
        }
      };
      webSocketMsg(params);
      state.isOpenWebSocket = true;
      // if (state.isOpenWebSocket) {
      //   // 如果websocket已经打开
      //   webSocketMsg(params)
      // } else {
      //   dataAcquisitionWebSocket.value.open = () => {
      //     webSocketMsg(params)
      //   }
      // }
    };
    // websocket 接收数据
    const webSocketMsg = params => {
      dataAcquisitionWebSocket.value.send(JSON.stringify(params));
      dataAcquisitionWebSocket.value.onmessage = msg => {
        const data = msg.data;
        const dataString = data.toString();
        if (dataString.startsWith('{"header":{"cmd":2}')) {
          const bodyData = JSON.parse(data).body;
          if (bodyData.isFinished && state.inProgress) {
            // 如果是移动端停止的采集，则PC端调用停止采集的接口
            stopRealTimeInterval();
          } else {
            const dataList = bodyData.list;
            dataList.forEach((item, index) => {
              state.colorList[index].status = item.state;
            });
            const seleRow = state.colorList.filter(item => {
              return item.status === 1;
            })[0];
            changeRadio(seleRow, true);
          }
        }
        if (dataString.startsWith('rdsData:')) {
          console.log('==========start=========');
          console.log('dataAcquisitionWebSocket', formatDateTime(new Date()), data);
          console.log('===========end==========');
          // storeToDB('DataCollection', 'DataCollection', data)
          state.dataCollection.push(data);
        }
        if (dataString.startsWith('rdsData--')) {
          storeToDB('BeginEndLog', 'BeginEndLog', data);
        }
      };
    };
    const websocketClose = () => {
      const params = {
        header: {
          cmd: 2
        },
        body: {
          deviceNumber: state.deviceInfo.deviceNumber,
          sampleId: state.jsonData.sampleId,
          capabilityId: state.jsonData.capabilityId,
          optType: -1
        }
      };
      dataAcquisitionWebSocket.value.send(JSON.stringify(params));
      state.dataCollection = [];
      state.isOpenWebSocket = false;
    };
    onUnmounted(() => {
      websocketClose();
    });
    /**
     * 点击提交按钮，打开提交审核对话框
     */
    const handleSumbit = () => {
      state.dataValue.dialogSubmit = true;
    };

    /**
     * 提交审核对话框，确认提交
     */
    const sumbitData = () => {
      excel.value.handleData();
    };

    /**
     * 关闭提交审核对话框
     */
    const closedialog = i => {
      state.dataValue.dialogSubmit = false;
      if (i) {
        handleChangeTestItem(state.activeItem);
      }
    };

    /**
     * 获取当前项目下样品信息
     */
    const getCurrentItemSamplesInfo = () => {
      state.loading = true;
      deviceByInfo(state.activeItem).then(res => {
        state.leftTable = res.data.data.filter(x => x.ownerid.toString().split(',').includes(state.accountId));
        if (state.leftTable.length !== 0) {
          state.experimentId = state.leftTable[0].id;
          if (route.query.secsamplenum) {
            const index = state.leftTable.findIndex(item => {
              return item.secsamplenum === route.query.secsamplenum;
            });
            state.tableRef.setCurrentRow(state.leftTable[index]);
          } else {
            state.tableRef.setCurrentRow(state.leftTable[0]);
          }
          state.leftTable.forEach(it => {
            if (it.id === state.activeRow.id) {
              state.tableRef.setCurrentRow(state.activeRow);
            }
          });
          getall();
        }
        state.loading = false;
      });
    };

    /**
     * 切换检测项目
     */
    const handleChangeTestItem = val => {
      state.activeItem = val;
      console.log(val);
      getCurrentItemSamplesInfo();
    };

    // 左侧样品列表

    /**
     * 左侧样品列表数据
     */
    const tableListNew = computed(() => {
      return state.leftTable.filter(
        data => !state.search || data.secsamplenum.toLowerCase().includes(state.search.toLowerCase())
      );
    });

    const getCurrentRowData = row => {
      if (row && row.ownerid) {
        state.activeRow = row;
        state.experimentId = row.id;
        state.tableRef.setCurrentRow(row);
        state.jsonData.isower = _.indexOf(row.ownerid.split(','), state.accountId) !== -1;
        getall();
      }
    };

    // 原始记录模板

    // 取模板数据
    const excel = ref(null);
    const handleData = thisValue => {
      state.experimentData = thisValue;
    };

    // 点击下载模板
    const handleCurrentChange = async row => {
      if (state.inProgress) {
        await ElMessageBox({
          title: '提示',
          message: '离开此页面会停止实时数采，是否确认离开？',
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          showCancelButton: true,
          closeOnClickModal: false,
          type: 'info'
        })
          .then(() => {
            stopRealTimeInterval();
            websocketClose();
            getCurrentRowData(row);
            return true;
          })
          .catch(() => {
            return false;
          });
      } else {
        getCurrentRowData(row);
      }
    };

    // 添加原始记录
    const handleAddRecord = () => {
      const infoPostdata = {};
      infoPostdata.capabilityId = state.jsonData.capabilityId;
      infoPostdata.experimentId = state.jsonData.experimentId;
      infoPostdata.samplesId = state.jsonData.samplesId;
      getInfoByExperimentId({
        sampleId: infoPostdata.samplesId,
        capabilityId: infoPostdata.capabilityId
      }).then(res => {
        const info = res.data.data;
        state.coreExistInfo = res.data.data;
        // 判断是否需要维护线芯
        if (info.coreExist) {
          // 判断是否维护了线芯
          if (info.coreColour) {
            add(infoPostdata);
          } else {
            handleSafeguard();
          }
        } else {
          add(infoPostdata);
        }
      });
      return false;
    };

    // 线芯维护弹屏
    const handleSafeguard = () => {
      state.dialogSheath = true;
    };

    const closexx = () => {
      state.dialogSheath = false;
      getCurrentRowData(state.activeRow);
    };

    // 绑定模板
    const add = templatePostdata => {
      const params = JSON.parse(JSON.stringify(state.jsonData));
      state.jsonData = {};
      downloadByCapabilityId(templatePostdata).then(res => {
        if (res) {
          state.isnew = true;
          state.jsonData = {
            ...params,
            excelHtml: decryptCBC(res.data.data.html),
            templateValue: res.data.data.templateValue,
            fileNo: res.data.data.fileNo,
            templateId: res.data.data.templateId,
            showType: res.data.data.showType,
            source: res.data.data.source
          };
        }
      });
    };

    // 数采基本数据

    /**
     * 获取线芯数据的列表
     */
    const getColorList = () => {
      state.customConsumeIndex = 0;
      getInfoBySampleCapabilityId({
        sampleId: state.jsonData.samplesId,
        capabilityId: state.jsonData.capabilityId
      }).then(res => {
        const info = res.data.data;
        state.coreExistInfo = res.data.data;
        let thisColors = [];
        if (info.coreColourList.length) {
          thisColors = JSON.parse(JSON.stringify(info.coreColourList));
        } else {
          thisColors = state.jsonData.experimentData.coreRecord.coreColourList;
        }
        thisColors.forEach(item => {
          item.radio = false;
          item.status = 0;
        });
        state.colorRadio = 0;
        state.colorList = thisColors;
      });
    };

    const init1 = () => {
      return new Promise((resolve, reject) => {
        // 获取单个模板信息
        experiment(state.experimentId).then(res => {
          if (res.data.code === 200 && res.data.data) {
            // state.jsonData = { ...res.data.data }
            res.data.data.isower = _.indexOf(res.data.data.ownerIds.split(','), state.accountId) !== -1;
            resolve(res.data.data);
            state.isnew = false;
          } else {
            resolve(res.data.data);
            // reject('接口错误')
          }
        });
      });
    };

    const init2 = () => {
      return new Promise((resolve, reject) => {
        TemplateIdByexperimentId(state.experimentId).then(res => {
          if (res.data?.code === 200) {
            resolve(res.data.data);
            state.sampleGroupNumber = res.data.data.groupNumber;
          } else {
            reject('接口错误');
          }
        });
      });
    };

    const getall = () => {
      Promise.all([init1(), init2()])
        .then(async allres => {
          // 两个都调成功以后执行的操作
          // 模板实验值信息
          await experimentmongodatainfo(state.experimentId).then(async resdata => {
            if (resdata.data.code === 200) {
              state.jsonData = {
                ...allres[0],
                excelHtml: decryptCBC(allres[1].html),
                templateValue: allres[1].templateValue,
                fileNo: allres[1].fileNo,
                disabled: state.disable !== 'check',
                templateId: state.new ? allres[1].templateId : allres[0].templateId,
                experimentData: resdata.data.data
              };
              state.devices = state.jsonData.experimentData.devices;
            }
          });
        })
        .catch(err => {
          // 抛出错误信息
          console.log(err);
          // ElMessage(err)
        });
    };

    // #endregion

    // #region 实时数据采集

    const showRealTimeDataDialog = () => {
      // const startDate = new Date()
      // startDate.setTime(startDate.getTime() - 2000)
      // const startDateTime = formatDateTime(startDate)
      // storeToDB(
      //   'DataCollection',
      //   'DataCollection',
      //   'rdsData:{"data":[]}',
      //   startDateTime
      // )
      getColorList();
      state.selectiondata = [];
      state.realTimeProgress = 0;
      state.realTimeDialogVisible = true;
    };

    const startRealTimeData = info => {
      if (state.inProgress) {
        return;
      }
      state.isInConsume = true;
      setCurrentColorRow(state.colorRadio);
      state.startDateTime = info.startDateTime;
      state.writeMethod = info.writeMethod;
      state.currentColor = info.colorRadio;
      state.dataCollection = [];
      storeToDB('BeginEndLog', 'BeginEndLog', `rdsData--数据采集开始:${state.startDateTime}`);
      deleteListByDate('BeginEndLog', 'BeginEndLog');
      // deleteListByDate('DataCollection', 'DataCollection')
      bus.startRds = true;
      state.inProgress = true;
      const rdsPostBody = {
        capabilityId: state.activeItem, // 检测项目id
        deviceNo: route.query.deviceNumber,
        secSampleNum: '',
        startDate: state.startDateTime,
        endDate: '',
        sampleId: state.jsonData.samplesId
      };
      startRdsData(rdsPostBody).then(resp => {
        if (resp.data.code === 200) {
          websocketStart(state.colorList);
          state.stopRealTimeKey = resp.data.data;
          if (!state.realIntervalId) {
            state.realIntervalId = setInterval(getRdsDataFromIdb, 3000);
          }
          if (!state.endIntervalId) {
            state.endIntervalId = setInterval(checkServer, 3000);
          }
        }
      });
    };

    const checkServer = () => {
      getListByDate('BeginEndLog', 'BeginEndLog', state.startDateTime).then(resp => {
        if (resp) {
          if (resp.length > 1) {
            ElMessage.success({
              message: '服务端采集已结束!',
              type: 'message'
            });
            stopRealTimeInterval();
            websocketClose();
          }
        }
      });
    };

    const getRdsDataFromIdb = async () => {
      // await getListByDate(
      //   'DataCollection',
      //   'DataCollection',
      //   state.startDateTime
      // ).then(resp => {
      //   if (resp) {
      //     resp.forEach((item, index) => {
      //       const oneData = JSON.parse(item.body.toString().substring(8)).data
      //       const dataArray = []
      //       oneData.forEach((item, i) => {
      //         dataArray[i] = item.partVos
      //       })
      //       state.selectiondata[index] = dataArray
      //     })
      //   }
      // })
      state.dataCollection.forEach((item, index) => {
        const oneData = JSON.parse(item.toString().substring(8)).data;
        const dataArray = [];
        oneData.forEach((item, i) => {
          dataArray[i] = item.partVos;
        });
        state.selectiondata[index] = dataArray;
      });
    };

    const stopRealTimeData = () => {
      ElMessageBox.confirm('确定要停止实时数采?', '停止实时数采', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          stopRealTimeInterval();
          websocketClose();
        })
        .catch(() => {});
    };

    const stopRealTimeInterval = (refresh = true) => {
      if (state.stopRealTimeKey) {
        stopRdsData(state.stopRealTimeKey, route.query.deviceNumber).then(resp => {
          if (resp.data.code === 200) {
            bus.startRds = false;
            if (state.realIntervalId) {
              clearInterval(state.realIntervalId);
            }
            if (state.endIntervalId) {
              clearInterval(state.endIntervalId);
            }
            state.endIntervalId = null;
            state.realIntervalId = null;
            state.inProgress = false;
            if (refresh) {
              getRdsDataFromIdb();
            }
            ElMessage.success({
              message: '停止实时数采成功！',
              type: 'success'
            });
          }
        });
      }
    };

    const closeRealTimeDialog = val => {
      state.realTimeDialogVisible = val;
    };

    // #endregion

    // #region 历史数据采集

    const closeHistoryDialog = val => {
      state.historyDialogVisible = val;
    };

    // 点击数据采集按钮
    const handleHistoryData = () => {
      state.inProgress = false;
      proxy.$refs.historyDataDialog.initHistoryData();
      getColorList();
      state.historyDialogVisible = true;
    };

    // 选择线芯
    const changeRadioColor = val => {
      state.colorRadio = val;
      state.colorList.forEach(item => {
        item.radio = false;
        item.status = 0;
      });
      setCurrentColorRow(val);
    };

    const confirmSelectedData = val => {
      state.selectiondata = val;
      handleSumbitData();
    };

    function getSampleGroupNumber() {
      return state.sampleGroupNumber && state.sampleGroupNumber > 0 ? state.sampleGroupNumber : 1;
    }

    // 提交选中的数采数据
    const handleSumbitData = async (isRealTime = false, isCustom = false, callback) => {
      const collectedDataLength = state.selectiondata.length;
      const totalRequiredDataLength = (state.colorList.length - state.colorRadio) * getSampleGroupNumber();
      if (state.selectiondata.length !== 0) {
        excel.value.handleData();

        // 多线芯模板里赋值
        // colorRadio
        if (state.colorList.length !== 0 && state.coreExistInfo.coreColourList) {
          // 实时数据采集-自定义采集
          if (isCustom) {
            saveMultiSampleGroup([state.selectiondata[state.customConsumeIndex]]);
            state.customConsumeIndex += 1;
            state.isInConsume = false;
            state.colorList[state.colorRadio].status = 2;
          } else {
            // 实时数据采集-顺序采集
            if (isRealTime || collectedDataLength <= totalRequiredDataLength) {
              const currentColorIndex = Math.min(state.colorRadio + collectedDataLength, state.colorList.length - 1);
              for (let colorIndex = state.colorRadio; colorIndex < currentColorIndex; colorIndex++) {
                state.colorList[colorIndex].status = 2;
              }
              state.colorList.forEach(item => {
                item.radio = false;
              });
              setCurrentColorRow(currentColorIndex);
              saveMultiSampleGroup();
            } else {
              ElMessage({
                message: '只能选择' + state.coreExistInfo.coreColourList.length + '条数据',
                type: 'warning'
              });
              return false;
            }
          }
        } else {
          // 非多线芯数据保存
          saveSingleSampleGroup();
        }

        // 添加设备
        const devicePostData = {
          experimentId: state.jsonData.experimentId,
          samplesId: state.jsonData.samplesId,
          devices: []
        };
        devicePostData.devices = state.devices;
        const exist = devicePostData.devices.findIndex((items, indexitem) => {
          return items.deviceId === state.deviceInfo.deviceId;
        });
        if (exist === -1) {
          const pdata = JSON.parse(JSON.stringify(state.deviceInfo));
          pdata.id = '';
          devicePostData.devices.push(pdata);
          addDevice(devicePostData).then(resp => {
            // console.log(resp)
          });
        }
        // --------保存模板数据
        var excelInfoBody = {};
        excelInfoBody.capabilityId = state.jsonData.capabilityId;
        excelInfoBody.experimentId = state.jsonData.experimentId;
        excelInfoBody.templateId = state.jsonData.templateId;
        excelInfoBody.samplesId = state.jsonData.samplesId;
        excelInfoBody.remark = '';
        excelInfoBody.experimentData = state.experimentData;
        console.log('excelInfoBody', excelInfoBody);
        await saveExcelInfo(excelInfoBody).then(res => {
          if (res.data.code === 200) {
            if (!isRealTime) {
              ElMessage.success({
                message: '保存成功',
                type: 'success'
              });
            }
            getCurrentRowData(state.activeRow);
          }
          callback && callback(res);
        });
      } else {
        ElMessage('请选择数据之后提交');
      }
    };

    const debounceSumbitData = debounce(handleSumbitData, 300);

    function saveMultiSampleGroup(selectedData = state.selectiondata) {
      var prefixKeyList = [];
      const collectedDataLength = selectedData.length;
      const totalRequiredDataLength = (state.colorList.length - state.colorRadio) * getSampleGroupNumber();
      const startSampleGroupDataLength = state.colorRadio * getSampleGroupNumber();
      const validDataCount = Math.min(collectedDataLength, totalRequiredDataLength);
      // 获取有值的 关键参数key 列表
      selectedData[0].forEach(itthis => {
        prefixKeyList.push(itthis.templateKey);
      });
      prefixKeyList.forEach((prefixKey, itindex) => {
        var currentPrefixKeyList = [];
        // 获取模板中关键参数对应的id列表
        Object.keys(state.experimentData.body).forEach((templateKey, indexkey) => {
          if (new RegExp(`^${prefixKey}((_[a-z]+)?_\\d+){0,2}$`).test(templateKey)) {
            currentPrefixKeyList.push(templateKey);
          }
        });
        if (currentPrefixKeyList.length - 1 >= state.colorRadio) {
          for (let i = startSampleGroupDataLength; i < startSampleGroupDataLength + validDataCount; i++) {
            const currentTemplateKey = currentPrefixKeyList[i];
            let currentValue = state.experimentData.body[currentTemplateKey];
            const conversionValue = selectedData[i - startSampleGroupDataLength][itindex].conversionValue;
            if (conversionValue !== undefined && conversionValue !== '') {
              // state.experimentData.body[currentTemplateKey] = selectedData[i - startSampleGroupDataLength][itindex].conversionValue.toString()
              const selectedItem = selectedData[i - startSampleGroupDataLength][itindex];
              state.experimentData.body[currentTemplateKey] = selectedItem.conversionValue.toString();
              currentValue = selectedItem.conversionValue.toString();
            }
            handleInputData(currentTemplateKey, currentValue);
          }
        }
      });
      saveAllExperimentData();
    }

    function saveSingleSampleGroup() {
      state.selectiondata.forEach(async (item, index) => {
        await item.forEach((it, itindex) => {
          const num = index + 1;
          const valuekey1 = it.templateKey;
          const valuekey2 = `${it.templateKey}_${num}`;
          for (const key in state.experimentData.body) {
            if (key === valuekey1 || key === valuekey2) {
              let currentValue = it.conversionValue;
              if (it.conversionValue.toString()) {
                state.experimentData.body[key] = it.conversionValue.toString();
                currentValue = it.conversionValue.toString();
              }
              handleInputData(key, currentValue);
            }
          }
          saveAllExperimentData();
        });
      });
    }

    function setCurrentColorRow(currentIndex) {
      if (state.colorList[currentIndex]) {
        state.colorList[currentIndex].radio = state.colorList[currentIndex].id;
        state.colorList[currentIndex].status = 1;
        state.colorTableRef?.setCurrentRow(state.colorList[currentIndex]);
      }
    }

    function scrollToRow(currentIndex) {
      const tbody = document.getElementById('color-table').querySelector('.el-table__body-wrapper');
      if (tbody) {
        const rowHeight = tbody.clientHeight / state.colorList.length;
        const top = currentIndex * rowHeight;
        tbody.scrollTop = top;
      }
    }

    // 手动触发oninput事件
    function handleInputData(eleId, value) {
      const inputEvent = new Event('input', {
        bubbles: true,
        cancelable: true
      });
      const inputEle = document.getElementById(eleId);
      if (inputEle) {
        inputEle.value = value;
        inputEle.dispatchEvent(inputEvent);
      }
    }

    function saveAllExperimentData() {
      for (const bodyProp in state.experimentData.body) {
        const propEle = document.getElementById(bodyProp);
        if (propEle) {
          state.experimentData.body[bodyProp] = propEle.value;
        }
      }
    }

    // #endregion

    // #region 手动录入

    // 跳转检测执行
    const goOut = () => {
      router.push({
        path: '/execution/addRecord',
        query: {
          experimentId: state.jsonData.experimentId,
          samplesId: state.jsonData.samplesId,
          capabilityId: state.jsonData.capabilityId,
          type: 'edit',
          new: state.isnew
        }
      });
    };

    // #endregion

    // #region 实时数采-自定义写入

    const groupChange = searchText => {
      const firstIndex = state.colorList.findIndex(item => item.coreColour.includes(searchText));
      if (firstIndex !== -1) {
        state.colorList.forEach(item => {
          item.radio = false;
        });
        setCurrentColorRow(firstIndex);
        scrollToRow(firstIndex);
      }
    };

    const isCustomWrite = computed({
      get: () => state.writeMethod === 2 && state.inProgress
    });
    // isReceive 是否是接收到websocket的数据变化，是不再触发websocket发送功能
    const changeRadio = (row, isReceive) => {
      if (isCustomWrite.value && row && row.id) {
        state.isInConsume = true;
        state.selectedRow = row.id;
        row.radio = row.id;
        state.colorList.forEach(item => {
          if (item.id !== row.id) {
            item.radio = false;
          }
          if (item.status === 1) {
            item.status = 0;
          }
        });
        row.status = 1;
        state.colorRadio = state.colorList.findIndex(item => item.id === row.id);
        if (isReceive !== true) {
          websocketStart(state.colorList);
        }
        if (state.customConsumeIndex < state.selectiondata.length) {
          handleSumbitData(true, true);
        }
      }
    };

    // table 选择事件
    const handleSelectionChange = val => {
      console.log(val);
      // state.tags = lodash.uniqBy(state.tags, 'id')
    };

    // #endregion

    // #region 立即执行的方法

    /**
     * 获取当前设备下可数采的检测项目数据
     */
    const getCurrentDeviceTestItems = () => {
      // 获取数采设备信息
      getDetail(state.deviceInfo.id).then(resp => {
        const resdeviceInfo = resp.data.data.device;
        state.deviceInfo.deviceStatusAfter = '1';
        state.deviceInfo.deviceStatusBefore = '1';
        state.deviceInfo.deviceUseTime = formatDate(new Date());
        state.deviceInfo.measurementDeviation = resdeviceInfo.measurementDeviation;
        state.deviceInfo.model = resdeviceInfo.model;
        state.deviceInfo.ownerId = getLoginInfo().accountId;
        state.deviceInfo.remark = resdeviceInfo.remark;
        state.deviceInfo.validBeginDate = formatDate(resdeviceInfo.validBeginDate);
        state.deviceInfo.validEndDate = formatDate(resdeviceInfo.validEndDate);
      });
      findCapabilityByDeviceId(state.deviceInfo.id).then(res => {
        state.itemList = res.data.data;
        if (state.itemList.length !== 0) {
          if (route.query.capabilityId) {
            state.activeItem = route.query.capabilityId;
          } else {
            state.activeItem = state.itemList[0].id;
          }
          getCurrentItemSamplesInfo();
        }
      });
    };

    getCurrentDeviceTestItems();

    const handleSupplement = () => {
      state.dialogSupplement = true;
    };
    const handleCloseDialog = () => {
      state.dialogSupplement = false;
    };

    // 过滤状态颜色
    const filterStatus = status => {
      const classMap = {
        0: ['info', '待写入'],
        1: ['warning', '写入中'],
        2: ['success', '已写入']
      };
      return classMap[status];
    };

    // #endregion

    watch(
      () => state.inProgress,
      (value, oldValue) => {
        if (value === true) {
          addBrowserClosePrompt();
        } else {
          removeBrowserClosePrompt();
        }
      }
    );

    watch(
      () => state.selectiondata.length,
      (value, oldValue) => {
        if (value > 0 && state.inProgress) {
          if (isCustomWrite.value) {
            if (state.isInConsume) {
              debounceSumbitData(true, true);
            }
          } else {
            debounceSumbitData(true, false, resp => {
              let computedValue = 0;
              if (state.colorList.length > 0 && !isNaN(state.colorRadio)) {
                computedValue =
                  state.selectiondata.length /
                  ((state.colorList.length - Number(state.colorRadio)) * getSampleGroupNumber());
              } else {
                computedValue = state.selectiondata.length / getSampleGroupNumber();
              }
              if (computedValue > 0 && computedValue < 1) {
                setTimeout(() => {
                  state.realTimeProgress = Number(Number.parseFloat(computedValue * 100).toFixed(2));
                }, 2000);
              } else if (computedValue >= 1) {
                getRdsDataFromIdb().then(() => {
                  setTimeout(() => {
                    stopRealTimeInterval(false);
                    state.realTimeProgress = 100;
                  }, 2000);
                });
              } else {
                state.realTimeProgress = 0;
              }
            });
          }
        }
      }
    );

    bus.$on('stopRds', key => {
      if (key) {
        stopRealTimeInterval();
        websocketClose();
      }
    });

    return {
      ...toRefs(state),
      emptyImg,
      handleData,
      getPermissionBtn,
      handleSupplement,
      handleCloseDialog,
      closedialog,
      handleSafeguard,
      excel,
      tableListNew,
      closexx,
      changeRadioColor,
      add,
      colWidth,
      handleSumbit,
      handleSumbitData,
      sumbitData,
      goOut,
      handleAddRecord,
      handleCurrentChange,
      handleChangeTestItem,
      closeHistoryDialog,
      handleHistoryData,
      confirmSelectedData,
      startRealTimeData,
      stopRealTimeData,
      showRealTimeDataDialog,
      closeRealTimeDialog,
      groupChange,
      isCustomWrite,
      changeRadio,
      handleSelectionChange,
      filterStatus
    };
  },
  computed: {
    ...mapGetters(['webSocketMsg'])
  },
  watch: {
    webSocketMsg(msg) {}
  }
};
</script>
<style scoped lang="scss">
$dataCollectionSpace: 15rem;
$baseMarginTop: 0.8rem;
.radio-item-content {
  max-height: 50px;
  overflow-y: auto;
}
:deep(.page-list-main .main-panel) {
  height: calc(100vh - 14rem);
  padding: 0 12px 0 20px;
}

.collection-form {
  .el-form-item {
    margin-bottom: 0;
  }
  .el-form-item:first-of-type {
    margin-bottom: 8px;
  }
  .full-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .title {
      display: flex;
      align-items: center;
      color: #303133;
      font-weight: 500;
      font-size: 16px;
      .icon-status {
        width: 20px;
        height: 20px;
        border-radius: 2px;
        font-size: 12px;
        margin-right: 10px;
        background: $tes-primary;
        color: #fff;
        display: inline-flex;
        justify-content: center;
        align-items: center;
      }
      .space {
        padding: 0 8px;
      }
    }
    .right-group {
      .item-label {
        color: #606266;
        font-weight: normal;
      }
    }
  }
  .form-item-progress {
    width: 30%;
    min-width: 240px;
    :deep(.el-progress.el-progress--line) {
      height: 36px;
    }
  }
}
.left-list {
  .ipt-search {
    margin-bottom: 20px;
  }

  :deep(.el-table.table-content) {
    .el-table__body-wrapper {
      max-height: calc(100vh - 27rem);
      overflow-y: auto;
      overflow-x: none;
    }
  }
  :deep(.el-table tr) {
    cursor: pointer;
  }
  .table-content {
    height: calc(100vh - 20rem);
    .item {
      display: flex;
      align-items: center;
    }
  }
}
:deep(.page-list-main .el-container .el-main),
:deep(.el-container .el-main .page-main) {
  background: #f0f2f5;
  height: 100%;
  padding: 0;
}
:deep(.page-list-main .main-panel .left-panel) {
  margin-top: 0.8rem;
  height: calc(100vh - 15rem - 0.8rem);
}

.group-wrapper {
  margin-top: 0.8rem;
  background-color: $background-color;
  padding: 0.571429rem 1.5rem;
  border-radius: 0.142857rem;
  display: block;
  line-height: 2.285714rem;
  font-size: 1.142857rem;
  font-family: 'MiSans', 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', Arial, sans-serif, iconfont;
  color: #303133;
  height: calc(100vh - 15rem - 0.8rem);

  .group-search {
    margin-bottom: 1rem;
  }

  .color-table-wrapper {
    height: auto;
    margin-bottom: 20px;
  }

  :deep(#color-table) {
    .el-table__body-wrapper {
      max-height: calc(100vh - 44rem);
      overflow-y: auto;
    }
  }

  .sample-group {
    width: 100%;
    max-height: calc(100vh - 15rem - 0.8rem - 3.5rem);
    overflow-y: auto;
    background-color: $background-color;
    :deep(.el-radio-group) {
      width: 100%;
      max-height: calc(100vh - 15rem - 0.8rem - 4.5rem);
      overflow-y: auto;
    }
    :deep(.el-radio) {
      background-color: #f5f7fa;
      line-height: 5;
      display: block;
      margin: 1px 0;
      text-align: start;
    }
  }
}

.right-box {
  margin-top: 0.8rem;
  width: 100%;
  max-height: calc(100vh - 14rem - 0.8rem);
  overflow-y: auto;
}
</style>
