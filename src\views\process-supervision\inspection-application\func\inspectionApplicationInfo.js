import { colWidth } from '@/data/tableStyle';

/**
 * 属性说明
 * field: 字段名
 * name: 字段显示名
 * colwidth: 列宽(可以为tableStyle中的值，也可是数字)
 * type: 列的类型。 0: 默认类型
 * isNotQuery: 列是否可以为非查询字段， 0: 不是，1: 是
 * routeType: 路由调整类型 0:检验申请-申请单号
 * styleContent: 样式字段解析
 * order: 排序字段，排序顺序
 * checkbox: 当前视图中是否显示
 * isHide: 是否为隐藏列
 * isOrder: 是否需要排序
 */

/**
 * 列的类型type
 * 0: 默认显示
 * 1: 带链接的列
 * 2: 显示用户名的列
 * 3: 显示日期的列
 * 4: 显示枚举的列
 * 4: 以显示枚举的列
 */

export const applicationFieldList = [
  {
    field: 'no',
    name: '申请单号',
    colWidth: colWidth.orderNo,
    type: 1,
    isNotQuery: 0,
    routeType: 0,
    styleContent: {},
    order: 0,
    checkbox: true,
    isHide: 0,
    isOrder: 1
  },
  {
    field: 'type',
    name: '检验类型',
    colWidth: 150,
    type: 4,
    isNotQuery: 1,
    styleContent: {},
    order: 0,
    checkbox: true,
    isHide: 0,
    isOrder: 1,
    isMinWidth: true
  },
  {
    field: 'inputWarehouseNo',
    name: '检验对象',
    colWidth: colWidth.objectNo,
    type: 6,
    isNotQuery: 1,
    styleContent: {},
    order: 0,
    checkbox: true,
    isHide: 0,
    isOrder: 1,
    isMinWidth: true
  },
  {
    field: 'wareHouseName',
    name: '对象位置',
    colWidth: colWidth.name,
    type: 7,
    isNotQuery: 1,
    styleContent: {},
    order: 0,
    checkbox: true,
    isHide: 0,
    isOrder: 1,
    isMinWidth: true
  },
  {
    field: 'supplierName',
    name: '对象名称',
    colWidth: colWidth.name,
    type: 8,
    isNotQuery: 1,
    styleContent: {},
    order: 0,
    checkbox: true,
    isHide: 0,
    isOrder: 1,
    isMinWidth: true
  },
  {
    field: 'registerTime',
    name: '登记日期',
    colWidth: colWidth.date,
    type: 3,
    isNotQuery: 1,
    styleContent: {},
    order: 0,
    checkbox: true,
    isHide: 0,
    isOrder: 1,
    isMinWidth: true
  },
  {
    field: 'registerUserId',
    name: '登记人',
    colWidth: colWidth.person,
    type: 2,
    isNotQuery: 1,
    styleContent: {},
    order: 0,
    checkbox: true,
    isHide: 0,
    isOrder: 1,
    isMinWidth: true
  },
  {
    field: 'applicantName',
    name: '申请人',
    colWidth: colWidth.person,
    type: 2,
    isNotQuery: 1,
    styleContent: {},
    order: 0,
    checkbox: true,
    isHide: 0,
    isOrder: 1,
    isMinWidth: true
  },
  {
    field: 'thirdType',
    name: '来源',
    colWidth: colWidth.typeGroup,
    type: 4,
    isNotQuery: 1,
    styleContent: {},
    order: 0,
    checkbox: true,
    isHide: 0,
    isOrder: 1,
    isMinWidth: true
  },
  {
    field: 'status',
    name: '状态',
    colWidth: colWidth.status,
    type: 5,
    isNotQuery: 1,
    styleContent: {},
    order: 0,
    checkbox: true,
    isHide: 0,
    isOrder: 1,
    isMinWidth: true
  }
];

export const applicationEnumList = [
  {
    field: 'type',
    dataMap: {
      1: '采购入库',
      2: '过程检验',
      3: '完工检验'
    }
  },
  {
    field: 'thirdType',
    dataMap: {
      0: 'ERP',
      1: 'MES',
      2: 'LIMS/自建'
    }
  }
];

export const applicationTagList = [
  {
    field: 'status',
    dataMap: {
      0: ['warning', '待提交'],
      1: ['success', '已提交'],
      2: ['info', '已作废']
    }
  }
];

export const applicationRouteList = [
  {
    field: 'id',
    conditionField: 'status'
  }
];

export const applicationCombinedFieldList = [
  {
    field: 'inputWarehouseNo',
    name: '入库单号',
    isNotQuery: 0
  },
  {
    field: 'productionOrderNo',
    name: '生产单号',
    isNotQuery: 0
  },
  {
    field: 'wareHouseName',
    name: '仓库名称',
    isNotQuery: 0
  },
  {
    field: 'productionProcedure',
    name: '生产工序',
    isNotQuery: 0
  },
  {
    field: 'productionStation',
    name: '生产机台',
    isNotQuery: 0
  },
  {
    field: 'supplierName',
    name: '供应商名称',
    isNotQuery: 0
  },
  {
    field: 'customerName',
    name: '客户名称',
    isNotQuery: 0
  }
];

export function handleEnum(field, value) {
  if (field && applicationEnumList.length > 0) {
    const enumIndex = applicationEnumList.findIndex(item => item.field === field);
    if (enumIndex === -1) {
      return '--';
    } else {
      return applicationEnumList[enumIndex].dataMap[value] || '--';
    }
  } else {
    return '--';
  }
}

export function handleTag(field, value) {
  if (field && applicationTagList.length > 0) {
    const enumIndex = applicationTagList.findIndex(item => item.field === field);
    if (enumIndex === -1) {
      return ['', value];
    } else {
      return applicationTagList[enumIndex].dataMap[value] || ['', value];
    }
  } else {
    return ['', value];
  }
}
