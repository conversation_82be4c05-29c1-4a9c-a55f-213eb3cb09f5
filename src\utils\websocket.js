// readyState常量 0 (CONNECTING) 正在链接中
//                1 (OPEN)已经链接并且可以通讯
//                2 (CLOSING)连接正在关闭
//                3 (CLOSED)连接已关闭或者没有链接成功
// 引入store，用于管理socket推送来的消息
import store from '@/store';
import { getLoginInfo, setHasWebSocket, getHasWebSocket } from '@/utils/auth';
import { formatWebsocketTime } from '@/utils/formatTime';
import { getWebSocketURL } from '@/utils/base-url';
// import { getCurrentInstance } from 'vue'
// 封装websocket对象
var $ws = null;
// const { proxy } = getCurrentInstance()
const WS = {
  websocket: null,
  isError: false,
  // 初始化webSocket
  createWS: function () {
    var currentUserId = getLoginInfo() ? getLoginInfo().accountId : '';
    var that = this;
    var wsUrl = getWebSocketURL() + '/' + currentUserId + '/' + formatWebsocketTime();
    if (!currentUserId) {
      return false;
    }
    if (!$ws) {
      if ('WebSocket' in window) {
        $ws = new WebSocket(wsUrl);
        $ws.onopen = that.wsOpen;
        $ws.onmessage = that.wsMessage;
        $ws.onerror = that.wsError;
        $ws.onclose = that.wsClose;
        // console.log('建立websocket连接')
        setHasWebSocket(true);
        that.websocket = $ws;
      } else {
        alert('当前浏览器不支持webSocket');
      }
    } else {
      // console.log('websocket已连接')
      setHasWebSocket(true);
    }
  },
  // webSocket 打开
  wsOpen: function () {
    console.log('== websocket 连接成功🔛 ==');
    // 开始心跳
    heartBeat.start();
    if (window.location.pathname === '/smart-charts/dataBoard') {
      heartBeat2.start();
    }
  },
  // websocket 接收到服务器消息
  wsMessage: function (msg) {
    const dataString = msg.data;
    const jsonRegExp = /(^[[{][^]*[\]}]$)/;
    if (jsonRegExp.test(dataString)) {
      const data = JSON.parse(dataString);
      if (data?.header?.cmd !== undefined) {
        switch (data.header.cmd) {
          case 1: // 待检任务
            if (window.location.pathname === '/smart-charts/dataBoard') {
              heartBeat2.reset();
              store.dispatch('websocket/changeMSG', data);
            }
            break;

          case 33:
            // 样品下达归档
            store.dispatch('websocket/changeMSG', data);
            break;

          case 0: // 测试
          default:
            // 设置心跳
            heartBeat.reset();
            break;
        }
      } else {
        // 接受到消息，重置心跳
        heartBeat.reset();
        store.dispatch('websocket/changeMSG', dataString);
      }
    }
  },
  // websocket 发生错误
  wsError: function () {
    WS.isError = true;
  },
  // websocket 关闭连接
  wsClose: function (event) {
    if ($ws.readyState !== 2 && getHasWebSocket() === 'true' && WS.isError === false) {
      $ws = null;
      WS.createWS();
    } else {
      $ws = null;
    }
  },
  getWebSocketInstance: function () {
    return WS.websocket;
  }
};
// webSocket 心跳
const heartBeat = {
  timeout: 60000, // 心跳重连时间
  timeoutObj: null, // 定时器
  reset: function () {
    clearTimeout(this.timeoutObj);
    this.start();
  },
  start: function () {
    if (!this.timeoutObj) {
      this.send();
    }
    this.timeoutObj = setTimeout(this.send, this.timeout);
  },
  send: function () {
    if ($ws.readyState === 1) {
      const jsonData = {
        header: {
          cmd: 0
        },
        body: {
          HeartBeat: new Date().getTime()
        }
      };
      $ws.send(JSON.stringify(jsonData));
    }
  }
};
const heartBeat2 = {
  timeout: 10000, // 心跳重连时间
  timeoutObj: null, // 定时器
  reset: function () {
    clearTimeout(this.timeoutObj);
    this.start();
  },
  start: function () {
    if (!this.timeoutObj) {
      this.send();
    }
    this.timeoutObj = setTimeout(this.send, this.timeout);
  },
  send: function () {
    if ($ws.readyState === 1) {
      const jsonData = {
        header: {
          cmd: 1
        },
        body: {
          tenantId: getLoginInfo() ? getLoginInfo().tenantId : '',
          beforeMinutes: 1
        }
      };
      $ws.send(JSON.stringify(jsonData));
    }
  }
};
export default WS;
