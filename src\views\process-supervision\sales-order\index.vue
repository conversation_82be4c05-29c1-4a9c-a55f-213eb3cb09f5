<template>
  <!-- 销售订单列表 -->
  <ListLayout
    :has-quick-query="true"
    :has-right-panel="true"
    :aside-panel-width="600"
    :aside-min-width="400"
    :aside-max-width="700"
    :main-offset-top="84"
  >
    <template #search-bar>
      <div class="searchInput">
        <div style="width: 42vw">
          <CombinationQuery
            :field-list="tableColumns"
            :field-tip="fieldTip"
            @get-query-info="getQueryInfo"
            @reset-search="reset"
          />
        </div>
      </div>
    </template>
    <template #button-group>
      <el-button
        v-if="getPermissionBtn('SendInspection')"
        class="fr"
        :disabled="tableData.length === 0"
        :loading="tableLoading"
        type="primary"
        size="large"
        @click="openInspectionDialog()"
        @keyup.prevent
        @keydown.enter.prevent
        >送检</el-button
      >
    </template>
    <template #radio-content>
      <el-row>
        <el-col :span="16">
          <el-radio-group v-model="radioData" size="small" @change="changeQuickSearch">
            <el-radio-button label="全部" />
            <el-radio-button label="待送检" />
            <el-radio-button label="已送检" />
          </el-radio-group>
        </el-col>
        <el-col :span="8" style="text-align: right">
          <TableColumnView binding-menu="SalesOrder" @columns="onUpdateColumns" />
        </el-col>
      </el-row>
    </template>
    <el-table
      ref="tableRef"
      :key="tableKey"
      v-loading="tableLoading"
      :data="tableData"
      fit
      border
      height="auto"
      :size="otherForm.tableSize"
      highlight-current-row
      class="dark-table format-height-table base-table"
      :row-style="
        () => {
          return 'cursor: pointer';
        }
      "
      @header-dragend="drageHeader"
      @sort-change="sortChange"
      @selection-change="handleSelectionChange"
      @current-change="changeRadio"
    >
      <el-table-column type="index" label="选择" width="70" align="center">
        <template #default="{ row }">
          <el-radio v-model="row.radio" :label="row.salesOrderDetailId" @change="changeRadio(row)">{{ '' }}</el-radio>
        </template>
      </el-table-column>
      <el-table-column label="序号" type="index" width="50" />

      <template v-for="(item, index) in tableColumns" :key="index">
        <el-table-column
          :prop="item.fieldKey"
          :label="item.fieldName"
          :sortable="Number(item.isSortable) === 1"
          :width="item.isMinWidth ? '' : item.columnWidth"
          :min-width="item.isMinWidth ? item.columnWidth : ''"
          :fixed="
            item.columnFixedType === columnFixedTypesEnum.Left
              ? 'left'
              : item.columnFixedType === columnFixedTypesEnum.Right
              ? 'right'
              : false
          "
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <div v-if="item.fieldType === fieldTypesEnum.Status">
              <el-tag size="small" effect="dark" :type="handleTag(item.fieldKey, row[item.fieldKey])[0]">{{
                handleTag(item.fieldKey, row[item.fieldKey])[1]
              }}</el-tag>
            </div>
            <div v-else-if="item.fieldType === fieldTypesEnum.Custom">
              <span v-if="item.fieldKey == 'materialCode'">
                {{ materialCategoryAll[row[item.fieldKey]]?.name || row[item.fieldKey] || '--' }}
              </span>
              <span v-else>{{ row[item.fieldKey] || '--' }}</span>
            </div>
            <div v-else>
              {{ row[item.fieldKey] || '--' }}
            </div>
          </template>
        </el-table-column>
      </template>
    </el-table>

    <pagination :page="listQuery.page" :limit="listQuery.limit" :total="total" @pagination="getOrderList" />
    <template #page-right-side>
      <el-tabs v-model="activeName" @tab-change="handleChangeTabs">
        <el-tab-pane label="销售信息" name="1">
          <el-descriptions :column="1" direction="horizontal">
            <el-descriptions-item v-for="item in productionInfoList" :key="item.field" label-align="left">
              <template #label>
                <OverflowTooltip :tooltip-content="`${item.label}:`" />
              </template>
              <div v-if="item.fieldType == 'custom'">
                <OverflowTooltip
                  v-if="item.field == 'isDeleted'"
                  :tooltip-content="currentRowInfo[item.field] == 0 ? '未删除' : '已删除'"
                />
              </div>
              <OverflowTooltip
                v-else-if="item.fieldType == 'date'"
                :tooltip-content="formatDate(currentRowInfo[item.field])"
              />
              <OverflowTooltip v-else :tooltip-content="currentRowInfo[item.field]?.toString() || '--'" />
            </el-descriptions-item>
          </el-descriptions>
        </el-tab-pane>
        <el-tab-pane label="送检记录" name="2">
          <el-table
            ref="tableRef"
            :key="tableKey"
            :data="inspectionList"
            fit
            border
            height="auto"
            :size="otherForm.tableSize"
            highlight-current-row
            :default-sort="{ prop: 'registerTime', order: 'descending' }"
            class="dark-table format-height-table base-table"
            @header-dragend="drageHeader"
          >
            <el-table-column label="序号" type="index" width="50" />
            <el-table-column label="申请单号" prop="no" :min-width="colWidth.orderNo" sortable show-overflow-tooltip>
              <template #default="{ row }">
                <div class="blue-color" @click="jumpToApplication(row)">{{ row.no || '--' }}</div>
              </template>
            </el-table-column>
            <el-table-column label="送检人" prop="registerUserId" :width="colWidth.person" show-overflow-tooltip>
              <template #default="{ row }">
                <div>{{ getNameByid(row.registerUserId) || row.registerUserId || '--' }}</div>
              </template>
            </el-table-column>
            <el-table-column label="送检时间" prop="registerTime" :width="colWidth.date" sortable show-overflow-tooltip>
              <template #default="{ row }">
                <div>{{ formatDate(row.registerTime) || row.registerTime || '--' }}</div>
              </template>
            </el-table-column>
            <el-table-column
              label="操作"
              :width="colWidth.operation"
              class-name="fixed-right"
              prop="operation"
              fixed="right"
            >
              <template #default="{ row }">
                <span class="blue-color" @click="jumpToApplication(row)">查看</span>
                <span
                  v-if="getPermissionBtn('SalesOrdeInvalid') && row.isInvalidated === 0"
                  class="blue-color"
                  @click="cancelApplication(row)"
                  >作废</span
                >
                <span
                  v-if="getPermissionBtn('SalesOrderRestore') && row.isInvalidated === 1"
                  class="blue-color"
                  @click="handleRestore(row)"
                  >还原</span
                >
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </template>

    <template #other>
      <DialogOrderInspection
        :show="showInspectionDialog"
        :page-view="pageViewGroup"
        :info="currentRowInfo"
        @close="closeInspectionDialog"
        @save-inspection="submitOrderInspection"
      />
    </template>
  </ListLayout>
</template>

<script>
import { reactive, ref, onMounted, toRefs, computed } from 'vue';
import Pagination from '@/components/Pagination';
import { getDictionary } from '@/api/user';
import { colWidth } from '@/data/tableStyle';
import ListLayout from '@/components/ListLayout';
import OverflowTooltip from '@/components/OverflowTooltip';
import { getNameByid, getPermissionBtn } from '@/utils/common';
import { formatDate } from '@/utils/formatTime';
import { drageHeader } from '@/utils/formatTable';
import router from '@/router/index.js';
import DialogOrderInspection from './components/DialogOrderInspection';
import { getSalesOrderList, getInspectionRecordList, saveInspectionRecord } from '@/api/sales-order';
import { ElMessage, ElMessageBox } from 'element-plus';
import { cancelInspection, restoreInspection } from '@/api/inspection-application';
import { getColWidth } from '@/utils/func/customTable';
import { orderFieldList, handleTag } from './func/productionOrderInfo';
import CombinationQuery from '@/components/CombinationQuery';
import TableColumnView from '@/components/TableColumnView';
import { fieldTypesEnum, columnFixedTypesEnum } from '@/components/TableColumnView/enum';
import { getViewByBindingMenu } from '@/api/tableView';
import { formatViewData } from '@/utils/formatJson';
import { materialCategoryList } from '@/api/material';

export default {
  name: 'SalesOrder',
  components: { ListLayout, Pagination, DialogOrderInspection, OverflowTooltip, CombinationQuery, TableColumnView },
  setup(props, context) {
    // const route = useRoute()
    const state = reactive({
      searchForm: {
        isAsc: true,
        param: '',
        submitStatus: '',
        materialNo: '',
        station: '',
        tableQueryParamList: []
      },
      tableColumns: [],
      materialCategoryAll: {},
      materialCategoryList: [],
      fieldTip: '',
      tableKey: 0,
      tableData: [],
      total: 0,
      tableLoading: false, // 表格加载的loading
      dialogFrom: {}, // 操作树节点的弹窗表格
      dictionaryAll: {
        5: {
          enable: {},
          all: {}
        }
      },
      listQuery: {
        page: 1,
        limit: 20
      },
      radioData: '全部',
      showInspectionDialog: false,
      currentRowInfo: {},
      inspectionList: [],
      selectedOrderDetailId: '',
      activeSearchName: '0',
      pageViewGroup: {
        '2-basicInfo-dialog': {}
      }
    });
    const editFrom = ref(null);
    const activeName = ref('1');
    const productionInfoList = ref([]);
    const multipleSelection = ref([]);

    const otherForm = reactive({
      tableSize: 'medium'
    });

    const reset = () => {
      state.searchForm.param = '';
      state.searchForm.tableQueryParamList = [];
      state.searchForm.materialNo = '';
      state.searchForm.station = '';
      state.listQuery.page = 1;
      state.listQuery.limit = 20;
      getOrderList();
    };

    // #region deprecated
    const getDetailView = async () => {
      const res = await getViewByBindingMenu('AddInspection');
      if (res) {
        state.pageViewGroup = formatViewData(res.data.data[0].sysEmployeeListConfigList);
      }
    };
    getDetailView();

    // 获取库存单位
    const getDictionaryList = () => {
      Object.keys(state.dictionaryAll).forEach(async item => {
        const response = await getDictionary(item);
        if (response) {
          state.dictionaryAll[item] = { enable: {}, all: {} };
          response.data.data.dictionaryoption.forEach(optionItem => {
            if (optionItem.status == 1) {
              state.dictionaryAll[item].enable[optionItem.code] = optionItem.name;
            }
            state.dictionaryAll[item].all[optionItem.code] = optionItem.name;
          });
        }
      });
    };

    // 列表排序
    const sortChange = column => {
      state.searchForm.orderBy = column.prop;
      state.searchForm.isAsc = column.order === 'ascending';
      getOrderList();
    };

    // #endregion

    // #region 生产订单

    const getOrderList = query => {
      const params = {
        ...state.searchForm
      };
      if (query && query.page) {
        state.listQuery.page = query.page;
        state.listQuery.limit = query.limit;
      }
      params.page = state.listQuery.page.toString();
      params.limit = state.listQuery.limit.toString();
      state.tableLoading = true;
      getSalesOrderList(params).then(res => {
        state.tableLoading = false;
        if (res) {
          state.tableData = res.data.data.list;
          state.total = res.data.data.totalCount;
          if (state.tableData.length > 0) {
            if (state.selectedOrderDetailId) {
              const findIndex = state.tableData.findIndex(
                item => item.salesOrderDetailId === state.selectedOrderDetailId
              );
              if (findIndex !== -1) {
                changeRadio(state.tableData[findIndex]);
              }
              state.selectedOrderDetailId = '';
            } else {
              changeRadio(state.tableData[0]);
            }
          }
        }
      });
    };
    const getMaterialcategory = () => {
      materialCategoryList('').then(response => {
        state.materialCategoryList = response.data.data;
        state.materialCategoryAll = {};
        response.data.data.forEach(item => {
          if (item.parentId === '0') {
            state.materialCategoryAll[item.code] = item;
          }
        });
      });
    };
    const changeQuickSearch = value => {
      const param = {
        全部: '',
        待送检: '0',
        已送检: '1'
      };
      state.searchForm.submitStatus = param[value];
      state.listQuery.page = 1;
      getOrderList();
    };

    const changeRadio = row => {
      if (row && row.salesOrderDetailId) {
        state.currentRowInfo = row;
        setInspectionRecordList();
        row.radio = row.salesOrderDetailId;
        state.tableData.forEach(item => {
          if (item.salesOrderDetailId !== row.salesOrderDetailId) {
            item.radio = false;
          }
        });
      }
    };

    const handleChangeTabs = value => {};

    const openInspectionDialog = () => {
      state.showInspectionDialog = true;
    };

    const closeInspectionDialog = val => {
      state.showInspectionDialog = false;
      if (val) {
        getOrderList();
      }
    };

    const handleSelectionChange = val => {
      multipleSelection.value = val;
    };

    const inspectionDisabled = computed(() => multipleSelection.value.length === 0);

    const jumpToApplication = row => {
      if (row.status?.toString() === '0') {
        // 待提交状态可以编辑
        router.push({ name: 'SalesOrderApplication', query: { id: row.id, flag: 2 } });
      } else {
        // 非待提交，只能看详情
        router.push({ name: 'SalesOrderApplication', query: { id: row.id, flag: 1 } });
      }
    };

    const cancelApplication = row => {
      ElMessageBox({
        title: '提示',
        message: '确定作废当前检验申请单吗？',
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        showCancelButton: true,
        closeOnClickModal: true,
        type: 'warning'
      })
        .then(() => {
          cancelInspection(row.id).then(res => {
            if (res !== false) {
              ElMessage.success(`编号：${row.no}已作废`);
              setInspectionRecordList();
            }
          });
        })
        .catch(() => {});
    };

    const handleRestore = row => {
      ElMessageBox({
        title: '提示',
        message: '确定还原当前检验申请单吗？',
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        showCancelButton: true,
        closeOnClickModal: true,
        type: 'warning'
      })
        .then(() => {
          restoreInspection(row.id).then(res => {
            if (res) {
              ElMessage.success(`编号${row.no}已还原`);
              setInspectionRecordList();
            }
          });
        })
        .catch(() => {});
    };

    // #endregion

    // #region 送检
    const submitOrderInspection = val => {
      saveInspectionRecord(val).then(res => {
        if (res && res.data.code === 200) {
          ElMessage.success(`送检成功!`);
          state.selectedOrderDetailId = state.currentRowInfo.salesOrderDetailId;
          getOrderList();
        }
      });
    };
    // #endregion

    // #region 立即执行的方法
    onMounted(() => {
      productionInfoList.value = orderFieldList;
      getDictionaryList();
      getOrderList();
      getMaterialcategory();
    });
    function setInspectionRecordList() {
      state.tableLoading = true;
      getInspectionRecordList(state.currentRowInfo.salesOrderDetailId).then(res => {
        state.tableLoading = false;
        if (res && res.data.code === 200) {
          state.inspectionList = res.data.data;
        }
      });
    }
    // #endregion

    // #region 组合查询

    function getQueryInfo(info) {
      state.searchForm.param = info.param;
      state.searchForm.tableQueryParamList = info.tableQueryParamList;
      getOrderList();
    }

    // 更新表格字段
    const onUpdateColumns = columns => {
      state.tableKey = state.tableKey + 1;
      state.tableColumns = columns;
      // 查询字段多放不下，产品说直接改成内容不在罗列
      state.fieldTip = '内容';
      // state.fieldTip = columns
      //   .filter(item => item.isQuery == 1)
      //   .map(item => item.fieldName)
      //   .join('/');
    };

    // #endregion
    return {
      ...toRefs(state),
      inspectionDisabled,
      sortChange,
      getDictionaryList,
      getPermissionBtn,
      formatDate,
      getNameByid,
      drageHeader,
      editFrom,
      otherForm,
      activeName,
      reset,
      colWidth,
      changeQuickSearch,
      handleChangeTabs,
      openInspectionDialog,
      closeInspectionDialog,
      productionInfoList,
      getOrderList,
      handleSelectionChange,
      jumpToApplication,
      cancelApplication,
      handleRestore,
      changeRadio,
      submitOrderInspection,
      getColWidth,
      orderFieldList,
      handleTag,
      getQueryInfo,
      onUpdateColumns,
      fieldTypesEnum,
      columnFixedTypesEnum
    };
  },
  created() {},
  methods: {}
};
</script>
<style lang="scss" scoped>
.searchInput {
  display: flex;
  .el-input {
    width: 360px;
    margin-right: 10px;
  }
  .advanced-btn {
    margin-left: 0.25rem;
  }
}

.el-descriptions {
  :deep(.el-descriptions--medium) {
    display: block;
    width: auto;
    min-width: none;
    tbody,
    tr,
    td {
      display: block;
      width: auto;
    }
  }
  :deep(.el-descriptions__label) {
    display: inline-block;
    color: $tes-font1;
    font-weight: normal;
    width: 140px;
    text-align: right;
  }
  :deep(.el-descriptions__content) {
    color: $tes-font;
  }
  :deep(.el-descriptions__body) {
    white-space: nowrap;
    max-height: 680px;
    overflow: auto;
  }
  :deep(.el-descriptions__content) {
    display: inline-block;
    width: calc(100% - 130px);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding-right: 8px;
  }
}

:deep(.left-panel) {
  height: 100%;
}
</style>
