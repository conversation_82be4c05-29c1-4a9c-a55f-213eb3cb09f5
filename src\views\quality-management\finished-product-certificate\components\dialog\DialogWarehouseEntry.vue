<template>
  <!-- 入库、批量入库 -->
  <el-dialog v-model="dialogShow" title="SAP入库" width="400px" :close-on-click-modal="false" @close="cancelDialog()">
    <el-form
      v-if="dialogShow"
      ref="formRef"
      v-loading="dialogLoading"
      :model="formData"
      label-position="top"
      label-width="60px"
    >
      <el-form-item
        label="仓库："
        prop="wareHouseNo"
        :rules="{
          required: true,
          message: '请选择仓库',
          trigger: 'change'
        }"
      >
        <el-select
          v-model="formData.wareHouseNo"
          filterable
          size="small"
          clearable
          placeholder="请选择仓库"
          class="w-full"
        >
          <el-option v-for="(val, key) in dictionaryAll['BYCK'].enable" :key="key" :label="val" :value="key" />
        </el-select>
      </el-form-item>
      <el-form-item
        label="交货单位："
        prop="deliveryInstitution"
        :rules="{
          required: true,
          message: '请选择交货单位',
          trigger: 'change'
        }"
      >
        <el-select
          v-model="formData.deliveryInstitution"
          filterable
          size="small"
          clearable
          placeholder="请选择交货单位"
          class="w-full"
        >
          <el-option v-for="(val, key) in dictionaryAll['JHDW'].enable" :key="key" :label="val" :value="key" />
        </el-select>
      </el-form-item>
      <el-form-item
        label="入库日期："
        prop="wareHouseDate"
        :rules="{
          required: false,
          message: '请选择入库日期',
          trigger: 'change'
        }"
      >
        <el-date-picker v-model="formData.wareHouseDate" type="date" placeholder="请选择入库日期" style="width: 100%" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button :loading="dialogLoading" @click="cancelDialog()">取消</el-button>
      <el-button :loading="dialogLoading" type="primary" @click="handleSubmit()">确定</el-button>
    </template>
  </el-dialog>
</template>
<script>
import { reactive, watch, ref, toRefs } from 'vue';
import { getNameByid } from '@/utils/common';
import { formatDate } from '@/utils/formatTime';
import { ElMessage } from 'element-plus';
import { finishedProductDataReturn } from '@/api/finished-product-certificate';
import { downloadFile } from '@/api/warehousing-record';
export default {
  name: 'DialogWarehouseEntry',
  components: {},
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    selectRow: {
      type: Object,
      default: () => ({})
    },
    dictionary: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['closeDialog'],
  setup(props, context) {
    const state = reactive({
      formData: {},
      dictionaryAll: {
        BYCK: { enable: {}, all: {} },
        JHDW: { enable: {}, all: {} }
      },
      dialogLoading: false,
      dialogShow: false,
      formRef: ref()
    });

    const cancelDialog = value => {
      state.dialogShow = false;
      context.emit('closeDialog', value);
    };

    const submitJudgement = () => {};

    watch(
      () => props.dialogVisible,
      newValue => {
        state.dialogShow = newValue;
        if (newValue) {
          state.formData = {
            wareHouseDate: formatDate(new Date())
          };
          state.dictionaryAll = props.dictionary || { BYCK: { enable: {}, all: {} } };
        }
      }
    );

    const handleSubmit = async () => {
      state.formRef
        .validate()
        .then(async valid => {
          if (valid) {
            const params = [];
            props.selectRow.forEach(item => {
              params.push({
                ...item,
                ...state.formData
              });
            });
            state.dialogLoading = true;
            const { data } = await finishedProductDataReturn({ list: params }).finally((state.dialogLoading = false));
            if (data) {
              ElMessage.success('入库成功!');
              handleDownLoad(data.data);
              cancelDialog(true);
            }
          } else {
            return false;
          }
        })
        .catch(() => {});
    };
    const handleDownLoad = async response => {
      state.listLoading = true;
      const { data } = await downloadFile(response).finally((state.listLoading = false));
      if (data) {
        window.open(data.data);
        ElMessage.success('导出成功!');
      }
    };

    return {
      ...toRefs(state),
      cancelDialog,
      handleSubmit,
      getNameByid,
      formatDate,
      submitJudgement
    };
  }
};
</script>
<style lang="scss" scoped></style>
