<template>
  <div :ref="el => (containerRef = el)" class="firstTabRaw">
    <div
      :ref="el => (templateRef = el)"
      class="template-container"
      :style="{ maxWidth: isTransverse ? '1163PX' : '834PX' }"
    >
      <div class="btnGroup">
        <div class="btnGroupLeft">
          <el-popover
            :width="840"
            placement="bottom-start"
            popper-class="item-detail-model"
            trigger="click"
            :show-arrow="false"
          >
            <template #reference>
              <el-button size="small" class="squareButton">
                <svg-icon icon-class="project-info" :width="20" :height="20" />
                <div class="button-name">项目信息</div>
              </el-button>
            </template>
            <template #default>
              <div class="content">
                <p class="title">{{ jsonData.capabilityName }}</p>
                <div class="tag-group">
                  <el-tag v-for="(item, index) in jsonData.capabilityPara" :key="index" type="info" size="small">{{
                    item
                  }}</el-tag>
                </div>
                <el-row class="itemLine">
                  <el-col :span="12" class="nowrap">
                    <span class="label">试验员：</span>
                    <UserTag
                      v-for="(item, index) in getNamesByid(jsonData.ownerIds)"
                      :key="index"
                      :name="index === getNamesByid(jsonData.ownerIds).length - 1 ? item : item"
                    />
                  </el-col>
                  <el-col :span="12" class="nowrap">
                    <span class="label">日期要求：</span>{{ jsonData.startdatetime }} ~ {{ jsonData.finishdatetime }}
                  </el-col>
                  <el-col :span="12" class="nowrap">
                    <span class="label">样品编号：</span>{{ jsonData.secSampleNum ? jsonData.secSampleNum : '--' }}
                  </el-col>
                  <el-col :span="12" class="nowrap">
                    <span class="label">样品名称：</span>{{ jsonData.samplesName ? jsonData.samplesName : '--' }}
                  </el-col>
                  <el-col :span="12" class="nowrap">
                    <span class="label">型号规格：</span>{{ jsonData.prodType ? jsonData.prodType : '--' }}
                  </el-col>
                  <el-col :span="12" class="nowrap">
                    <span class="label">样品数量：</span>{{ jsonData.sampleNum ? jsonData.sampleNum : '--' }}
                    {{ sampleUnit }}
                  </el-col>
                  <el-col :span="12" class="nowrap">
                    <span class="label">试验方法：</span>{{ jsonData.method ? jsonData.method : '--' }}
                  </el-col>
                  <el-col :span="12">
                    <span class="label">试验要求：</span>
                    <template v-if="!isEditRequirement">
                      <Tooltip :content="jsonData.requirement" placement="bottom-start" effect="light">
                        <div class="requirement-text nowrap">
                          {{ jsonData.requirement ? jsonData.requirement : '--' }}
                        </div>
                      </Tooltip>
                      <el-button class="no-select" circle icon="el-icon-edit" size="small" @click="onRequirementEdit" />
                    </template>
                    <template v-else>
                      <el-input
                        v-model="requirement"
                        type="textarea"
                        :autosize="{ minRows: 1, maxRows: 3 }"
                        class="requirement-input"
                        size="small"
                      />
                      <span class="label" />
                      <div class="requirement-btns">
                        <el-button class="no-select" size="small" @click="onRequirementCancel">取消</el-button>
                        <el-button
                          :loading="saving"
                          class="no-select"
                          type="primary"
                          size="small"
                          @click="onRequirementSave"
                          >保存</el-button
                        >
                      </div>
                    </template>
                  </el-col>
                  <el-col v-if="jsonData.retestSourceId !== '' && jsonData.retestReason" :span="24" class="nowrap">
                    <span class="label">复测说明：</span>{{ jsonData.retestReason }}
                  </el-col>
                </el-row>
              </div>
            </template>
          </el-popover>
          <el-button
            size="small"
            class="squareButton"
            @click="handleOperate('check')"
            @keyup.prevent
            @keydown.enter.prevent
          >
            <svg-icon icon-class="detail" :width="20" :height="20" />
            <div class="button-name">详 情</div>
          </el-button>
          <el-button
            v-if="getPermissionBtn('printBtn')"
            size="small"
            class="squareButton"
            @click="toImg()"
            @keyup.prevent
            @keydown.enter.prevent
          >
            <svg-icon icon-class="print" :width="20" :height="20" />
            <div class="button-name">打 印</div>
          </el-button>
          <el-button
            v-if="isNotItemView && jsonData.status !== 2 && getPermissionBtn('batchPrint')"
            size="small"
            class="batchBtn squareButton"
            @click="handlePrintDialog()"
            @keyup.prevent
            @keydown.enter.prevent
          >
            <svg-icon icon-class="batch-print" :width="20" :height="20" />
            <div class="button-name">批量打印</div>
          </el-button>
          <el-button
            v-if="getPermissionBtn('dataAcquisition') && jsonData.status === 2"
            size="small"
            class="squareButton"
            @click="handleDataAcquisition()"
            @keyup.prevent
            @keydown.enter.prevent
          >
            <svg-icon icon-class="dataCollection" :width="20" :height="20" />
            <div class="button-name">数据采集</div>
          </el-button>
        </div>
        <div class="btnGroupRight">
          <el-button
            v-if="
              jsonData.status == '5' &&
              (accountId === jsonData.reviewerId ||
                jsonData.realOwnerIds?.split(',').some(item => {
                  return item === accountId;
                })) &&
              getPermissionBtn('executeAgain')
            "
            size="small"
            class="squareButton"
            @click="handleDialog(2)"
            @keyup.prevent
            @keydown.enter.prevent
          >
            <svg-icon icon-class="refresh" :width="20" :height="20" />
            <div class="button-name">复 测</div>
          </el-button>
          <el-button
            v-if="jsonData.status == '5' && accountId === jsonData.reviewerId && getPermissionBtn('executeBack')"
            size="small"
            class="squareButton"
            @click="handleOperate('back')"
            @keyup.prevent
            @keydown.enter.prevent
          >
            <svg-icon icon-class="send-back" :width="20" :height="20" />
            <div class="button-name">退 回</div>
          </el-button>
          <el-button
            v-if="jsonData.status == '2' && jsonData.isower"
            size="small"
            class="squareButton"
            @click="handleOperate('edit')"
            @keyup.prevent
            @keydown.enter.prevent
          >
            <svg-icon icon-class="edit2" :width="20" :height="20" />
            <div class="button-name">编 辑</div>
          </el-button>
          <el-button
            v-if="jsonData.status == '2' && jsonData.isower"
            size="small"
            class="squareButton"
            type="primary"
            @click="handleDialog('0')"
            @keyup.prevent
            @keydown.enter.prevent
          >
            <svg-icon icon-class="submit" :width="20" :height="20" />
            <div class="button-name">提 交</div>
          </el-button>
          <el-button
            v-if="
              isNotItemView &&
              jsonData.status === 3 &&
              accountId === jsonData.reviewerId &&
              getPermissionBtn('executeAudits')
            "
            size="small"
            class="batchBtn squareButton"
            @click="handleAuditDialog"
            @keyup.prevent
            @keydown.enter.prevent
          >
            <svg-icon icon-class="examines" :width="20" :height="20" />
            <div class="button-name">批量审核</div>
          </el-button>
          <el-button
            v-if="jsonData.status == '3' && accountId === jsonData.reviewerId && getPermissionBtn('executeAudit')"
            size="small"
            class="squareButton"
            type="primary"
            @click="handleAuditDialog(0)"
            @keyup.prevent
            @keydown.enter.prevent
          >
            <svg-icon icon-class="examine" :width="20" :height="20" />
            <div class="button-name">审 核</div>
          </el-button>
        </div>
      </div>
      <div class="scroll-wrapper">
        <div id="printMe" class="moban">
          <base-excel ref="excel" :json-data="jsonData" :experiment-id="experimentId" @handleData="handleData" />
        </div>
      </div>
    </div>
    <module-audit
      :data-value="dataValue"
      :json-data="jsonData"
      @closedialog="closedialog"
      @sumbitData="sumbitData"
      @sumbit-success="sumbitSuccess"
    />
    <!--复测弹屏-->
    <retest-test :dialog-reset-visiable="dialogResetVisiable" :json-data="jsonData" @closedialog="closedialog2" />
    <!--审核弹屏-->
    <el-dialog
      v-model="dialogBatch"
      title="原始记录审核"
      :close-on-click-modal="false"
      width="480px"
      custom-class="submit_dialog"
    >
      <el-table
        v-if="isUl"
        ref="multipleSelection"
        :key="tableKey"
        :data="table"
        fit
        border
        size="medium"
        max-height="301"
        style="width: 100%"
        class="dark-table test-item-table base-table"
        @selection-change="selectionAuditChange"
      >
        <el-table-column type="selection" prop="checkbox" width="55" align="center" />
        <el-table-column label="检测项目" :min-width="140" show-overflow-tooltip>
          <template #default="scope">{{ scope.row.capabilityName }}</template>
        </el-table-column>
        <el-table-column label="试验员" :min-width="140" show-overflow-tooltip>
          <template #default="{ row }">
            <div v-if="row.ownerIds">
              <UserTag
                v-for="(item, index) in getNamesByid(row.ownerIds)"
                :key="index"
                size="small"
                class="tag-name"
                :name="item"
              />
            </div>
          </template>
        </el-table-column>
      </el-table>
      <el-form v-if="dialogBatch" ref="ruleForm2" :model="formData2" label-position="top" label-width="120px">
        <el-form-item
          label="审核结果："
          prop="result"
          :rules="{ required: true, message: '请选择审核结果', trigger: 'change' }"
        >
          <el-radio-group v-model="formData2.result" class="radioGroup">
            <el-radio label="1" border class="pass">通过</el-radio>
            <el-radio label="0" border class="sendBack fr">退回</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审核说明：">
          <el-input v-model="formData2.backReason" type="textarea" :rows="2" placeholder="请输入说明" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="close(1)">取 消</el-button>
          <el-button type="primary" @click="SubmitAudit">确 认</el-button>
        </span>
      </template>
    </el-dialog>
    <el-dialog
      v-model="dialogBack"
      title="退回确认"
      :close-on-click-modal="false"
      :before-close="closeBack"
      width="480px"
    >
      <el-form
        v-if="dialogBack"
        ref="ruleFormBack"
        :model="formDataBack"
        label-position="right"
        label-width="110px"
        size="small"
      >
        <el-form-item label="退回原因：" prop="backReason">
          <el-input v-model="formDataBack.backReason" type="textarea" :rows="2" placeholder="请输入原因" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeBack">取 消</el-button>
          <el-button type="primary" @click="onSubmit('back')">确 认</el-button>
        </span>
      </template>
    </el-dialog>
    <!--批量打印-->
    <el-dialog
      v-model="dialogPrint"
      title="批量打印"
      :close-on-click-modal="false"
      :before-close="closePrint"
      width="720px"
    >
      <el-table
        v-if="dialogPrint"
        id="sortableList"
        ref="multipleSelection"
        :key="tableKeyPrint"
        :data="leftUlTable"
        fit
        size="medium"
        max-height="301"
        style="width: 100%"
        class="dark-table test-item-table base-table"
        @selection-change="handleSelectionChange"
        @row-click="handleRowClick"
      >
        <el-table-column type="selection" prop="checkbox" width="55" align="center" />
        <el-table-column label="排序" width="50px">
          <i class="tes-move iconfont" style="font-size: 12px; cursor: move" />
        </el-table-column>
        <el-table-column label="试验员" :min-width="100" align="left">
          <template #default="{ row }">
            <div v-if="row.ownerIds">
              <UserTag v-for="(item, index) in getNamesByid(row.ownerIds)" :key="index" :name="item" />
            </div>
          </template>
        </el-table-column>
        <el-table-column label="检测项目" :min-width="140" align="left" show-overflow-tooltip>
          <template #default="scope">
            <span v-if="scope.row.retestSourceId !== ''" class="testing-icon text-copy">复</span>
            <span v-if="scope.row.isRetest === 1" class="testing-icon text-origin">源</span>
            <span v-if="scope.row.isEditMore > 0" class="testing-icon text-change">改</span>
            <span v-if="scope.row.isBack === 1" class="testing-icon text-back">退</span>
            <span v-if="scope.row.isManual === 1" class="testing-icon text-info">增</span>
            <span v-if="scope.row.isSpecial === 1" class="testing-icon text-copy">特</span>
            <span v-if="scope.row.showType === 1" style="font-size: 12px">（横向）</span>
            {{ scope.row.capabilityName }}
          </template>
        </el-table-column>
      </el-table>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closePrint()">取 消</el-button>
          <el-button type="primary" @click="SubmitPrint">确 认</el-button>
        </span>
      </template>
    </el-dialog>
    <DialogAssociatedDevice
      :dialog-show="dialogAssociated"
      :secsamplenum="jsonData.secSampleNum"
      :capability-id="jsonData.capabilityId"
      :device-list="associatedDevice"
      @closeDialog="closeDialog3"
    />
  </div>
</template>

<script>
import { reactive, toRefs, ref, watch, computed, onMounted, onUnmounted, nextTick } from 'vue';
import { useRoute } from 'vue-router';
import router from '@/router/index.js';
import { getNamesByid, getNameByid, getPermissionBtn } from '@/utils/common';
import { getLoginInfo } from '@/utils/auth';
import UserTag from '@/components/UserTag';
import SvgIcon from '@/components/SvgIcon';
import baseExcel from '@/views/excelComponents/baseExcel';
import ModuleAudit from '@/components/BusinessComponents/ModuleAudit';
import RetestTest from '../RetestTest.vue';
import { back, review, findDeviceByCapabilityId, saveExperimentRequirement } from '@/api/execution';
import { ElMessage } from 'element-plus';
import pdf from '@/utils/preview-or-download-pdf';
import { addByTemp } from '@/api/messageAgent';
import { filterSampleUnitToName } from '@/utils/formatJson';
import DialogAssociatedDevice from './dialog-associated-device.vue';
import { throttle } from 'lodash';
import Sortable from 'sortablejs';
import Tooltip from '@/components/Tooltip';
export default {
  name: 'FirstTabRaw',
  components: { baseExcel, UserTag, SvgIcon, ModuleAudit, RetestTest, DialogAssociatedDevice, Tooltip },
  props: {
    jsonData: {
      type: Object,
      default: function () {
        return {};
      }
    },
    leftUl: {
      type: Array,
      default: function () {
        return [];
      }
    }
  },
  emits: ['refreshDetail', 'changeStatus', 'getList', 'showRawTemplate', 'showItemInfo'],
  setup(props, ctx) {
    watch(props, newValue => {
      state.jsonData = newValue.jsonData;
      state.experimentId = newValue.jsonData.experimentId;
      state.leftUlTable = JSON.parse(JSON.stringify(props.leftUl));
      state.isEditRequirement = false;
      setContainerDisplayMode();
    });
    const route = useRoute();
    const state = reactive({
      isUl: false,
      printList: [],
      leftUlTable: [],
      associatedDevice: [],
      jsonData1: {},
      experimentId: '',
      dialogAssociated: false,
      postDeviceData: [],
      accountId: getLoginInfo().accountId,
      table: [],
      multipleSelection: [],
      selectAuditList: [],
      testipt: '',
      dataValue: {
        dialogSubmit: false
      },
      dialogResetVisiable: false,
      html: '',
      tableKey: ref(0),
      tableKeyPrint: ref(0),
      ruleForm: ref(null),
      ruleForm2: ref(null),
      ruleFormBack: ref(null),
      dialogBatch: false, // 批量审核、批量打印弹出框
      dialogPrint: false,
      dialogBack: false, // 退回弹出框
      dialogSubmit: false, // 提交、审核、复测弹出框
      drawerVisiable: false, // 项目信息下拉框
      formData2: {
        backReason: '',
        experimentId: '',
        result: ''
      },
      formDataBack: {},
      printListdata: [],
      formData: {
        syuser: []
      },
      dialogTitle: {
        0: '提交审核',
        1: '原始记录审核',
        2: '复测试验'
      },
      jsonData: props.jsonData,
      dictionary: [
        { value: '0', label: '正常' },
        { value: '1', label: '不正常' }
      ],
      selected: [], // 已经选中的编号集合
      tableList: [
        {
          bianhao: '',
          status: '0',
          status2: '0',
          user: '邓洪刚',
          date: new Date()
        }
      ],
      userList: [],
      imgList: [],
      options: [],
      requirement: '',
      isEditRequirement: false,
      saving: false
    });
    const containerRef = ref(null);
    const templateRef = ref(null);
    const getdata = () => {};
    getdata();
    const testadd = () => {
      const formdata = {};
      formdata.testdata = document.getElementById('time').value;
    };
    // 行拖拽
    const rowDrop = () => {
      // 获取当前表格
      const el = document.getElementById('sortableList').querySelector('.el-table__body-wrapper tbody');
      Sortable.create(el, {
        animation: 300,
        handle: '.tes-move',
        draggable: '.el-table__row',
        ghostClass: 'ghost',
        // 拖动对象移动样式
        dragClass: 'drag',
        forceFallback: true,
        onEnd({ newIndex, oldIndex }) {
          if (oldIndex !== newIndex) {
            const currRow = state.leftUlTable.splice(oldIndex, 1)[0];
            state.leftUlTable.splice(newIndex, 0, currRow);
            state.tableKeyPrint += 1;
            nextTick(() => {
              rowDrop();
            });
          }
        }
      });
    };
    // 提交、复测、审核弹出框
    const handleDialog = radio => {
      if (radio === 2) {
        state.dialogResetVisiable = true;
      } else {
        state.dataValue.dialogSubmit = true;
      }
    };
    // 编辑、详情、退回
    const handleOperate = type => {
      const experimentid = state.jsonData.experimentId;
      if (type === 'back') {
        state.dialogBack = true;
        state.formDataBack.backReason = '';
      } else {
        if (route.name === 'recordReviewDetail') {
          router.push({
            path: '/recordReview/addRecord',
            query: {
              type: type,
              experimentId: experimentid,
              samplesId: state.jsonData.samplesId,
              capabilityId: route.query.capabilityId || state.jsonData.capabilityId
            }
          });
        } else {
          router.push({
            path: '/execution/addRecord',
            query: {
              type: type,
              experimentId: experimentid,
              samplesId: state.jsonData.samplesId,
              capabilityId: route.query.capabilityId || state.jsonData.capabilityId
            }
          });
        }
      }
    };
    // 提交按钮
    const hanleBatch = radio => {
      state.dataValue.dialogSubmit = true;
    };
    // 审核
    const handleAuditDialog = obj => {
      state.dialogBatch = true;
      state.isUl = (obj !== 0) !== false;
      handleTable(3);
    };
    // 打印
    const handlePrintDialog = () => {
      state.dialogPrint = true;
      state.tableKeyPrint = ref(0);
      handleTable(state.jsonData.status);
      if (state.leftUlTable) {
        nextTick(() => {
          rowDrop();
        });
      }
    };
    // 处理模板数据
    const excel = ref(null);
    const handleData = thisValue => {
      state.experimentData = thisValue;
    };
    const sumbitData = () => {
      excel.value.handleData();
    };

    const sumbitSuccess = () => {
      excel.value.handleData();
      ctx.emit('showItemInfo', 4);
    };

    const handleTable = status => {
      if (state.leftUlTable.length > 0) {
        state.table = state.leftUlTable.filter(item => {
          return item.reviewerId === getLoginInfo().accountId && item.status === status;
        });
      }
    };
    // 审核弹出框 选择事件
    const selectionAuditChange = val => {
      state.selectAuditList = val;
    };
    // 提交审核
    const SubmitAudit = () => {
      state.ruleForm2.validate().then(valid => {
        if (valid) {
          const reviewList = [];
          const capabilityNames = [];
          const capabilityIds = [];
          const postdata = state.formData2;
          if (state.isUl) {
            if (state.selectAuditList.length > 0) {
              state.selectAuditList.forEach(item => {
                const obj = { ...postdata };
                obj.experimentId = item.experimentId;
                reviewList.push(obj);
              });
            } else {
              ElMessage.warning({
                message: '请选择检测项目',
                type: 'warning'
              });
              return false;
            }
            // 批量审批-选择的项目
            if (state.selectAuditList.length > 0) {
              state.selectAuditList.forEach(sal => {
                capabilityNames.push(sal.capabilityName);
                capabilityIds.push(sal.capabilityId);
              });
            }
          } else {
            postdata.experimentId = state.jsonData.experimentId;
            reviewList.push(postdata);
            capabilityNames.push(state.jsonData.capabilityName);
            capabilityIds.push(state.jsonData.capabilityId);
          }
          review({ reviewList: reviewList }).then(res => {
            if (res) {
              state.formData2 = {};
              ElMessage.success({
                message: '提交成功',
                type: 'success'
              });
              router.push({
                path: '/execution/detail',
                query: {
                  samplesId: state.jsonData.samplesId,
                  avtivestatus: 1
                }
              });
              // 添加消息待办
              const params = {
                eventCode: 'M009',
                receiverType: '1',
                senderName: getNameByid(state.accountId),
                receiverIds: state.accountId,
                receiverNames: getNameByid(state.accountId),
                c_ids: capabilityIds.join(','),
                c_b_samplesIdArray: state.jsonData.samplesId,
                c_b_sampleNoArray: state.jsonData.secSampleNum,
                c_b_projectNameArray: capabilityNames.join(','),
                c_b_capabilityIdArray: capabilityIds.join(',')
              };

              if (postdata.result === '0' || postdata.result === 0) {
                params.eventCode = 'M010';
              }
              addByTemp(params).then(res => {
                if (res !== false) {
                  // console.log(res.data)
                }
              });

              ctx.emit('getList');
              state.dialogBatch = false;
            }
          });
        }
      });
    };
    // 弹出框的表单提交
    const onSubmit = type => {
      if (type === 'batch') {
        // 多选
      } else if (type === 'back') {
        // console.log('退回确认')
        // console.log(state.jsonData)
        state.formDataBack.experimentId = state.jsonData.experimentId;
        back(state.formDataBack).then(res => {
          if (res.data.code) {
            ElMessage.success({
              message: '操作成功',
              type: 'success'
            });
            router.push({
              path: '/execution/detail',
              query: {
                samplesId: state.jsonData.samplesId
                // capabilityId: row.capabilityId
              }
            });
            // 添加消息待办
            const params = {
              eventCode: 'M010',
              receiverType: '1',
              senderName: getNameByid(state.accountId),
              receiverIds: state.accountId,
              receiverNames: getNameByid(state.accountId),
              c_ids: state.jsonData.capabilityId,
              c_b_samplesIdArray: state.jsonData.samplesId,
              c_b_sampleNoArray: state.jsonData.secSampleNum,
              c_b_projectNameArray: state.jsonData.capabilityName,
              c_b_capabilityIdArray: state.jsonData.capabilityId
            };

            addByTemp(params).then(res => {
              if (res !== false) {
                // console.log(res.data)
              }
            });

            ctx.emit('getList');
            ctx.emit('changeStatus', 5);
            state.dialogBack = false;
          }
        });
      }
    };
    // 弹出框的打印提交
    const handleSelectionChange = val => {
      state.printList = val;
    };
    // 批量打印
    const excel1 = ref(null);
    const SubmitPrint = () => {
      if (state.printList.length === 0) {
        ElMessage.warning({
          message: '请选择要打印的',
          type: 'warning'
        });
      } else {
        const showTypeArray = [...new Set(state.printList.map(item => item.showType))];
        if (showTypeArray.length > 1) {
          ElMessage.warning({
            message: '请勾选一种排版类型打印',
            type: 'warning'
          });
        } else {
          const { href } = router.resolve({
            path: '/execution/printRecord',
            query: {
              type: 'print',
              printlist: state.printList.map(item => item.experimentId).toString(),
              showType: state.printList[0].showType
            }
          });
          window.open(href, '_blank');
          closePrint();
        }
      }
    };
    // 转图片打印
    const toImg = () => {
      // 转图片打印
      pdf.PageImg(state.jsonData?.showType);
    };
    const closedialog = val => {
      state.dataValue.dialogSubmit = false;
      if (val) {
        ctx.emit('getList');
      }
    };
    const closedialog2 = val => {
      state.dialogResetVisiable = false;
      if (val) {
        ctx.emit('getList');
      }
    };
    const close = index => {
      state.ruleForm2.resetFields();
      state.dialogBatch = false;
      state.dialogPrint = false;
    };
    const closeBack = () => {
      state.ruleFormBack.resetFields();
      state.dialogBack = false;
    };
    const closePrint = () => {
      state.multipleSelection.clearSelection();
      state.dialogPrint = false;
    };

    const isNotItemView = computed(() => {
      return route.name !== 'item-execution-detail';
    });
    const isTransverse = computed(() => state.jsonData?.showType === 1);
    const handleRowClick = row => {
      state.multipleSelection.toggleRowSelection(row);
    };

    const setContainerDisplayMode = throttle(() => {
      const containerWidth = containerRef?.value?.offsetWidth;
      const templateWidth = templateRef?.value?.offsetWidth;
      const expectedWidth = templateWidth + 100 * 2;
      if (containerWidth < expectedWidth) {
        containerRef?.value?.classList?.add('small-screen');
      } else {
        containerRef?.value?.classList?.remove('small-screen');
      }
    }, 300);
    // 数据采集
    const handleDataAcquisition = () => {
      findDeviceByCapabilityId(state.jsonData.capabilityId).then(res => {
        if (res) {
          state.associatedDevice = res.data.data;
          if (state.associatedDevice.length) {
            state.dialogAssociated = true;
          } else {
            ElMessage.warning({
              message: '请先绑定设备!'
            });
          }
        }
      });
    };
    const closeDialog3 = () => {
      state.dialogAssociated = false;
    };
    const onRequirementEdit = () => {
      state.isEditRequirement = true;
      state.requirement = state.jsonData.requirement || '';
    };
    const onRequirementCancel = () => {
      state.isEditRequirement = false;
      state.requirement = state.jsonData.requirement || '';
    };
    const onRequirementSave = () => {
      const params = {
        experimentId: state.jsonData.experimentId,
        requirement: state.requirement
      };
      state.saving = true;
      saveExperimentRequirement(params).then(res => {
        state.saving = false;
        if (res) {
          ctx.emit('refreshDetail', state.jsonData);
          state.isEditRequirement = false;
          ElMessage.success({ message: res.data.data });
        }
      });
    };
    onMounted(() => {
      setContainerDisplayMode();
      window.addEventListener('resize', setContainerDisplayMode);
    });

    onUnmounted(() => {
      window.removeEventListener('resize', setContainerDisplayMode);
    });

    return {
      ...toRefs(state),
      containerRef,
      closeDialog3,
      handleDataAcquisition,
      rowDrop,
      templateRef,
      isNotItemView,
      isTransverse,
      handleRowClick,
      getdata,
      excel,
      getPermissionBtn,
      excel1,
      toImg,
      handleSelectionChange,
      handleData,
      sumbitData,
      sumbitSuccess,
      closePrint,
      closeBack,
      getNamesByid,
      onSubmit,
      SubmitAudit,
      testadd,
      handleTable,
      handlePrintDialog,
      SubmitPrint,
      close,
      closedialog,
      closedialog2,
      handleDialog,
      handleAuditDialog,
      handleOperate,
      hanleBatch,
      selectionAuditChange,
      onRequirementEdit,
      onRequirementCancel,
      onRequirementSave
    };
  },
  computed: {
    sampleUnit: function () {
      return filterSampleUnitToName(this.jsonData.sampleUnitId) ?? this.jsonData.sampleUnitId;
    }
  },
  created() {}
};
</script>

<style lang="scss" scoped>
.firstTabRaw {
  height: 100%;
  position: relative;

  :deep(.el-select--medium),
  :deep(.el-range-editor.el-input__inner) {
    width: 100%;
  }
  :deep(.el-form .el-form-item) {
    margin-bottom: 20px;
  }
}

.template-container {
  box-sizing: content-box;
  max-width: 800px; // 794PX + 6PX 6为滚动条宽度
  height: calc(100% - 40px);
  margin: 20px auto;
  padding: 0 20px;
  position: relative;
}

.scroll-wrapper {
  height: 100%;
  margin: 0 auto;
  padding-right: 6px;
  overflow-x: auto;
  overflow-y: auto;
}

.moban {
  min-height: 500px;
  border: 1px solid #ebeef5;

  :deep(.slot-temp) {
    font-family: Times New Roman, 'SourceHanSerifCN';
    box-shadow: none;
    filter: none;
  }
}

.btnGroupLeft,
.btnGroupRight {
  position: absolute;
  top: 0;

  .squareButton {
    display: block;
    width: 80px;
    margin: 0 0 10px 0;
    padding: 10px 0 !important;
  }

  .squareButton.el-button--primary {
    fill: #fff;
  }

  .squareButton:not(.el-button--primary):hover .svg-icon {
    fill: var(--tesPrimary);
  }

  .button-name {
    margin-top: 10px;
  }
}

.btnGroupLeft {
  right: 100%;
}

.btnGroupRight {
  left: 100%;
}

.submit_dialog {
  .el-form-item {
    margin-bottom: 20px;
  }
  .el-form-item__error {
    top: 85%;
  }
  .el-radio {
    margin-right: 0;
    background: #f4f4f5;
  }
  .sendBack.is-checked {
    background: $tes-red;
  }
  .pass.is-checked {
    background: $green;
  }
  .sendBack {
    :deep(.el-radio__input.is-checked + .el-radio__label) {
      color: #fff;
    }
  }
  .pass {
    :deep(.el-radio__input.is-checked + .el-radio__label) {
      color: #fff;
    }
  }
  .el-radio.is-bordered {
    border: 0;
    border-radius: 4px;
    width: 49%;
    text-align: center;
  }
  .radioGroup {
    width: 100%;
    display: flex;
    align-items: center;
    :deep(.el-radio__input) {
      display: none;
    }
  }
}
.fgline {
  margin: 0 5px;
}
.handClose {
  cursor: pointer;
  position: absolute;
  right: 30px;
  width: 20px;
  height: 20px;
  z-index: 999;
}
.item-detail-model {
  .content {
    padding: 10px;
    .title {
      font-size: 16px;
      color: $tes-font;
      margin-bottom: 20px;
    }
    .tag-group {
      margin-bottom: 2px;
      .el-tag {
        margin-right: 8px;
        margin-bottom: 8px;
      }
    }
  }
}
.itemDetailModule {
  text-align: left;
  position: absolute;
  width: 95%;
  margin: 0 auto;
  z-index: 999;
  top: 20px;
  padding: 11px 16px 8.5px 16px;
  background: #fff;
  box-shadow: 0px 2px 12px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  h2 {
    font-size: 18px;
    line-height: 26px;
    font-weight: 500;
    color: #303133;
  }
  .title {
    line-height: 30px;
    margin: 7px 0;
  }
}
.test-item-table {
  margin-bottom: 15px;
}

.itemLine {
  line-height: 32px;
  .label {
    text-align: left;
    color: #909399;
    width: 80px;
    display: inline-block;
  }
}

.drawertitle {
  margin-bottom: 10px;
  .el-tag {
    margin-right: 8px;
  }
}
#printIt {
  width: 794px;
  min-height: 100px;
  overflow: hidden;
  h1 {
    background: #0a76a4;
  }
  h2 {
    background: yellow;
  }
  img {
    display: block;
    width: 100%;
    height: auto;
  }
}

.small-screen {
  .template-container {
    max-width: none;
    width: auto;
  }

  .scroll-wrapper {
    height: calc(100% - 76px);
  }

  .btnGroup {
    display: flex;
    justify-content: space-between;
    padding-right: 6px;
  }

  .btnGroupLeft,
  .btnGroupRight {
    position: static;
    top: auto;
    right: auto;
    left: auto;

    .squareButton {
      display: inline-block;
      margin-right: 10px;

      &:last-child {
        margin-right: 0;
      }
    }
  }
}
.requirement-text {
  display: inline-block;
  max-width: 70%;
  vertical-align: bottom;
}
.requirement-input,
.requirement-btns {
  width: 280px;
}
.requirement-btns {
  display: inline-block;
  margin-top: 10px;
  text-align: left;
}
.no-select.el-button.is-circle {
  border: none;
}
</style>
