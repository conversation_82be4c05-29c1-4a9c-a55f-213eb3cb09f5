<template>
  <div
    id="template"
    ref="templateRef"
    class="slot-temp box-excel"
    :class="{ vertical: !isTransverse, horizontal: isTransverse, 'uni-template-temp': isUniappMobile }"
  >
    <TemplateTool
      v-if="(type === 'edit' || type === 'check') && !isUniappMobile"
      :is-transverse="isTransverse"
      :is-has-no-read="isHasNoRead"
      @handleType="handleType"
    />
    <BaseTemplate
      :sample-num="jsonData?.secSampleNum"
      :model-specification="jsonData?.experimentProdTypeList"
      :file-no="form.fileNo"
      :capability-name="jsonData?.capabilityName"
      :head-type="templateStyle"
      :template-tail-style="templateTailStyle"
      :is-transverse="isTransverse"
      :is-dog-tag="isDogTagType"
    >
      <template #test-site>
        <select
          v-model="form.address"
          class="select-option"
          :class="{ red: formcolor.address }"
          :disabled="type !== 'edit'"
          :readonly="type !== 'edit'"
          placeholder="  "
          @click="handleHistory('address')"
        >
          <option v-for="item in testSiteList" :key="item.id" :value="item.code">{{ item.name }}</option>
        </select>
      </template>
      <template #test-time>
        <input v-model="testTime" class="input" type="text" readonly />
      </template>
      <template #test-method>
        <div
          v-if="type !== 'edit'"
          :class="{ red: formcolor.method }"
          style="word-wrap: break-word; word-break: break-all"
          @click="handleHistory('method')"
        >
          {{ form.method }}
        </div>
        <el-popover v-else :visible="visibleMethod" placement="bottom" :width="200" popper-class="methodPopover">
          <template #reference>
            <textarea
              ref="methodRef"
              v-model="form.method"
              class="input"
              rows="1"
              placeholder=" "
              style="word-wrap: break-word; word-break: break-all"
              @input="handleInputMethod"
              @blur="handleBlurMethod"
            />
          </template>
          <ul>
            <li v-for="item in methodList" :key="item.id" @click="handleAddMethod(item.name)">{{ item.name }}</li>
          </ul>
        </el-popover>
      </template>
      <template #test-standard>
        <div
          v-if="type !== 'edit'"
          :class="{ red: formcolor.selectedStandardName }"
          style="word-wrap: break-word; word-break: break-all"
          @click="handleHistory('selectedStandardName')"
        >
          {{ form.selectedStandardName }}
        </div>
        <el-popover v-else :visible="visibleStandard" placement="bottom" :width="200" popper-class="methodPopover">
          <template #reference>
            <textarea
              ref="standardRef"
              v-model="form.selectedStandardName"
              class="input"
              rows="1"
              placeholder=" "
              style="word-wrap: break-word; word-break: break-all"
              @input="handleInputStandard"
              @blur="handleBlurStandard"
            />
          </template>
          <ul>
            <li v-for="item in standardList" :key="item.id" @click="handleAddStandard(item.basisName)">
              {{ item.basisName }}
            </li>
          </ul>
        </el-popover>
      </template>
      <template #test-temperature>
        <input
          v-model="form.temperature"
          type="text"
          class="input"
          :class="{ red: formcolor.temperature }"
          :readonly="type !== 'edit'"
          @click="handleHistory('temperature')"
        />
      </template>
      <template #test-humidness>
        <input
          v-model="form.humidness"
          type="text"
          class="input"
          :class="formcolor.humidness ? 'red' : ''"
          :readonly="type !== 'edit'"
          @click="handleHistory('humidness')"
        />
      </template>
      <template #test-device>
        <div v-if="deviceListNew">
          <el-row v-for="(item, index) in deviceListNew" :key="index">
            <el-col :span="24">
              <el-row class="bd-b">
                <el-col :span="6" class="bd-r"> 检测设备： </el-col>
                <el-col :span="18">
                  <input v-model="item.deviceName" type="text" class="input" :readonly="true" />
                </el-col>
              </el-row>
            </el-col>
            <el-col :span="24">
              <el-row class="bd-b">
                <el-col :span="6" class="bd-r">设备编号：</el-col>
                <el-col :span="6" class="bd-r">
                  <input v-model="item.deviceNumber" type="text" class="input" maxlength="30" :readonly="true" />
                </el-col>
                <el-col :span="5" class="bd-r">上/下次检定日期：</el-col>
                <el-col :span="7">
                  <div style="color: #606266">{{ item.validBeginDate }}/{{ item.validEndDate }}</div>
                </el-col>
              </el-row>
            </el-col>
          </el-row>
        </div>
      </template>
      <div ref="scriptRef" class="html" v-html="jsonData.excelHtml" />
      <div class="excel-bd">
        <slot />
      </div>
      <template #tester>
        <div v-if="(jsonData.status === 3 || jsonData.status === 5) && jsonData.realOwnerIds" class="inline-block">
          <div v-for="(item, index) in jsonData.realOwnerIds.split(',')" :key="index" class="inline-block">
            <img
              v-if="
                jsonData.realOwnerIdsimgs &&
                jsonData.realOwnerIdsimgs[index] &&
                jsonData.realOwnerIdsimgs[index] !== 'null'
              "
              class="qImage"
              :src="jsonData.realOwnerIdsimgs[index]"
              alt=""
              oncontextmenu="return false"
              referrerPolicy="no-referrer"
            />
            <span v-else style="vertical-align: top">{{ getNameByid(item) }}</span>
          </div>
        </div>
      </template>
      <template #checker>
        <div v-if="jsonData.status === 5" class="inline-block">
          <div v-for="(item, index) in jsonData.realReviewerId.split(',')" :key="index" class="qm">
            <img
              v-if="
                jsonData.realReviewerIdimg &&
                jsonData.realReviewerIdimg[index] &&
                jsonData.realReviewerIdimg[index] !== 'null'
              "
              class="qImage inline-block"
              :src="jsonData.realReviewerIdimg[index]"
              alt=""
              oncontextmenu="return false"
              referrerPolicy="no-referrer"
            />
            <span v-else style="vertical-align: top">{{ getNamesByIds(item) }}</span>
          </div>
        </div>
      </template>
      <template #test-date>
        <span v-if="jsonData.status === 3 || jsonData.status === 5">
          {{ jsonData?.completestartdate }}
        </span>
      </template>
      <template #check-date>
        <span v-if="jsonData.status === 5">
          {{ jsonData?.reviewdatetime }}
        </span>
      </template>
    </BaseTemplate>
  </div>
  <!-- OCR识别弹出框 -->
  <DialogOcr :dialog-show="dialogOCR" :type="ocrType" @closeDialog="closeOcrDailog" />
  <!-- 截图 -->
  <DialogPageSnapshot
    :dialog-visiable="dialogSnapshot"
    :parameter="{ relevancyKey: experimentId }"
    :img-url="imgBaseUrl"
    @closeDialog="handleCloseSnapshot"
  />
  <!-- 截图历史记录 -->
  <DrawerSnapshotHistory
    :drawer="drawerDrawerHistory"
    :parameter="{ hasNotSeen: isHasNoRead, relevancyKey: experimentId }"
    @close="handleCloseHistory"
  />
  <div v-show="historyData.length >= 2 && showHistory" id="showHistory" class="pop-zr">
    <el-card class="box-card">
      <h4>历史修改</h4>
      <i class="el-icon-close i-close" @click="showHistory = false" />
      <div class="pop-content">
        <el-table :data="historyData" style="width: 100%">
          <el-table-column prop="value" label="数值" width="100">
            <template #default="{ row }">
              <!-- 科学计数法显示 -->
              <div v-if="row.value.indexOf('e-') !== -1 || row.value.indexOf('e+') !== -1">
                {{ row.value }}
              </div>
              <div v-else-if="row.value.split('-').length > 1">
                {{ historyLabel[row.value.split('-')[2]] || row.value.split('-')[2] }}
              </div>
              <div v-else>{{ historyLabel[row.value] || row.value }}</div>
            </template>
          </el-table-column>
          <el-table-column prop="createBy" label="修改人" width="80">
            <template #default="{ row }">
              {{ getNameByid(row.createBy) }}
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="时间" width="100" />
        </el-table>
      </div>
    </el-card>
  </div>
  <!-- 粘贴提示框 -->
  <div
    v-show="affixTips"
    class="affixTips"
    :style="{ left: clientOffset.clientX + 'px', top: clientOffset.clientY + 'px' }"
  >
    <ul>
      <li v-for="item in affixTipData" :key="item" @click="handleSelectAffix(item)">{{ item }}</li>
    </ul>
  </div>
  <!-- 软键盘框 上标下标-->
  <SoftKeyboard :is-show="softKeyboard" :client-offset="clientOffset" :select-mark="selectMark" :focus-id="focusId" />
</template>

<script>
import $ from 'jquery';
import axios from 'axios';
import { useRoute, onBeforeRouteLeave } from 'vue-router';
// import '@/views/excelComponents/commonPart/excelcommon.scss';
import { ElMessageBox, ElMessage } from 'element-plus';
// import './excelstyle.scss';
// import './module.scss';
import DialogOcr from './components/dialog-ocr';
import { getNameByid, getNamesByid, getNamesByIds } from '@/utils/common';
import { detectionaddress } from '@/api/excel';
import { reactive, toRefs, watch, computed, onMounted, onUnmounted, nextTick, ref, getCurrentInstance } from 'vue';
import { getTenantConfig } from '@/utils/auth';
import BaseTemplate from '@/views/excelComponents/components/BaseTemplate';
import TemplateTool from '@/views/excelComponents/components/TemplateTool';
import { getXhModuleNumber, disabledXxysSelection } from './func/dynamicCore';
import { getTemplateStandard } from './func/templateStandard';
import { setTemplateScript, setTemplateScript2 } from './func/templateScript';
import { formatMethod } from './func/format';
import { getDictionary } from '@/api/user';
import { formatDate } from '@/utils/formatTime';
import { getMethodList } from '@/api/testItem';
import { screenshotRecord } from '@/api/sysConfig';
import pdf from '@/utils/preview-or-download-pdf';
import SoftKeyboard from '@/components/SoftKeyboard';
import DialogPageSnapshot from '@/components/BusinessComponents/DialogPageSnapshot';
import { getCapabilityStandardBasisList } from '@/api/capability';
import DrawerSnapshotHistory from '@/components/BusinessComponents/DrawerSnapshotHistory.vue';
import { mounted, unmounted } from './template-style-loader';
import { getMinioURL } from '@/utils/base-url';
// import '@/utils/mathjax' // 必须在引入mathjax前引入mathjax的配置文件
// 这里使用的是tex-svg.js, 样式挺好看, 参考文章中的样式不知道为什么会很奇怪, 注意下
// import MathJax from 'mathjax'

export default {
  name: 'BaseExcel',
  components: { BaseTemplate, TemplateTool, DialogOcr, DialogPageSnapshot, SoftKeyboard, DrawerSnapshotHistory },
  props: {
    jsonData: {
      type: Object,
      default: function () {
        return {};
      }
    },
    experimentId: {
      type: String,
      default: ''
    },
    isStandardCustom: {
      type: Number,
      default: 0
    },
    parentType: {
      type: Number,
      default: function () {
        return 0;
      }
    }
  },
  emits: ['handleData', 'setImg'],
  setup(props, ctx) {
    const route = useRoute();
    const { appContext } = getCurrentInstance();
    const bus = appContext.config.globalProperties.bus;
    const scriptRef = ref();
    const state = reactive({
      isUniappMobile: route.name === 'UniappTemplate',
      colorlist: [],
      dialogOCR: false,
      templateRef: ref(),
      selectMark: {}, // 光标选中的信息，包括开始结束索引和选中的文本
      dialogSnapshot: false,
      affixTips: false, // 粘贴建议框
      softKeyboard: false, // 软键盘
      clientOffset: {}, // 位置
      ocrType: '',
      focusId: '', // 获取焦点的id
      imgBaseUrl: '',
      drawerDrawerHistory: false,
      isHasNoRead: false,
      experimentId: route.query.experimentId,
      affixTipData: JSON.parse(localStorage.getItem('templateValue')), // 粘贴建议数据
      visibleMethod: false, // 试验方法选择框
      visibleStandard: false, // 判定依据，检测标准弹出框
      transverse: false,
      isAffixOpen: false,
      standardCustom: 0,
      templateStyle:
        getTenantConfig().templateStyle === undefined || getTenantConfig().templateStyle === null
          ? 2
          : getTenantConfig().templateStyle,
      templateTailStyle:
        getTenantConfig().templateStyle === undefined || getTenantConfig().templateStyle === null
          ? 0
          : getTenantConfig().templateTailStyle,
      isEdit: false, // 判断模板是否已经修改
      isflag: true,
      realOwnerIdsimgs: [],
      methodRef: ref(),
      standardRef: ref(),
      isDogTagType: false, // 是否是铭牌模板
      methodList: [], // 试验方法下拉数据
      standardList: [], // 检测依据下拉数据源
      realReviewerIdimg: '',
      templateHeaderName: '',
      texJs: '',
      jsonDataNew: props.jsonData,
      publicJs: '',
      module: '',
      colorupdate: true,
      type: '',
      deviceListNew: [],
      showHistory: false,
      historyLabel: {}, // 单选框的label，用在历史记录里
      formcolor: {},
      form: {
        address: '',
        temperature: '',
        humidness: '',
        method: '',
        selectedStandardName: '',
        fileNo: ''
      },
      options: [],
      historyData: [],
      testTime: '',
      setHistoryTimer: null,
      testSiteList: []
    });
    const NEW_TEMPLATE_ELEMENT_SELECTOR =
      '.template-content textarea, .template-content input, .template-content select';
    const TEXTAREA_TYPE_REGEXP = /^textarea$/i;
    const RADIO_CHECKBOX_TYPE_REGEXP = /^(radio|checkbox)$/i;
    const DATE_TYPE_REGEXP = /^(date|datetime-local|time)$/i;
    const RADIO_CHECKBOX_PARENT_ID_REGEXP = /^[a-z\d\b]{9}_[a-z\b]+/;
    const isTransverse = computed(() => state.jsonDataNew.showType === 1);
    const { DEV, VITE_TEMPLATE_EDITOR, VITE_TEMPLATE_EDITOR_ORIGIN } = import.meta.env;
    const templateEditorPath = (DEV ? VITE_TEMPLATE_EDITOR_ORIGIN : '') + VITE_TEMPLATE_EDITOR;
    const templateEditorPath2 = (DEV ? 'http://*************' : '') + getMinioURL();
    bus.$on('excelData', allData => {});

    watch(
      () => props.isStandardCustom,
      newIsStandardCustom => {
        state.standardCustom = newIsStandardCustom;
      }
    );

    // 试验方法
    const getMethodListAll = () => {
      if (state.jsonDataNew.capabilityId) {
        getMethodList(state.jsonDataNew.capabilityId).then(res => {
          if (res) {
            state.methodList = res.data.data;
          }
        });
      }
    };

    // 检测依据
    const getStandardListAll = () => {
      if (state.jsonDataNew.capabilityId) {
        getCapabilityStandardBasisList(state.jsonDataNew.capabilityId).then(res => {
          if (res) {
            state.standardList = res.data.data;
          }
        });
      }
    };

    watch(
      () => props.experimentId,
      newValue => {
        setTimeout(() => {
          window.MathJax.startup.defaultReady();
        }, 300);
      }
    );
    const getPublicJs = () => {
      return new Promise(resolve => {
        const scriptbox = document.getElementById('scriptbox');
        if (!scriptbox) {
          const publicEle = document.createElement('script');
          publicEle.setAttribute('id', 'scriptbox');
          publicEle.type = 'text/javascript';
          publicEle.src = `${templateEditorPath2}/public.js`;
          publicEle.onload = () => {
            resolve();
            setTemplateScript(scriptRef, getMinioURL());
          };
          document.head.appendChild(publicEle);
        }
        handleWireCore();
      });
    };
    const getTexMmlChtml = () => {
      axios.get(`${templateEditorPath}/tex-mml-svg.js`).then(res => {
        state.texJs = res.data;
        if (state.texJs) {
          setTemplateScript2(scriptRef, getMinioURL(), state.texJs);
        }
      });
    };
    // 检测地点
    const getAddress = () => {
      detectionaddress({}).then(res => {
        state.options = res.data.data;
      });
    };

    // 打开OCR识别弹出框
    const handleOCRDialog = type => {
      state.dialogOCR = true;
      state.ocrType = type;
    };
    const closeOcrDailog = val => {
      state.dialogOCR = false;
      if (val) {
        state.affixTipData = JSON.parse(localStorage.getItem('templateValue'));
      }
    };
    const getTestSiteList = () => {
      getDictionary('SYCS').then(res => {
        state.testSiteList = res.data.data?.dictionaryoption;
      });
    };
    onMounted(() => {
      mounted();
      getTestSiteList();
      nextTick(async () => {
        $('#showHistory').hover(
          function () {
            window.clearInterval(state.setHistoryTimer);
            state.showHistory = true;
          },
          function () {
            state.setHistoryTimer = setTimeout(() => {
              state.showHistory = false;
            }, 3000);
          }
        );
        await getPublicJs();
        setData();
        state.isEdit = false;
      });
    });

    onUnmounted(() => {
      unmounted();
      const sjlxRadio = document.getElementById('sjlx_radio');

      if (sjlxRadio) {
        sjlxRadio.removeEventListener('change', handleChangeSJLX);
      }
    });

    onBeforeRouteLeave((to, from, next) => {
      if (state.isEdit) {
        ElMessageBox({
          title: '提示',
          message: '当前模板数据未保存，是否确认离开？',
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          showCancelButton: true,
          closeOnClickModal: true,
          type: 'warning'
        })
          .then(() => {
            state.isEdit = false;
            next();
          })
          .catch(() => {});
      } else {
        next();
      }
    });

    const HTMLDOMtoString = HTMLDOM => {
      const div = document.createElement('div');
      div.appendChild(HTMLDOM);
      return div.innerHTML;
    };

    const setXXYSValue = (colorList, suffixList) => {
      for (var x = 0; x < colorList.length; x++) {
        if ($(`#xxys_${suffixList[x]}`)) {
          $(`#xxys_${suffixList[x]}`).val(colorList[x]);
        }
      }
    };

    // 初始化数据
    const setData = async () => {
      getXhModuleNumber(route.name, state.jsonDataNew.coreNumber, state.jsonDataNew.experimentData);
      setTemplateHeader();
      setTemplateValue();
      inputSoftKeyboard(); // 设置软键盘
      // 判断模板标准
      setExperimentData();
      const sjlxRadio = document.getElementById('sjlx_radio');
      if (sjlxRadio) {
        sjlxRadio.addEventListener('click', handleChangeSJLX);
      }
    };
    const handleChangeSJLX = () => {
      setTimeout(() => {
        getXhModuleNumber(route.name, state.jsonDataNew.coreNumber, state.jsonDataNew.experimentData);
        disabledXxysSelection();
      }, 100);
    };

    // 设置模板头信息
    function setTemplateHeader() {
      if (props.parentType === 1) {
        state.type = 'edit';
      } else if (props.parentType === 2) {
        state.type = 'check';
      } else {
        state.type = route.query.type;
      }
      state.jsonDataNew.method = formatMethod(state.jsonDataNew.method);
      state.form.method = state.jsonDataNew.method || state.form.method;
      state.form.selectedStandardName =
        state.jsonDataNew?.experimentData?.selectedStandardName || state.form.selectedStandardName;
      state.form.fileNo = state.jsonDataNew.fileNo;
    }

    // 设置模板关键参数值
    function setTemplateValue() {
      if (state.jsonDataNew.templateValue && JSON.stringify(state.jsonDataNew.templateValue) !== '{}') {
        for (const id in state.jsonDataNew.templateValue) {
          const ele = document.getElementById(id);
          if (ele) {
            ele.value = state.jsonDataNew.templateValue[id];
          }
        }
      }
      getTemplateStandard(state.jsonDataNew?.experimentStandardVoList, state.standardCustom);
    }

    // 设置试验数据
    function setExperimentData() {
      if (JSON.stringify(state.jsonDataNew.experimentData) !== '{}' && state.jsonDataNew.experimentData !== undefined) {
        // 获取设备列表
        state.deviceListNew = state.jsonDataNew.experimentData.devices;
        const templateContainer = document.getElementById('template');
        if (templateContainer) {
          // 获取所有需要填入值的 元素
          const allFormElements = templateContainer.querySelectorAll(NEW_TEMPLATE_ELEMENT_SELECTOR);
          const inputEleCollection = templateContainer.getElementsByClassName('ipt');
          // 设置模板表头数据
          if (state.jsonDataNew.experimentData.body) {
            setTempalteFormHeader();
            if (allFormElements.length > 0) {
              setInputEleValueAndStatusByNewTemplate(allFormElements);
            }
            if (inputEleCollection?.length > 0) {
              setInputEleValueAndStatus(inputEleCollection);
            }
          }
          const inputElements = templateContainer.querySelectorAll('.template-content textarea');
          if (inputElements.length > 0) {
            inputElements.forEach(item => {
              item.removeEventListener('click', inputAffixFocus);
              item.addEventListener('click', inputAffixFocus);
            });
          }
          if (inputEleCollection?.length > 0) {
            for (var i = 0; i < inputEleCollection.length; i++) {
              const ele = templateContainer.querySelector('#' + inputEleCollection[i].id);
              if (ele) {
                ele.removeEventListener('click', inputAffixFocus);
                ele.addEventListener('click', inputAffixFocus);
              }
            }
          }
        }

        disabledXxysSelection();
      }
    }
    function inputAffixFocus(e) {
      const { tagName, type, classList } = e.target;
      if (
        state.type === 'edit' &&
        state.isAffixOpen &&
        ((tagName === 'INPUT' && type === 'text' && classList.contains('ipt')) ||
          (tagName === 'TEXTAREA' && (classList.contains('input') || classList.contains('input-number'))))
      ) {
        state.affixTips = true;
        state.focusId = e.srcElement.id;
        state.clientOffset = {
          clientX: e.clientX + 10,
          clientY: e.clientY + 10
        };
      }
    }
    // 调用软键盘
    const inputSoftKeyboard = () => {
      const softDoms = document.getElementsByClassName('softKeyboardShow');
      const softInput = document.getElementsByClassName('softKeyboardInput');
      Array.from(softDoms).forEach(item => {
        var id = item.getAttribute('id');
        document.getElementById(id).addEventListener('click', function (e) {
          state.focusId = e.target.id;
          state.clientOffset = {
            clientX: e.clientX + 10,
            clientY: e.clientY + 10
          };
        });
      });
      // 值改变时触发
      Array.from(softInput).forEach(item => {
        var inputId = item.getAttribute('id');
        document.getElementById(inputId).addEventListener('focus', function (e) {
          state.softKeyboard = true;
          console.log(e);
          // console.log(window.getSelection().toString())
          // if (window.getSelection().toString()) {
          // }
          // state.selectMark = {
          //   start: this.selectionStart,
          //   end: this.selectionEnd,
          //   text: window.getSelection().toString()
          // }
        });
      });
      // 失去焦点时触发
      Array.from(softInput).forEach(item => {
        var inputId = item.getAttribute('id');
        document.getElementById(inputId).addEventListener('blur', function (e) {
          // state.softKeyboard = false
        });
      });
    };
    function setTempalteFormHeader() {
      state.form = {
        address: '',
        temperature: '',
        humidness: '',
        method: '',
        selectedStandardName: '',
        fileNo: ''
      };
      const keylist = Object.keys(state.jsonDataNew.experimentData?.header);
      keylist.forEach(item => {
        if (state.jsonDataNew.experimentData?.header[item].length >= 2 && state.type === 'check') {
          state.formcolor[item] = true;
        } else {
          state.formcolor[item] = false;
        }
        state.form[item] = state.jsonDataNew.experimentData?.header[item][0].value;
      });
    }

    function setInputEleValueAndStatusByNewTemplate(allFormElements) {
      allFormElements.forEach(inputElement => {
        if (state.type !== 'edit') {
          inputElement.style.backgroundColor = '#fff';
          inputElement.setAttribute('disabled', '');
          inputElement.setAttribute('readonly', '');
        }
        const isRadioOrCheckbox = inputElement.type && RADIO_CHECKBOX_TYPE_REGEXP.test(inputElement.type);
        const key = isRadioOrCheckbox ? inputElement.id.match(RADIO_CHECKBOX_PARENT_ID_REGEXP)?.[0] : inputElement.id;
        const valueList = state.jsonDataNew.experimentData.body[key];
        let realValue = null;
        if (valueList !== '' && valueList !== undefined) {
          if (valueList instanceof Array) {
            if (valueList.length >= 2 && state.type === 'check') {
              inputElement.style.color = 'red';
              inputElement.removeEventListener('mouseenter', onShowHistory);
              inputElement.addEventListener('mouseenter', onShowHistory);
            }
            realValue = valueList[0].value;
          }
          if (isRadioOrCheckbox) {
            if (inputElement.value == realValue) {
              inputElement.checked = true;
            }
          } else {
            if (inputElement !== null) {
              inputElement.value = realValue;
              if (TEXTAREA_TYPE_REGEXP.test(inputElement.type)) {
                inputElement.dispatchEvent(new Event('input'));
              }
              if (DATE_TYPE_REGEXP.test(inputElement.type)) {
                inputElement.dispatchEvent(new Event('change'));
              }
            }
          }
        }
      });
    }

    function setInputEleValueAndStatus(inputEleCollection) {
      // 设置所有需要填值元素的值和状态
      Array.from(inputEleCollection).forEach(item => {
        var id = item.getAttribute('id');
        var isRadio = id.indexOf('_radio') !== -1;
        const ele = document.getElementById(id);
        if (state.type === 'edit') {
          ele?.removeAttribute('readonly');
          if (isRadio) {
            const index = id.indexOf('_radio');
            const fileName = id.substring(0, index);
            $('input[name="' + fileName + '"]').attr('disabled', false);
          }
        } else {
          ele.style.backgroundColor = '#fff';
          if (isRadio) {
            const index = id.indexOf('_radio');
            const fileName = id.substring(0, index);
            $('input[name="' + fileName + '"]').attr('disabled', true);
          }
          ele.setAttribute('readonly', 'readonly');
          ele.disabled = '';
        }
        // ele.removeEventListener('click', onShowHistory.bind(null, id));
        // ele.addEventListener('click', onShowHistory.bind(null, id));
        ele.removeEventListener('click', onShowHistory);
        ele.addEventListener('click', onShowHistory);
        var thisValue = state.jsonDataNew.experimentData.body[id];
        var newValue = '';
        if (thisValue instanceof Array) {
          if (thisValue.length >= 2 && state.type === 'check') {
            item.style.color = 'red';
          }
          newValue = thisValue[0].value;
        }
        if (isRadio) {
          const index = id.indexOf('_radio');
          const fileName = id.substring(0, index);
          $('input[name="' + fileName + '"][value="' + newValue + '"]').prop('checked', true);
        }
        if (item.type === 'checkbox') {
          const checkBoxValue = newValue.split('@;');
          checkBoxValue.forEach(val => {
            $('input[id="' + item.id + '"][value="' + val + '"]').attr('checked', true);
          });
        }
        if (item.tagName === 'SELECT') {
          if (state.type === 'edit') {
            // 编辑页面
            if ($(item).attr('data-code') === 'XXYS') {
              if (newValue) {
                $(item).val(newValue);
              }
            } else {
              $(item).val(newValue);
            }
          } else {
            // 查看页面
            item.options.length = 0;
            const opitem = new Option();
            opitem.value = newValue;
            if (newValue.split(' ').length > 1) {
              // 如果含有空格 拿空格去切割，取最后一个
              opitem.text = newValue.split(' ')[newValue.split(' ').length - 1];
            } else if (newValue.split('-').length > 1) {
              opitem.text = newValue.split('-')[newValue.split('-').length - 1];
            } else {
              opitem.text = newValue;
            }
            item.options.add(opitem, 0);
          }
        }
        if ($(item).attr('data-code') !== 'XXYS' && item.type !== 'checkbox') {
          item.value = newValue !== null ? newValue : ' ';
        }
      });
    }

    const onShowHistory = e => {
      var id = e.target.getAttribute('id');
      if (
        state.jsonDataNew.experimentData.body[id] &&
        state.jsonDataNew.experimentData.body[id].length >= 2 &&
        state.type === 'check'
      ) {
        window.clearInterval(state.setHistoryTimer);
        state.historyData = state.jsonDataNew.experimentData.body[id];
        state.showHistory = true;
        state.historyLabel = {};
        if (id.indexOf('_radio') > -1) {
          Array.from($('#' + id).children()).forEach(function (radioItem) {
            state.historyLabel[radioItem.getAttribute('value')] = radioItem.nextSibling.nodeValue;
          });
        }
        state.setHistoryTimer = setTimeout(() => {
          window.clearInterval(state.setHistoryTimer);
          state.showHistory = false;
        }, 3000);
      }
    };

    // 头部历史数据赋值
    const handleHistory = item => {
      if (state.type === 'check') {
        state.historyData = [];
        const listdata = state.jsonDataNew.experimentData.header[item];
        listdata?.forEach(obj => {
          const valueaa = {};
          valueaa.createTime = obj.createTime;
          valueaa.createBy = obj.createBy;
          valueaa.value = obj.value;
          state.historyData.push(valueaa);
        });
        state.showHistory = true;
        if (item === 'address') {
          state.historyLabel = {};
          // 如果点击的是试验场地
          state.testSiteList.forEach(val => {
            state.historyLabel[val.code] = val.name;
          });
        }
      }
    };
    const handleSelectAffix = val => {
      if (document.getElementById(state.focusId)) {
        if (document.getElementById(state.focusId).classList.value.indexOf('number') > -1) {
          if (Number(val) || Number(val) === 0) {
            document.getElementById(state.focusId).value = val;
            var event = new InputEvent('input', {
              bubbles: true, // 事件是否冒泡
              cancelable: true // 事件是否可以被取消
            });

            // 触发事件
            document.getElementById(state.focusId).dispatchEvent(event);
          } else {
            ElMessage.warning('只能填入数字');
          }
        } else {
          document.getElementById(state.focusId).value = val;
        }
      }
      state.affixTips = false;
    };

    // 取模板数据
    const handleData = () => {
      const excelData = {};
      const allData = {};
      const templateContainer = document.getElementById('template');
      // new template
      const allFormElements = templateContainer.querySelectorAll(NEW_TEMPLATE_ELEMENT_SELECTOR);
      // console.log('allFormElements', allFormElements);
      allFormElements.forEach(item => {
        if (item.type && item.type.match(RADIO_CHECKBOX_TYPE_REGEXP)) {
          if (item.checked) {
            const parentId = item.id.match(RADIO_CHECKBOX_PARENT_ID_REGEXP)[0];
            excelData[parentId] = item.value;
          }
        } else {
          excelData[item.id] = item.value;
        }
      });

      // old template
      const inputEleCollection = templateContainer.getElementsByClassName('ipt');
      for (var i = 0; i < inputEleCollection.length; i++) {
        if (!inputEleCollection.item(i).id.includes('moduleIndex')) {
          const id = inputEleCollection.item(i).id;
          if (id.indexOf('_radio') !== -1) {
            const index = id.indexOf('_radio');
            const radioName = id.substring(0, index);
            excelData[id] = $('input[name="' + radioName + '"]:checked').val();
          } else if (inputEleCollection.item(i).type === 'checkbox') {
            var checkedValues = [];
            $('input[name="' + id + '"]:checked').each(function () {
              checkedValues.push($(this).val());
            });
            excelData[id] = checkedValues.join('@;');
          } else {
            excelData[id] = $('#' + id).val();
          }
        }
      }
      // console.log('excelData', excelData);
      state.isEdit = false;
      // return false
      allData.header = { ...state.form };
      console.log(excelData);
      allData.body = { ...excelData };
      ctx.emit('handleData', allData);
      bus.$emit('excelData', allData);
    };
    // 线芯颜色字典
    const getcolorlist = () => {};

    // 线芯自动赋值并禁用
    const handleWireCore = () => {
      if (state.type === 'edit') {
        $('input, textarea, select').change(function () {
          state.isEdit = true;
        });
      }
      const ele = document.getElementById(`sjlx_radio`) || document.getElementById(`sjxz_radio`);
      if (ele) {
        disabledXxysSelection();
        ele.addEventListener('blur', function (e) {
          if (e.target.tagName === 'INPUT') {
            setData();
          }
        });
      }
    };
    getAddress();
    getcolorlist();
    // 更改试验方法
    const handleInputMethod = val => {
      if (val.substr(-1) === '#') {
        if (state.methodList.length > 0) {
          state.visibleMethod = true;
        } else {
          ElMessage.warning('暂无推荐数据，请先新增');
        }
      }
    };
    // 试验方法失去焦点出发
    const handleBlurMethod = () => {
      state.visibleMethod = false;
    };
    // 添加试验方法
    const handleAddMethod = val => {
      state.form.method = state.form.method.slice(0, -1);
      state.form.method += val;
      state.visibleMethod = false;
      state.methodRef.focus();
    };
    // 添加判定依据
    const handleAddStandard = val => {
      state.form.selectedStandardName = state.form.selectedStandardName.slice(0, -1);
      state.form.selectedStandardName += val;
      state.visibleStandard = false;
      state.standardRef.focus();
    };
    // 更改试验方法
    const handleInputStandard = val => {
      if (val.substr(-1) === '#') {
        if (state.standardList.length > 0) {
          state.visibleStandard = true;
        } else {
          ElMessage.warning('暂无推荐数据，请先新增');
        }
      }
    };
    // 试验方法失去焦点出发
    const handleBlurStandard = () => {
      state.visibleStandard = false;
    };
    // 左侧工具操作
    const handleType = info => {
      if (info?.type === 'affix') {
        // 打开关闭粘贴模式
        state.isAffixOpen = info.value;
        state.affixTips = false;
        state.affixTipData = JSON.parse(localStorage.getItem('templateValue'));
      } else if (info?.type === 'ocr') {
        handleOCRDialog('online');
      } else if (info?.type === 'snapshotHistory') {
        // 截图记录
        state.drawerDrawerHistory = true;
      } else if (info?.type === 'snapshot') {
        toImgSnapshot();
      }
    };
    // 屏幕截屏快照
    const toImgSnapshot = async () => {
      state.imgBaseUrl = await pdf.PageSnapshot(state.templateRef);
      nextTick(() => {
        state.dialogSnapshot = true;
      });
    };
    // 是否有未查看的新截图
    const getHasNotSeen = () => {
      if (state.type === 'edit') {
        screenshotRecord({ relevancyKey: state.experimentId }).then(res => {
          if (res) {
            state.isHasNoRead = res.data.data;
          }
        });
      }
    };
    getHasNotSeen();
    const handleCloseHistory = () => {
      state.drawerDrawerHistory = false;
      state.isHasNoRead = false;
    };
    const handleCloseSnapshot = isUpdate => {
      state.dialogSnapshot = false;
      if (isUpdate) {
        getHasNotSeen();
      }
    };
    watch(
      () => props.jsonData,
      newValueJson => {
        if (newValueJson) {
          state.texJs = '';
          const itemData = JSON.parse(JSON.stringify(newValueJson));
          state.jsonDataNew = itemData;
          state.jsonDataNew.excelHtml = itemData.excelHtml;
          state.jsonDataNew.experimentData = itemData.experimentData;
          if (state.jsonDataNew?.completestartdate === state.jsonDataNew?.completedatetime) {
            state.testTime = formatDate(state.jsonDataNew?.completestartdate, '', true);
          } else {
            state.testTime =
              formatDate(state.jsonDataNew?.completestartdate, '', true) +
              '~' +
              formatDate(state.jsonDataNew?.completedatetime, '', true);
          }
          if (state.jsonDataNew.isSpecial) {
            state.isDogTagType = true;
          } else {
            state.isDogTagType = false;
          }
          getMethodListAll();
          getStandardListAll();
          nextTick(() => {
            setTemplateScript(scriptRef, getMinioURL());
            setTimeout(async () => {
              if (route.name === 'AddRecord' || route.name === 'ExperimentExcel') {
                window.MathJax.startup.defaultReady();
              }
              setData();
            }, 300);
            state.isEdit = false;
          });
        }
      },
      { immediate: true }
    );
    return {
      ...toRefs(state),
      isTransverse,
      handleData,
      handleType,
      handleCloseSnapshot,
      getHasNotSeen,
      handleCloseHistory,
      handleOCRDialog,
      closeOcrDailog,
      handleSelectAffix,
      handleInputMethod,
      handleBlurMethod,
      handleAddMethod,
      handleAddStandard,
      handleInputStandard,
      handleBlurStandard,
      formatDate,
      setXXYSValue,
      HTMLDOMtoString,
      getPublicJs,
      getTexMmlChtml,
      getcolorlist,
      getNamesByid,
      getNamesByIds,
      handleHistory,
      getNameByid,
      setData,
      scriptRef
    };
  }
};
</script>

<style scoped lang="scss">
@import '@/styles/mixin.scss';
.slot-temp {
  font-family: Times New Roman, 'SourceHanSerifCN';
  box-sizing: border-box;
  box-shadow: 0px 4px 12px 4px rgba(0, 0, 0, 0.08), 0px 2px 4px -2px rgba(0, 0, 0, 0.16);
  filter: drop-shadow(0px 3px 8px rgba(0, 0, 0, 0.12));
  margin: 0 auto;
  padding: 50px 55px 50px 55px;
  font-size: 14px;
  font-weight: 500;
  background: #fff;

  &.vertical {
    width: 794px;
  }

  &.horizontal {
    width: 1123px;
  }
}

// .isTransverse {
//   width: 1123px;
//   .bd {
//     width: 1009px;
//   }
// }
.uni-template-temp {
  padding-top: 25px;
}
.softKeyboardShow {
  -webkit-user-select: none; /* Safari */
  -moz-user-select: none; /* Firefox */
  -ms-user-select: none; /* IE10+/Edge */
  user-select: none; /* 标准语法 */
}
.affixTips {
  background: #fff;
  width: 120px;
  max-height: 200px;
  position: fixed;
  font-size: 14px;
  overflow-y: auto;
  padding: 10px 10px;
  text-align: left;
  box-shadow: 0px 4px 12px 4px rgba(0, 0, 0, 0.08), 0px 2px 4px -2px rgba(0, 0, 0, 0.16);
  ul {
    margin: 0;
    padding: 0;
  }
  li {
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    word-wrap: break-word;
    &:hover {
      background: $tes-primary2;
      color: $tes-primary;
    }
  }
}
.moban {
  position: relative;
}
.template-input {
  :deep(.el-input__inner) {
    padding: 0 10px;
    color: #000;
  }
}
.methodPopover {
  li {
    margin: 0 0 10px 0;
    padding: 5px;
    cursor: pointer;
    &:hover {
      background-color: $tes-primary2;
    }
  }
}
:deep(.el-input.is-disabled .el-input__inner) {
  color: #000;
}

:deep(.is-disabled .el-input__suffix) {
  display: none;
}
.qm {
  display: flex;
  align-items: flex-start;
}
.qImage {
  max-height: 80px;
  max-width: 70px;
  display: block;
}

.pop-zr {
  width: 320px;
  position: fixed;
  top: 200px;
  right: 50px;
  transition: all 0.2s ease 0s;

  .pop-content {
    max-height: 300px;
    overflow-y: auto;
  }

  h4 {
    text-align: left;
    margin-bottom: 5px;
  }

  .i-close {
    position: absolute;
    top: 10px;
    right: 5px;
    cursor: pointer;
  }
}

.print-box {
  width: 794px;
}

@media print {
  /*隐藏不打印的元素*/
  .print-box {
    display: block;
  }
  /*其他打印样式*/
}

.red {
  color: red;

  input {
    color: red !important;
  }

  :deep(.el-input__inner) {
    color: red !important;
  }

  .el-input__inner {
    color: red !important;
  }
}
.inline-block {
  display: inline-block;
  vertical-align: top;
}
</style>
