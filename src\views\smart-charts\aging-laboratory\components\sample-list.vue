<template>
  <!-- 老化箱状态 -->
  <div class="sample-list flex flex-col flex-1">
    <div class="item-title">放样记录</div>
    <div class="auto-scroll-table-container flex-1 overflow-y-auto flex flex-col">
      <el-row class="table-th">
        <el-col :span="3"> 设备编号 </el-col>
        <el-col :span="4"> 设备名称 </el-col>
        <el-col :span="2"> 状态 </el-col>
        <el-col :span="3"> 样品信息 </el-col>
        <el-col :span="2"> 实时温度 </el-col>
        <el-col :span="4"> 开始时间 </el-col>
        <el-col :span="4"> 结束时间 </el-col>
        <el-col :span="2"> 放样人 </el-col>
      </el-row>
      <div :id="`sampleContent${type}`" class="flex-1 overflow-y-auto table-content flex flex-col gap-2">
        <el-row v-for="item in tableData" :key="item.id">
          <el-col :span="3"> {{ item.deviceNumber }} </el-col>
          <el-col :span="4"> {{ item.boxName }} </el-col>
          <el-col :span="2"> {{ dictionaryAll['24'].all[item.boxStatus] || item.boxStatus }} </el-col>
          <el-col :span="3"> {{ item.sampleName }} </el-col>
          <el-col :span="2"> {{ item.sampleTemperature }} </el-col>
          <el-col :span="4"> {{ item.startDateTime }} </el-col>
          <el-col :span="4"> {{ item.endDateTime }} </el-col>
          <el-col :span="2"> {{ getNameByid(item.createBy) || item.createBy }} </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onBeforeUnmount, reactive, toRefs, nextTick } from 'vue';
import { sampleSplitList } from '@/api/aging-laboratory';
import { getNameByid } from '@/utils/common';

export default {
  props: {
    type: {
      type: Number,
      default: 0
    },
    dictionary: {
      type: Object,
      default: () => {}
    }
  },
  setup(props) {
    const tableRef = ref(null);
    const state = reactive({
      dictionary: {
        24: {
          all: {},
          enable: {}
        }
      },
      dictionaryAll: props.dictionary || {
        24: {
          all: {},
          enable: {}
        }
      },
      detailLoading: false,
      tableData: []
    });
    const autoScrollInterval = ref(null);

    // 获取数据
    const getList = async () => {
      state.detailLoading = true;
      const { data } = await sampleSplitList().finally((state.detailLoading = false));
      state.tableData = data.data[props.type];
      startAutoScroll();
    };

    // 滚动事件处理
    const handleScroll = ({ scrollTop, scrollLeft, scrollHeight, clientHeight }) => {
      // 检查是否滚动到底部
      const isBottom = scrollTop + clientHeight >= scrollHeight - 10;

      if (isBottom && !state.detailLoading) {
        getList();
      }
    };

    // 自动滚动功能
    const startAutoScroll = () => {
      nextTick(() => {
        document.getElementById(`sampleContent${props.type}`).scrollTop = 0;
      });
    };

    onMounted(() => {
      getList();
    });

    onBeforeUnmount(() => {
      if (autoScrollInterval.value) {
        clearInterval(autoScrollInterval.value);
        autoScrollInterval.value = null;
      }
    });

    return {
      ...toRefs(state),
      tableRef,
      getNameByid,
      handleScroll
    };
  }
};
</script>

<style lang="scss" scoped>
@import '@/styles/intelligentChart.scss';
.table-content {
  color: #fff;
  text-align: center;
  .el-col {
    line-height: 28px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding: 2px 5px;
    font-size: 14px;
  }
  .el-col:nth-child(2n) {
    background-color: #4163bc;
  }
  .el-col:nth-child(2n + 1) {
    background-color: #4a6fd0;
  }
  // box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
}
.table-th {
  color: #fff;
  line-height: 30px;
  font-size: 15px;
}
.item-title {
  background: #4280d7;
  border-radius: 8px 8px 0px 0px;
  font-size: 18px;
  color: $scrollListColor;
  height: 44px;
  line-height: 30px;
  color: $titleColor;
  font-weight: 700;
  padding: 7px 0 7px 29px;
  text-align: left;
}
.auto-scroll-table-container {
  width: 100%;
  height: 100%;
  border: 2px solid #4280d7;
  padding: 3px 10px 10px 10px;
  border-radius: 0 0 4px 4px;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
}
</style>
