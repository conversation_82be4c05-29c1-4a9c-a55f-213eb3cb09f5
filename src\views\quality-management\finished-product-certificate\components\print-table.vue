<template>
  <div class="page-model">
    <div class="flex flex-row justify-between pb-3">
      <div class="btn-group">
        <el-button
          :disabled="!tableSelected.length"
          :loading="listLoading"
          type="primary"
          size="small"
          @click="handleDetermin(tableSelected)"
          >批量判定
        </el-button>
        <el-button
          :disabled="!tableSelected.length"
          :loading="listLoading"
          type="primary"
          size="small"
          @click="handleWarehouse(tableSelected)"
          >批量入库
        </el-button>
        <el-button
          :disabled="!tableSelected.length"
          :loading="listLoading"
          type="primary"
          size="small"
          @click="handlePrint(tableSelected)"
          >批量打印合格证
        </el-button>
      </div>
    </div>
    <el-table
      ref="tableRef"
      :key="tableKey"
      v-loading="listLoading"
      :data="tableList"
      size="medium"
      fit
      border
      height="auto"
      class="dark-table base-table"
      :row-style="
        () => {
          return 'cursor: pointer';
        }
      "
      @header-dragend="drageHeader"
      @row-click="handleRowClick"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" :width="colWidth.checkbox" align="center" fixed="left" />
      <template v-for="(item, index) in tableColumns" :key="index">
        <el-table-column
          :prop="item.fieldKey"
          :label="item.fieldName"
          :sortable="Number(item.isSortable) === 1"
          :width="item.isMinWidth ? '' : item.columnWidth"
          :min-width="item.isMinWidth ? item.columnWidth : ''"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <template v-if="item.fieldType === fieldTypesEnum.Person">
              <UserTag :name="getNameByid(row[item.fieldKey]) || row[item.fieldKey] || '--'" />
            </template>
            <template v-else-if="item.fieldType === fieldTypesEnum.Status">
              <el-tag
                v-if="row[item.fieldKey] !== ''"
                size="small"
                effect="dark"
                :type="status[item.fieldKey][row[item.fieldKey]]?.type || 'primary'"
                >{{ status[item.fieldKey][row[item.fieldKey]]?.label || '--' }}
              </el-tag>
              <span v-else>--</span>
            </template>
            <template v-else-if="item.fieldType === fieldTypesEnum.Date">
              <span>{{ row[item.fieldKey] == '' ? '--' : formatDate(row[item.fieldKey]) }}</span>
            </template>
            <template v-else-if="item.fieldType === fieldTypesEnum.Link">
              <template v-if="item.fieldKey == 'no'">
                <span v-if="row[item.fieldKey]" v-copy="row.no" class="nowrap blue-color" @click.stop="iaDetail(row)">{{
                  row.no
                }}</span>
                <span v-else>--</span>
              </template>
              <template v-else-if="item.fieldKey == 'secSampleNum'">
                <span
                  v-if="row[item.fieldKey]"
                  v-copy="row.secSampleNum"
                  class="nowrap blue-color"
                  @click.stop="handleSampleDetail(row)"
                  >{{ row.secSampleNum || '--' }}</span
                >
                <span v-else>--</span>
              </template>
              <template v-else-if="item.fieldKey == 'inspectionCode'">
                <span
                  v-if="row[item.fieldKey]"
                  v-copy="row.inspectionCode"
                  class="nowrap blue-color"
                  @click.stop="handleReportDetail(row)"
                  >{{ row.inspectionCode || '--' }}</span
                >
                <span v-else>--</span>
              </template>
            </template>
            <template v-else-if="item.fieldType === fieldTypesEnum.Custom">
              <span v-if="item.fieldKey == 'deliveryInstitution'">
                <!-- 交货单位 -->
                {{ dictionary['JHDW'].all[row[item.fieldKey]] || row[item.fieldKey] || '--' }}
              </span>
              <span v-else-if="item.fieldKey == 'inputWarehouseUnit'">
                <!-- 入库单位（客户数量单位） -->
                {{ dictionary['5'].all[row[item.fieldKey]] || row[item.fieldKey] || '--' }}
              </span>
              <span v-else-if="item.fieldKey == 'isSelectCapability'">
                <!-- 检测项目 -->
                {{ row[item.fieldKey] == 1 ? '是' : '否' }}
              </span>
              <span v-else-if="item.fieldKey == 'smallSerialNumber'">
                {{
                  row.smallSerialNumber?.length
                    ? `${row.smallSerialNumber[row.smallSerialNumber.length - 1][0]}~${
                        row.smallSerialNumber[row.smallSerialNumber.length - 1][1]
                      }`
                    : 'N/A'
                }}
              </span>
              <div v-else-if="item.fieldKey === 'nuclearMarker'">
                <span v-if="row.nuclearMarker">
                  {{ row.nuclearMarker.indexOf('H') > -1 ? '⚠H' : '' }}
                  {{ row.nuclearMarker.indexOf('空') > -1 ? '©' : '' }}
                </span>
                <span v-else>--</span>
              </div>
              <div v-else-if="item.fieldKey == 'decideType'">
                {{ dictionary['HGZSCPD'].all[row[item.fieldKey]] || row[item.fieldKey] || '--' }}
              </div>
              <span v-else>{{ row[item.fieldKey] || '--' }}</span>
            </template>
            <span v-else>{{ row[item.fieldKey] || '--' }}</span>
          </template>
        </el-table-column>
      </template>
      <el-table-column label="操作" fixed="right" :min-width="colWidth.operationSingle">
        <template #default="{ row }">
          <span class="blue-color" @click.stop="handleEdit([row])">编辑</span>
        </template>
      </el-table-column>
      <el-table-column label="打印" fixed="right" :min-width="colWidth.operationSingle">
        <template #default="{ row }">
          <span class="blue-color" @click.stop="handlePrint([row])">合格证</span>
        </template>
      </el-table-column>
    </el-table>
    <!-- 编辑弹出框 -->
    <DialogEditCertificate
      :dialog-visible="dialogEdit"
      :page-view="tableColumns"
      :dictionary="dictionary"
      :select-row="rowSelectData"
      @closeDialog="handleCloseDialog"
    />
    <!-- 入库、批量入库弹出框 -->
    <DialogWarehouseEntry
      :dialog-visible="dialogWarehouse"
      :select-row="rowSelectData"
      :dictionary="dictionary"
      @closeDialog="handleCloseWare"
    />
    <!-- 批量判定弹出框 -->
    <DialogBatchDetermin
      :dialog-visible="dialogVisible"
      :select-row="rowSelectData"
      :dictionary="dictionary"
      @closeDialog="determinCloseDialog"
    />
    <!-- 批量完成 -->
    <DialogBatchCompletion :un-list="unFinishedSampleList" :dialog-visible="showUnFinishedDialog" />
    <!-- 打印、批量打印 -->
    <DialogPrintCertificate
      :dialog-visible="dialogPrint"
      :certificate-templates="certifiTemplates"
      :select-row="rowSelectData"
      :dictionary="dictionary"
      @closeDialog="closePrint"
    />
  </div>
</template>

<script>
import { reactive, ref, toRefs, watch, onMounted } from 'vue';
import router from '@/router/index.js';
import { formatDate } from '@/utils/formatTime';
import { ElMessage } from 'element-plus';
import UserTag from '@/components/UserTag';
import { getNamesByid, getNameByid, getPermissionBtn } from '@/utils/common';
import { getFinishedProductList } from '@/api/finished-product-certificate';
import { drageHeader } from '@/utils/formatTable';
import { colWidth } from '@/data/tableStyle';
import { fieldTypesEnum, columnFixedTypesEnum } from '@/components/TableColumnView/enum';
import { getDictionary } from '@/api/user';
import { getViewByBindingMenu } from '@/api/tableView';
import DialogEditCertificate from './dialog/DialogEditCertificate.vue';
import DialogWarehouseEntry from './dialog/DialogWarehouseEntry.vue';
import DialogPrintCertificate from './dialog/DialogPrintCertificate.vue';
import DialogBatchDetermin from '../../raw-certificate/components/DialogBatchDetermin.vue';
import DialogBatchCompletion from '../../raw-certificate/components/DialogBatchCompletion.vue';
import { saveBatchFinish } from '@/api/order';

export default {
  name: 'FinishedProductCertificate',
  components: {
    UserTag,
    DialogEditCertificate,
    DialogWarehouseEntry,
    DialogBatchDetermin,
    DialogBatchCompletion,
    DialogPrintCertificate
  },
  props: {
    select: {
      type: Object,
      default: function () {
        return {};
      }
    },
    certificateTemplates: {
      type: Array,
      default: () => []
    }
  },
  setup(props) {
    const state = reactive({
      tableRef: ref(),
      fieldTips: '',
      selectRow: {},
      unFinishedSampleList: [],
      certifiTemplates: [], // 适配当前的模板
      dialogPrint: false,
      showUnFinishedDialog: false,
      dialogVisible: false,
      dialogEdit: false,
      dialogWarehouse: false,
      dialogWarrant: false,
      rowEdit: {},
      ruleForm: ref(),
      listLoading: false,
      status: {
        warehousePrintStatus: {
          0: { type: 'warning', label: '未打印' },
          1: { type: 'success', label: '已打印' }
        },
        printStatus: {
          0: { type: 'warning', label: '未打印' },
          1: { type: 'success', label: '已打印' }
        },
        thirdDataReturnStatus: {
          0: { type: 'warning', label: '待入库' },
          1: { type: 'success', label: '已入库' }
        },
        decideMethod: {
          0: { type: 'info', label: '空' },
          1: { type: 'primary', label: '手动' },
          2: { type: 'success', label: '自动' }
        },
        reportType: {
          0: { type: 'success', label: '合格' },
          1: { type: 'error', label: '不合格' },
          2: { type: 'info', label: '不判定' }
        }
      },
      listQuery: {
        limit: 20,
        page: 1
      },
      formInline: {
        param: '',
        tableQueryParamList: []
      },
      dictionary: {
        BYCK: {
          enable: {},
          all: {}
        },
        HGZSCPD: {
          enable: {},
          all: {}
        },
        5: {
          enable: {},
          all: {}
        },
        JHDW: {
          enable: {},
          all: {}
        }
      },
      tableSelected: [],
      rowSelectData: [],
      tableColumns: [],
      tableList: [],
      allTableList: [],
      dialogFormVisible: false,
      total: 0
    });
    const getDetailView = async () => {
      const res = await getViewByBindingMenu('FinishedProductCertificate');
      if (res) {
        state.tableColumns = res.data.data[0].sysEmployeeListConfigList;
      }
    };
    const getList = filterVal => {
      const params = {
        salesOrderCode: state.selectRow.salesOrderCode,
        salesOrderItemNo: state.selectRow.salesOrderItemNo
      };
      state.listLoading = true;
      getFinishedProductList(params).then(res => {
        state.listLoading = false;
        if (res) {
          // state.allTableList = JSON.parse(JSON.stringify(res.data.data));
          state.tableList = JSON.parse(JSON.stringify(res.data.data));
          // filterTable(filterVal || state.radioValue);
        }
      });
    };
    // const filterTable = filterVal => {
    //   if (filterVal == 1) {
    //     state.tableList = state.allTableList.filter(item => item.printStatus == 0 || item.warehousePrintStatus == 0);
    //   } else if (filterVal == 2) {
    //     state.tableList = state.allTableList.filter(item => item.printStatus == 1 || item.warehousePrintStatus == 1);
    //   } else if (filterVal == 3) {
    //     state.tableList = state.allTableList.filter(item => item.thirdDataReturnStatus == 0);
    //   } else if (filterVal == 4) {
    //     state.tableList = state.allTableList.filter(item => item.thirdDataReturnStatus == 1);
    //   } else {
    //     state.tableList = state.allTableList;
    //   }
    // };
    watch(
      () => props.select,
      newValue => {
        state.selectRow = newValue;
        state.certifiTemplates = [];
        if (state.selectRow?.salesOrderItemNo) {
          getList();
        }
      },
      { deep: true }
    );

    const tableKey = ref(0);
    const tableKeyLeft = ref(0);
    const getDictionaryList = () => {
      Object.keys(state.dictionary).forEach(async item => {
        const response = await getDictionary(item);
        if (response) {
          state.dictionary[item] = { enable: {}, all: {} };
          response.data.data.dictionaryoption.forEach(optionItem => {
            if (optionItem.status == 1) {
              state.dictionary[item].enable[optionItem.code] = optionItem.name;
            }
            state.dictionary[item].all[optionItem.code] = optionItem.name;
          });
        }
      });
    };
    getDictionaryList();

    const reset = () => {
      state.formInline = {
        param: '',
        tableQueryParamList: []
      };
      getList();
    };
    const handleSelectionChange = val => {
      state.tableSelected = val;
    };
    const handleAudit = () => {};
    /** 批量判定 */
    const handleDetermin = rowArray => {
      state.dialogVisible = true;
      state.rowSelectData = JSON.parse(JSON.stringify(rowArray));
    };
    const handleDetail = row => {
      router.push({
        path: '/recordReview/sample/detail',
        query: {
          orderId: row.orderId,
          sampleId: row.samplesId
        }
      });
    };
    const handleRowClick = row => {
      state.tableRef.toggleRowSelection(
        row,
        !state.tableSelected.some(item => {
          return row.id === item.id;
        })
      );
    };
    const getSingleText = val => {
      state.formInline.param = val;
      state.formInline.tableQueryParamList = [];
      getList();
    };

    const getParamList = paramList => {
      state.formInline.tableQueryParamList = paramList;
      state.formInline.param = '';
      getList();
    };
    // 打印
    const handlePrint = rowArray => {
      state.dialogPrint = true;
      state.rowSelectData = JSON.parse(JSON.stringify(rowArray));
      state.certifiTemplates = props.certificateTemplates.filter(item => {
        return item.categoryCode == state.rowSelectData[0].mateType;
      });
    };
    // 编辑
    const handleEdit = rowArray => {
      state.dialogEdit = true;
      state.rowSelectData = JSON.parse(JSON.stringify(rowArray));
    };
    // 入库
    const handleWarehouse = rowArray => {
      state.dialogWarehouse = true;
      state.rowSelectData = rowArray;
    };

    const onUpdateColumns = columns => {
      tableKey.value = tableKey.value + 1;
      state.tableColumns = columns;
      state.fieldTips = columns
        .filter(item => item.isQuery == 1)
        .map(item => item.fieldName)
        .join('/');
    };
    const handleCloseDialog = isrefresh => {
      state.dialogEdit = false;
      if (isrefresh) {
        getList();
      }
    };
    const handleCloseWare = isrefresh => {
      state.dialogWarehouse = false;
      if (isrefresh) {
        getList();
      }
    };
    const determinCloseDialog = value => {
      state.dialogVisible = false;
      if (value) {
        getList();
        if (value.progress == 1) {
          submitBatchFinish();
        }
      }
    };
    const submitBatchFinish = (event = false) => {
      const params = {
        sampleBatchFinishVoList: []
      };
      state.tableSelected.forEach(item => {
        params.sampleBatchFinishVoList.push({
          sampleId: item.sampleId,
          secSampleNum: item.secSampleNum,
          status: item.status
        });
      });
      saveBatchFinish(params).then(res => {
        if (res) {
          if (res.data.data && res.data.data.length > 0) {
            if (event) {
              ElMessage.success('部分样品已完成检测,无法完成的样品见提示框!');
            } else {
              ElMessage.success('所选样品已全部判定合格,部分样品已完成检测,无法完成的样品见提示框!');
            }
            state.unFinishedSampleList = res.data.data;
            state.showUnFinishedDialog = true;
          } else {
            if (event) {
              ElMessage.success('所选样品已全部完成检测!');
            } else {
              ElMessage.success('所选样品已全部判定合格并完成检测!');
            }
          }
        }
      });
    };
    const closeWarehouseWarrant = isRefresh => {
      state.dialogWarrant = false;
      if (isRefresh) {
        getList();
      }
    };
    const closePrint = isRefresh => {
      state.dialogPrint = false;
      if (isRefresh) {
        getList();
      }
    };
    // 查看
    const iaDetail = row => {
      if (row.no) {
        router.push({ name: 'FinishedProductApplication', query: { id: row.orderId, flag: 1 } });
      }
    };
    // 样品编号
    const handleSampleDetail = row => {
      router.push({ name: 'FinishedSampleDetail', query: { orderId: row.orderId, sampleId: row.sampleId } });
    };
    // 报告编号
    const handleReportDetail = row => {
      router.push({
        name: 'FinishedProductTestReport',
        query: { reportId: row.reportId, sampleId: row.sampleId, reportStage: 6 }
      });
    };
    onMounted(() => {
      getDetailView();
    });
    return {
      ...toRefs(state),
      handlePrint,
      handleSampleDetail,
      handleReportDetail,
      iaDetail,
      closeWarehouseWarrant,
      closePrint,
      handleCloseDialog,
      determinCloseDialog,
      handleCloseWare,
      handleEdit,
      handleWarehouse,
      getSingleText,
      getParamList,
      handleRowClick,
      getPermissionBtn,
      handleDetail,
      drageHeader,
      getNameByid,
      getNamesByid,
      handleAudit,
      handleDetermin,
      reset,
      handleSelectionChange,
      formatDate,
      getList,
      tableKey,
      tableKeyLeft,
      colWidth,
      onUpdateColumns,
      fieldTypesEnum,
      columnFixedTypesEnum
    };
  }
};
</script>
<style lang="scss" scoped>
.page-searchbar {
  background: #fff;
  display: flex;
  justify-content: space-between;
  padding: 0 20px;
}

.page-model {
  background: #fff;
  padding: 20px 20px 0 20px;
  overflow: auto;
  flex: 1;
  display: flex;
  flex-direction: column;
}

:deep(.format-height-table2) {
  .el-table__body-wrapper {
    max-height: calc(100vh - 47.5rem) !important;
    overflow-y: auto;
  }
}
</style>
