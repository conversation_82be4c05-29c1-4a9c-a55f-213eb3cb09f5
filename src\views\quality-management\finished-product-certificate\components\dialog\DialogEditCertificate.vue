<template>
  <el-dialog
    v-model="dialogShow"
    top="5vh"
    title="编辑"
    width="950px"
    :close-on-click-modal="false"
    @close="cancelDialog()"
  >
    <el-form
      ref="ruleForm"
      v-loading="dialogLoading"
      :model="formData"
      label-position="right"
      class="form-class overflow-y-auto pr-2"
    >
      <el-row>
        <el-col v-for="field in pageViewAll" :key="field.fieldKey" :span="12">
          <el-form-item
            :label="`${field.fieldName}：`"
            :label-width="`${labelWidth[field.fieldKey] || 110}px`"
            :prop="field.fieldKey"
            :rules="{
              required: false,
              message: `请输入${field.fieldName}`,
              trigger: 'change'
            }"
          >
            <el-input
              v-if="editFieldAll[field.fieldKey]?.type == 'text'"
              v-model="formData[field.fieldKey]"
              clearable
              :placeholder="`请输入${field.fieldName}`"
            />
            <el-row v-if="editFieldAll[field.fieldKey]?.type == 'number'" :gutter="10">
              <el-col :span="16">
                <el-input-number
                  v-model="formData[field.fieldKey]"
                  :min="0"
                  controls-position="right"
                  :placeholder="`请输入${field.fieldName}`"
                  style="width: 100%"
                />
              </el-col>
              <el-col :span="8" class="text-center">
                <el-select
                  v-model="formData.inputWarehouseUnit"
                  filterable
                  size="small"
                  disabled
                  :placeholder="`请选择`"
                  class="w-full"
                >
                  <el-option v-for="(val, key) in dictionaryAll['5']?.enable" :key="key" :label="val" :value="key" />
                </el-select>
              </el-col>
            </el-row>
            <el-select
              v-if="editFieldAll[field.fieldKey]?.type == 'select'"
              v-model="formData[field.fieldKey]"
              filterable
              size="small"
              clearable
              :placeholder="`请选择${field.fieldName}`"
              class="w-full"
            >
              <el-option
                v-for="(val, key) in dictionaryAll[editFieldAll[field.fieldKey].code]?.enable"
                :key="key"
                :label="val"
                :value="key"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row class="items-baseline">
        <el-col :span="3">小盘起始序列号：</el-col>
        <el-col :span="5">
          <el-form-item
            label=""
            label-width="0"
            prop="startNumber"
            :rules="{
              required: formData.endNumber || formData.endNumber == 0,
              message: '请输入起始序号',
              trigger: 'change'
            }"
          >
            <el-input-number
              v-model="formData.startNumber"
              controls-position="right"
              :max="formData.endNumber"
              :min="1"
              placeholder="起"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="1" class="text-center"> ~ </el-col>
        <el-col :span="5">
          <el-form-item label="" label-width="0" prop="endNumber">
            <el-input-number
              v-model="formData.endNumber"
              controls-position="right"
              :min="formData.startNumber"
              placeholder="止"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row v-for="(item, index) in formData.smallSerialNumber" :key="index" class="items-center mb-1">
        <el-col :span="5" :offset="3">
          <el-input v-model="item[0]" disabled placeholder="起" size="small" />
        </el-col>
        <el-col :span="1" class="text-center"> ~ </el-col>
        <el-col :span="5">
          <el-input v-model="item[1]" disabled placeholder="止" size="small" />
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button :loading="dialogLoading" @click="cancelDialog()">取消</el-button>
      <el-button :loading="dialogLoading" type="primary" @click="handleSubmit()">确定</el-button>
    </template>
  </el-dialog>
</template>
<script>
import { reactive, watch, ref, toRefs } from 'vue';
// import UserTag from '@/components/UserTag';
import { saveCertificatePrint } from '@/api/certificate-export';
import { getNameByid } from '@/utils/common';
import { formatDate } from '@/utils/formatTime';
import { ElMessage } from 'element-plus';
// import { certificateDecide } from '@/api/raw-certificate';
export default {
  name: 'DialogEditCertificate',
  components: {},
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    pageView: {
      type: Object,
      default: () => ({})
    },
    selectRow: {
      type: Array,
      default: () => []
    },
    dictionary: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['closeDialog'],
  setup(props, context) {
    const state = reactive({
      formData: {
        smallSerialNumber: []
      },
      dictionaryAll: {
        JHDW: {
          all: {},
          enable: {}
        },
        5: {
          all: {},
          enable: {}
        }
      },
      pageViewAll: {},
      dialogLoading: false,
      labelWidth: {
        allowedOperatingTemperature: 180,
        suppDate: 150,
        reelDesc: 150
      },

      editFieldAll: {
        projectName: { type: 'text' },
        serialNumber: { type: 'text' },
        inboundLength: { type: 'number' },
        customerQuantity: { type: 'number' },
        customerModel: { type: 'text' },
        customerSpecifications: { type: 'text' },
        customerVoltage: { type: 'text' },
        customerBarcodeNumber: { type: 'text' },
        allowedOperatingTemperature: { type: 'text' },
        receivingInstitution: { type: 'text' },
        grossWeight: { type: 'text' },
        meterSpan: { type: 'text' },
        reelNo: { type: 'text' },
        mateName: { type: 'text' },
        remark: { type: 'text' },
        judgmentName: { type: 'text' }
      },
      reportType: 1, // 0 合格， 1 不合格
      progress: 0,
      dialogShow: false,
      ruleForm: ref()
    });

    const cancelDialog = value => {
      state.dialogShow = false;
      context.emit('closeDialog', value);
    };

    const submitJudgement = () => {};

    watch(
      () => props.dialogVisible,
      newValue => {
        state.dialogShow = newValue;
        if (newValue) {
          state.formData = props.selectRow[0] || { smallSerialNumber: [] };
          state.formData.inboundLength = Number(state.formData.inboundLength);
          state.formData.customerQuantity = Number(state.formData.customerQuantity);
          state.pageViewAll = props.pageView.filter(item => state.editFieldAll[item.fieldKey]);
          state.dictionaryAll = props.dictionary || {
            JHDW: {
              all: {},
              enable: {}
            }
          };
        }
      }
    );
    const handleSubmit = async () => {
      state.ruleForm.validate().then(async valid => {
        if (valid) {
          const params = {
            ...state.selectRow,
            ...JSON.parse(JSON.stringify(state.formData))
          };
          if (state.formData.startNumber && !state.formData.endNumber) {
            params.smallSerialNumber.push([Number(state.formData.startNumber), Number(state.formData.startNumber)]);
          } else if (state.formData.startNumber) {
            params.smallSerialNumber.push([Number(state.formData.startNumber), Number(state.formData.endNumber)]);
          }
          const { data } = await saveCertificatePrint({ entityList: [params] }).finally((state.dialogLoading = false));
          if (data) {
            ElMessage.success('编辑成功!');
            cancelDialog(true);
          }
        }
      });
    };

    return {
      ...toRefs(state),
      cancelDialog,
      handleSubmit,
      getNameByid,
      formatDate,
      submitJudgement
    };
  }
};
</script>
<style lang="scss" scoped>
.form-class {
  max-height: 600px;
}
</style>
