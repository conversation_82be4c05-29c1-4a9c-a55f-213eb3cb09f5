<template>
  <div v-if="isShow" class="modelTable">
    <!-- 产品的表格 -->
    <el-table
      ref="tableRef"
      v-loading="tableLoading"
      :data="tableData"
      fit
      border
      height="auto"
      size="medium"
      highlight-current-row
      class="dark-table format-height-table base-table format-height-table2"
      @header-dragend="drageHeader"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" :width="colWidth.checkbox" align="center" />
      <el-table-column label="序号" :width="colWidth.serialNo" align="center">
        <template #default="{ $index }">
          {{ $index + 1 }}
        </template>
      </el-table-column>
      <el-table-column label="产品名称" prop="productName" :min-width="colWidth.name" show-overflow-tooltip>
        <template #default="{ row }">
          <span class="blue-color" @click="handleDetail(row, false)">{{ row.productName || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="更新时间" prop="lastUpdateDateTime" :width="colWidth.datetime">
        <template #default="{ row }">
          {{ row.lastUpdateDateTime || '' }}
        </template>
      </el-table-column>
      <el-table-column label="更新人" prop="lastUpdateByuserId" :width="colWidth.person">
        <template #default="{ row }">
          <UserTag :name="getNameByid(row.lastUpdateByuserId) || row.lastUpdateByuserId || '--'" />
        </template>
      </el-table-column>
      <el-table-column label="版本" prop="version" :width="colWidth.status">
        <template #default="{ row }">
          <div v-if="row.version">V{{ row.version }}<span v-if="row.versionStatus === 0">（草稿）</span></div>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" prop="status" :width="colWidth.status">
        <template #default="{ row }">
          <el-tag size="small" effect="dark" :type="statusType[row.status]">{{
            statusJson[row.status] || '--'
          }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        v-if="
          getPermissionBtn('gistEdit') ||
          getPermissionBtn('gistStatusChange') ||
          getPermissionBtn('gistDelete') ||
          getPermissionBtn('gistCopy')
        "
        label="操作"
        :min-width="colWidth.operation"
        fixed="right"
        class-name="fixed-right"
      >
        <template #default="{ row }">
          <!-- 0，草稿1、生效2、停用 -->
          <span v-if="row.status != '2' && getPermissionBtn('gistEdit')" class="blue-color" @click="handleProduct(row)"
            >编辑规格</span
          >
          <span
            v-if="row.status != '2' && getPermissionBtn('gistEdit')"
            class="blue-color"
            @click="handleDetail(row, true)"
            >编辑项目</span
          >
          <span
            v-if="row.status == '2' && getPermissionBtn('gistStatusChange')"
            class="blue-color"
            @click="handleOpera(row, '1')"
            >启用</span
          >
          <span
            v-if="row.status == '1' && getPermissionBtn('gistStatusChange')"
            class="blue-color"
            @click="handleOpera(row, '2')"
            >停用</span
          >
          <span
            v-if="row.status == '0' && getPermissionBtn('gistDelete')"
            class="blue-color"
            @click="handleOpera(row, '3')"
            >删除</span
          >
          <span
            v-if="row.status !== '0' && getPermissionBtn('gistCopy')"
            class="blue-color"
            @click="handleOpera(row, '4')"
            >复制</span
          >
        </template>
      </el-table-column>
    </el-table>
    <AddEditSpecifications
      :is-show="isShowProduct"
      :dialog-type="dialogType"
      :specify-list="specifyListData"
      :detail-data="detailData"
      @closeDialog="handleAddDialog"
    />
    <Pagination :page="page" :limit="limit" :total="total" @pagination="getTableList" />
  </div>
</template>

<script>
import { reactive, toRefs, ref, watch, getCurrentInstance } from 'vue';
import { formatDate } from '@/utils/formatTime';
import router from '@/router/index.js';
import UserTag from '@/components/UserTag';
import Pagination from '@/components/Pagination';
import { enableProduct, deleteProductList, getProductList } from '@/api/testBase';
import { drageHeader } from '@/utils/formatTable';
import AddEditSpecifications from './addEditSpecifications';
import { getNameByid, getPermissionBtn } from '@/utils/common';
import { colWidth } from '@/data/tableStyle';
export default {
  name: 'ProductTable',
  components: { AddEditSpecifications, Pagination, UserTag },
  props: {
    isShow: {
      type: Boolean,
      default: false
    },
    searchParams: {
      type: String,
      default: ''
    },
    detailData: {
      type: Object,
      default: function () {
        return {};
      }
    },
    specifyList: {
      type: Array,
      default: function () {
        return [];
      }
    },
    standardCategoryId: {
      type: String,
      default: ''
    }
  },
  setup(props, context) {
    const { proxy } = getCurrentInstance();
    watch(props, newValue => {
      state.isShow = props.isShow;
      if (state.isShow) {
        state.detailData = props.detailData;
        state.searchParams = newValue.searchParams;
        state.specifyListData = props.specifyList;
        getTableList();
      }
      // state.tableData = props.tableData
    });
    const state = reactive({
      tableData: [],
      isEdit: false, // 是否是编辑
      dialogType: '',
      selectRow: [],
      statusJson: {
        0: '草稿',
        1: '生效',
        2: '停用'
      },
      statusType: {
        0: 'info',
        1: 'success',
        2: 'danger'
      },
      detailData: {},
      detailProduct: {},
      productJson: {},
      materialCode: '',
      standardCategoryId: '',
      dialogTreeData: {},
      checkTreeNode: {},
      copyData: {},
      searchParams: '', // 查询的参数
      specifyListData: [], // 规格列表
      tableRef: ref(),
      tableLoading: false,
      page: 1,
      limit: 20,
      isShow: false,
      isShowProduct: false, // 是否显示规格弹出框
      productType: '', // 规格弹出框类型
      total: 0
    });
    const getTableList = query => {
      var params = {
        standardCategoryId: state.detailData.checkTreeNode.id,
        materialCategoryCode: state.detailData.materialCategoryCode,
        param: state.searchParams,
        isReleased: false
      };
      if (query && query.page) {
        params.page = query.page.toString();
        params.limit = query.limit.toString();
      } else {
        params.page = state.page.toString();
        params.limit = state.limit.toString();
      }
      state.tableLoading = true;
      getProductList(params).then(res => {
        state.tableLoading = false;
        if (res) {
          state.tableData = res.data.data.list;
          state.total = res.data.data.totalCount;
        }
      });
    };
    // 关闭产品详情抽屉
    const closeDeatil = val => {
      getTableList();
    };
    // 编辑、查看详情
    const handleDetail = (row, isEdit) => {
      router.push({
        path: '/testBase/detail',
        query: {
          isEdit: isEdit,
          productId: row.id,
          checkTreeId: state.detailData.checkTreeNode.id,
          productName: row.productName,
          versionId: row.versionId,
          materialCategoryCode: state.detailData.materialCategoryCode,
          specifications: row.specifications
        }
      });
      // 带给详情页的产品信息
    };
    // 编辑产品
    const handleProduct = row => {
      state.isShowProduct = true;
      state.detailData.productDetail = JSON.parse(JSON.stringify(row));
      state.dialogType = 'edit';
    };
    const handleAddDialog = val => {
      state.isShowProduct = val.dialogVisible;
      if (val.isRefresh) {
        getTableList();
      }
    };
    // 停用(2)、启用(1)、删除(3)、复制(4)
    const handleOpera = (row, type) => {
      const params = {
        id: row.id,
        isDeleted: false,
        status: Number(type)
      };
      if (type === '1') {
        enableProduct(params).then(res => {
          if (res) {
            proxy.$message.success('启用成功');
            getTableList();
          }
        });
      } else if (type === '2' || type === '3') {
        const confirmTitle = {
          2: { label1: '当前标准已生效，是否确认停用？', label2: '停用成功', label3: '确认停用' },
          3: { label1: '是否删除？删除后将不能还原。删除确认', label2: '删除成功', label3: '确认删除' }
        };
        proxy
          .$confirm(confirmTitle[type].label1, confirmTitle[type].label3, {
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            type: 'warning'
          })
          .then(() => {
            if (type === '2') {
              // 停用产品
              enableProduct(params).then(res => {
                if (res) {
                  proxy.$message.success(confirmTitle[type].label2);
                  getTableList();
                }
              });
            } else {
              // 删除产品
              deleteProductList([row.id]).then(res => {
                if (res) {
                  proxy.$message.success(confirmTitle[type].label2);
                  getTableList();
                }
              });
            }
          })
          .catch(() => {});
      } else {
        state.isShowProduct = true;
        state.dialogType = 'copy'; // 弹出框类型
        state.detailData.productDetail = row;
      }
    };
    const handleSelectionChange = val => {
      state.selectRow = val;
    };
    const getSelectRow = () => {
      return state.selectRow;
    };
    return {
      ...toRefs(state),
      colWidth,
      handleSelectionChange,
      getSelectRow,
      formatDate,
      closeDeatil,
      handleDetail,
      handleOpera,
      handleProduct,
      getNameByid,
      handleAddDialog,
      getPermissionBtn,
      getTableList,
      drageHeader
    };
  },
  computed: {},
  created() {}
};
</script>

<style lang="scss" scoped>
.modelTable {
  margin-top: 10px;
}
.el-dropdown-link {
  margin-right: 10px;
}
:deep(.el-table.format-height-table2) {
  .el-table__body-wrapper {
    max-height: calc(100vh - 300px);
    overflow-y: auto;
    overflow-x: hidden;
  }
}
</style>
